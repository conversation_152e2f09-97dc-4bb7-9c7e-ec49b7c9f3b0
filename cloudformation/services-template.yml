AWSTemplateFormatVersion: 2010-09-09
Transform: "AWS::Serverless-2016-10-31"
Description: Infrastructure for SQL Copilot Backend

Parameters:
  Env:
    Description: The environment (dev, test, prod etc.) Used as resource names and tags
    Type: String
  ProjectId:
    Description: The unique id or name of this project. Used in resource names and tags
    Type: String
    Default: sql-copilot
  BackendImage:
    Type: String
    Description: The full image:tag for the image to push to the task
  DefaultDesiredTaskCount:
    Type: Number
    Description: The number of ecs tasks to run by default (before scaling kicks in)
    Default: 1
  ACMCertificateArn:
    Type: String
    Description: The ARN for the ACM certificate for the domain name we are using for the ALB.
  ParameterStoreNamePrefix:
    Type: String
    Description: The prefix for the parameter store.
    Default: sql-copilot
  # SecretsArn:
  #   Type: String
  #   Description: Secrets ARN 
    

Resources:
  FargateExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: [ecs-tasks.amazonaws.com]
            Action: [sts:AssumeRole]
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: TaskRolePolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: "logs:CreateLog*"
                Resource: "*"
              - Effect: Allow
                Action:
                  - "ssmmessages:CreateControlChannel"
                  - "ssmmessages:CreateDataChannel"
                  - "ssmmessages:OpenControlChannel"
                  - "ssmmessages:OpenDataChannel"
                  - "ssm:GetParameters"
                Resource: "*"
              - Effect: Allow
                Action: "secretsmanager:*"
                Resource: "*"
              - Effect: Allow
                Action: "kms:Decrypt"
                Resource: "*"

  FargateTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: [ecs-tasks.amazonaws.com]
            Action: [sts:AssumeRole]
      Policies:
        - PolicyName: TaskRolePolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: "logs:CreateLogStream"
                Resource: "*"
              - Effect: Allow
                Action: "logs:PutLogEvents"
                Resource: "*"
              - Effect: Allow
                Action: "ssm:*"
                Resource: "*"
              - Effect: Allow
                Action: "secretsmanager:*"
                Resource: "*"
              - Effect: Allow
                Action:
                  - "ssmmessages:CreateControlChannel"
                  - "ssmmessages:CreateDataChannel"
                  - "ssmmessages:OpenControlChannel"
                  - "ssmmessages:OpenDataChannel"
                Resource: "*"
              - Effect: Allow
                Action: "kms:Decrypt"
                Resource: "*"

  FargateCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub "${ProjectId}-${Env}"
      CapacityProviders:
        - FARGATE
      ServiceConnectDefaults:
        Namespace: !Sub "${ProjectId}-${Env}"

  BackendTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub backend-${Env}
      Cpu: "1024"             # You can specify "256", "512", "1024", etc.
      Memory: "2048"          # You can specify "512", "1024", "2048", etc.
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt FargateExecutionRole.Arn
      TaskRoleArn: !GetAtt FargateTaskRole.Arn
      ContainerDefinitions:
        - Name: !Sub "${ProjectId}-${Env}"
          Image: !Ref BackendImage
          Essential: true
          PortMappings:
            - ContainerPort: 5045
              Name: api
          HealthCheck:
            Command:
              - "CMD-SHELL"
              - "echo 'success' || exit 1"
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 60
          Environment:
            - Name: APP_ENV
              Value: !Ref Env
          Secrets:
            - Name: DATABASE
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/DATABASE"
            - Name: OPENAI_API_KEY
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/OPENAI_API_KEY"
            - Name: ROLE
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/ROLE"
            - Name: SNOWFLAKE_ACCOUNT
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SNOWFLAKE_ACCOUNT"
            - Name: SNOWFLAKE_PASSWORD
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SNOWFLAKE_PASSWORD"
            - Name: SNOWFLAKE_SCHEMA
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SNOWFLAKE_SCHEMA"
            - Name: SNOWFLAKE_USER
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SNOWFLAKE_USER"
            - Name: SNOWFLAKE_WAREHOUSE
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SNOWFLAKE_WAREHOUSE"
            - Name: APP_USERNAME
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/APP_USERNAME"
            - Name: APP_PASSWORD
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/APP_PASSWORD"
            - Name: SLACK_BOT_TOKEN
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SLACK_BOT_TOKEN"
            - Name: SLACK_APP_TOKEN
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/SLACK_APP_TOKEN"
            - Name: DOC_USERNAME
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/DOC_USERNAME"
            - Name: DOC_PASSWORD
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/DOC_PASSWORD"
            - Name: ANTHROPIC_API_KEY
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/ANTHROPIC_API_KEY"
            - Name: ANTHROPIC_API_KEY_2
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/ANTHROPIC_API_KEY_2"
            - Name: REDIS_URL
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ParameterStoreNamePrefix}/${Env}/REDIS_URL"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-create-group: true
              awslogs-group: !Sub /ecs/${ProjectId}-${Env}/backend
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: ecs
          LinuxParameters:
            InitProcessEnabled: true
          
  BackEndService:
    Type: AWS::ECS::Service
    DependsOn:
      - FargateCluster
      - CopilotALB
      - TLSListener
    UpdatePolicy:
      AutoScalingRollingUpdate:
        MinInstancesInService: 1
        MaxBatchSize: 1
        PauseTime: PT0S
    Properties:
      Cluster: !Ref FargateCluster
      ServiceName: !Sub "${ProjectId}-${Env}-backend"
      TaskDefinition: !Ref BackendTaskDefinition
      DesiredCount: !Ref DefaultDesiredTaskCount
      LaunchType: FARGATE
      HealthCheckGracePeriodSeconds: 60
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      LoadBalancers:
        - ContainerName: !Sub ${ProjectId}-${Env}
          ContainerPort: 5045
          TargetGroupArn: !Ref BackEndTargetGroup
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets:
            - "{{resolve:ssm:/devops/private-subnet-1}}"
            - "{{resolve:ssm:/devops/private-subnet-2}}"
            - "{{resolve:ssm:/devops/private-subnet-3}}"
          SecurityGroups:
            - !Ref FargateServiceSecurityGroup

  FargateServiceSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Access to the Fargate services
      VpcId: "{{resolve:ssm:/devops/vpc-id}}"
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 5045
          ToPort: 5045
          SourceSecurityGroupId: !Ref ALBSecurityGroup
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 0
          ToPort: 65535
          CidrIp: 0.0.0.0/0

  CopilotALB:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${ProjectId}-${Env}-alb
      Scheme: internet-facing
      Subnets:
          - "{{resolve:ssm:/devops/public-subnet-1}}"
          - "{{resolve:ssm:/devops/public-subnet-2}}"
          - "{{resolve:ssm:/devops/public-subnet-3}}"
      SecurityGroups:
        - !Ref ALBSecurityGroup

  BackEndTargetGroup:
    DependsOn:
      - CopilotALB
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Port: 5045
      Protocol: HTTP
      VpcId: "{{resolve:ssm:/devops/vpc-id}}"
      TargetType: ip
      # HealthCheckIntervalSeconds: 60
      HealthCheckPath: "/health"
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 10
      HealthyThresholdCount: 3
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200

  HTTPListener:
    Type: 'AWS::ElasticLoadBalancingV2::Listener'
    Properties:
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: '443'
            Host: '#{host}'
            Path: '/#{path}'
            Query: '#{query}'
            StatusCode: HTTP_301
      LoadBalancerArn:
        Ref: CopilotALB
      Port: 80
      Protocol: HTTP

  TLSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref BackEndTargetGroup
      LoadBalancerArn: !Ref CopilotALB
      Port: 443
      Protocol: HTTPS
      SslPolicy: ELBSecurityPolicy-2016-08
      Certificates:
        - CertificateArn: !Ref ACMCertificateArn

  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Access to the Fargate services
      VpcId: "{{resolve:ssm:/devops/vpc-id}}"
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 0
          ToPort: 65535
          CidrIp: 0.0.0.0/0

Outputs:
  ALBDomainName:
    Description: The DNS name of the ALB
    Value: !GetAtt CopilotALB.DNSName
    Export:
      Name: !Sub '${AWS::StackName}-AlbDns'
  ALBArn:
    Description: The ARN of our ALB
    Value: !Ref CopilotALB
    Export:
      Name: !Sub '${AWS::StackName}-AlbArn'
