AWSTemplateFormatVersion: 2010-09-09
Description: 'Create a CNAME record for a domain name'
Parameters:
  TargetDomainName:
    Description: The domain name to be used for the cname record
    Type: String
  DomainName:
    Type: String
    Description: The domain name
  ProjectId:
    Description: The unique id or name of this project. Used in resource names and tags
    Type: String
  HostedZoneId:
    Type: String
Resources:
  MyRoute53RecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !Ref HostedZoneId
      Name: !Ref DomainName
      Type: CNAME
      TTL: 300
      ResourceRecords:
        - !Ref TargetDomainName
