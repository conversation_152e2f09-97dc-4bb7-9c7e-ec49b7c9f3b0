#!/usr/bin/env python3
"""
Database clearing script
Clears all data from all tables in the database
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.databse import clear_database, test_db_connection

def main():
    """Clear the database"""
    print("Testing database connection...")
    
    if not test_db_connection():
        print("❌ Database connection failed!")
        print("Please check your database configuration in config.py")
        sys.exit(1)
    
    print("✅ Database connection successful!")
    
    # Ask for confirmation
    response = input("⚠️  WARNING: This will delete ALL data from the database!\nAre you sure you want to continue? (yes/no): ")
    
    if response.lower() not in ['yes', 'y']:
        print("❌ Operation cancelled.")
        sys.exit(0)
    
    print("Clearing database...")
    try:
        if clear_database():
            print("✅ Database cleared successfully!")
        else:
            print("❌ Failed to clear database!")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 