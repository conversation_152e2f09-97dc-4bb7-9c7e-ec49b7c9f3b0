#!/usr/bin/env python3
"""
Database management script
Provides options to initialize, clear, and manage the database
"""

import sys
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.databse import init_db, clear_database, clear_table, test_db_connection
from app.models import Base

def main():
    """Main database management function"""
    parser = argparse.ArgumentParser(description='Database management tool')
    parser.add_argument('action', choices=['init', 'clear', 'clear-table', 'test'], 
                       help='Action to perform')
    parser.add_argument('--table', type=str, help='Table name for clear-table action')
    parser.add_argument('--force', action='store_true', help='Skip confirmation prompts')
    
    args = parser.parse_args()
    
    # Test database connection first
    print("Testing database connection...")
    if not test_db_connection():
        print("❌ Database connection failed!")
        print("Please check your database configuration in config.py")
        sys.exit(1)
    
    print("✅ Database connection successful!")
    
    if args.action == 'test':
        print("✅ Database connection test passed!")
        return
    
    elif args.action == 'init':
        print("Creating database tables...")
        try:
            init_db()
            print("✅ Database tables created successfully!")
        except Exception as e:
            print(f"❌ Error creating tables: {e}")
            sys.exit(1)
    
    elif args.action == 'clear':
        if not args.force:
            response = input("⚠️  WARNING: This will delete ALL data from the database!\nAre you sure you want to continue? (yes/no): ")
            if response.lower() not in ['yes', 'y']:
                print("❌ Operation cancelled.")
                return
        
        print("Clearing database...")
        try:
            if clear_database():
                print("✅ Database cleared successfully!")
            else:
                print("❌ Failed to clear database!")
                sys.exit(1)
        except Exception as e:
            print(f"❌ Error clearing database: {e}")
            sys.exit(1)
    
    elif args.action == 'clear-table':
        if not args.table:
            print("❌ Error: --table argument is required for clear-table action")
            sys.exit(1)
        
        if not args.force:
            response = input(f"⚠️  WARNING: This will delete ALL data from table '{args.table}'!\nAre you sure you want to continue? (yes/no): ")
            if response.lower() not in ['yes', 'y']:
                print("❌ Operation cancelled.")
                return
        
        print(f"Clearing table: {args.table}")
        try:
            if clear_table(args.table):
                print(f"✅ Table '{args.table}' cleared successfully!")
            else:
                print(f"❌ Failed to clear table '{args.table}'!")
                sys.exit(1)
        except Exception as e:
            print(f"❌ Error clearing table: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main() 