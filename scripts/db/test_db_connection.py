#!/usr/bin/env python3
"""
Test script to verify database connectivity
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.databse import test_db_connection, init_db
from config import Config
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database():
    """Test database connection and initialization"""
    print("=" * 50)
    print("DATABASE CONNECTION TEST")
    print("=" * 50)
    
    # Print database configuration
    print(f"Database Host: {Config.DB_HOST}")
    print(f"Database Port: {Config.DB_PORT}")
    print(f"Database Name: {Config.DB_NAME}")
    print(f"Database User: {Config.DB_USER}")
    print(f"Database URL: {Config.DATABASE_URL}")
    print("-" * 50)
    
    try:
        # Test connection
        print("Testing database connection...")
        if test_db_connection():
            print("✅ Database connection successful!")
        else:
            print("❌ Database connection failed!")
            return False
        
        # Test table initialization
        print("Testing database table initialization...")
        init_db()
        print("✅ Database tables initialized successfully!")
        
        print("=" * 50)
        print("✅ All database tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        logger.error(f"Database test error: {e}")
        return False

if __name__ == "__main__":
    success = test_database()
    sys.exit(0 if success else 1) 