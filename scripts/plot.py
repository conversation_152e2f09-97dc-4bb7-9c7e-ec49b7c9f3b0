

import matplotlib.pyplot as plt
import pandas as pd
import io
from contextlib import redirect_stdout
import ast

def execute_plotting_code(code_text, data):
    """
    Safely executes AI-generated plotting code with provided data
    
    Parameters:
    code_text (str): The Python code as text that defines the plotting function
    data (list): The data to be plotted
    
    Returns:
    function: The extracted plotting function that can be called with data
    """
    # Create a new namespace to execute the code
    namespace = {}
    
    try:
        # First validate the code structure
        ast.parse(code_text)
        
        # Execute the code in the isolated namespace
        exec(code_text, namespace)
        
        # Get the plotting function from namespace
        if 'plot_data' in namespace:
            plot_func = namespace['plot_data']
            return plot_func
        else:
            raise ValueError("Could not find the plotting function in the provided code")
            
    except SyntaxError as e:
        print(f"Syntax error in the provided code: {e}")
        return None
    except Exception as e:
        print(f"Error executing the code: {e}")
        return None

def generate_plot(code_text, data):
    """
    Wrapper function to generate plot from AI code and data
    
    Parameters:
    code_text (str): The Python code as text
    data (list): The data to be plotted
    """
    # Get the plotting function
    plot_func = execute_plotting_code(code_text, data)
    
    if plot_func:
        try:
            # Execute the plotting function with the provided data
            res = plot_func(data)
            return res
        except Exception as e:
            print(f"Error generating plot: {e}")
            
