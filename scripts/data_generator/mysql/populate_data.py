import mysql.connector
from faker import Faker
import random
from datetime import datetime, timedelta
import json
import uuid
import time

# Database connection parameters
from test_keys import *

# Initialize Faker
fake = Faker()

def create_connection():
    """Create a new database connection"""
    return mysql.connector.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        port=MYSQL_PORT,
        autocommit=False,
        use_unicode=True,
        charset='utf8mb4'
    )

def generate_phone():
    # Generate a phone number that fits within 20 characters
    return f"+{random.randint(1, 999)}-{random.randint(100, 999)}-{random.randint(1000, 9999)}"

def generate_unique_email():
    # Generate a unique email by adding timestamp and random number
    timestamp = int(time.time() * 1000) % 1000000  # last 6 digits of timestamp
    random_num = random.randint(1000, 9999)
    base_email = fake.email()
    username, domain = base_email.split('@')
    return f"{username}_{timestamp}_{random_num}@{domain}"

def generate_unique_id(prefix, length=6, suffix=""):
    """Generate a unique ID with timestamp to avoid duplicates"""
    timestamp = int(time.time() * 1000000) % (10 ** length)  # Use microseconds for better uniqueness
    if suffix:
        return f"{prefix}-{timestamp:0{length}d}-{suffix}"
    return f"{prefix}-{timestamp:0{length}d}"

def generate_customers(conn, num_records=1000):
    print(f"Generating {num_records} customers...")
    cur = conn.cursor(dictionary=True)
    try:
        for i in range(num_records):
            cur.execute("""
                INSERT INTO ecommerce.customers 
                (first_name, last_name, email, phone, address, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                fake.first_name()[:50],  # Limit to 50 chars
                fake.last_name()[:50],   # Limit to 50 chars
                generate_unique_email()[:100],  # Limit to 100 chars
                generate_phone(),  # Already limited to 20 chars
                fake.address()[:200],  # Limit to 200 chars
                fake.date_time_this_year()
            ))
            
            # Add delay every 100 records to prevent connection issues
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} customers...")
                time.sleep(0.1)  # Small delay
        
        conn.commit()
        print(f"Successfully generated {num_records} customers!")
    except Exception as e:
        print(f"Error generating customers: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_categories(conn, num_records=50):
    print(f"Generating {num_records} categories...")
    cur = conn.cursor(dictionary=True)
    categories = []
    try:
        for i in range(num_records):
            cur.execute("""
                INSERT INTO ecommerce.categories 
                (name, description, parent_category_id)
                VALUES (%s, %s, %s)
            """, (
                fake.word().capitalize()[:50],  # Limit to 50 chars
                fake.sentence()[:200],  # Limit to 200 chars
                random.choice([None] + categories) if categories else None
            ))
            categories.append(cur.lastrowid)
            
            # Add delay every 10 records
            if (i + 1) % 10 == 0:
                conn.commit()
                print(f"Inserted {i + 1} categories...")
                time.sleep(0.05)
        
        conn.commit()
        print(f"Successfully generated {num_records} categories!")
        return categories
    except Exception as e:
        print(f"Error generating categories: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_products(conn, num_records=1000, categories=None):
    print(f"Generating {num_records} products...")
    cur = conn.cursor(dictionary=True)
    products = []
    try:
        for i in range(num_records):
            cur.execute("""
                INSERT INTO ecommerce.products 
                (name, description, price, stock_quantity, category_id, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                fake.catch_phrase()[:100],  # Limit to 100 chars
                fake.text(max_nb_chars=200),  # Already limited to 200 chars
                round(random.uniform(10.0, 1000.0), 2),
                random.randint(0, 1000),
                random.choice(categories) if categories else None,
                fake.date_time_this_year()
            ))
            products.append(cur.lastrowid)
            
            # Add delay every 100 records
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} products...")
                time.sleep(0.1)
        
        conn.commit()
        print(f"Successfully generated {num_records} products!")
        return products
    except Exception as e:
        print(f"Error generating products: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_orders(conn, num_records=1000, customers=None, products=None):
    print(f"Generating {num_records} orders...")
    cur = conn.cursor(dictionary=True)
    orders = []
    try:
        for i in range(num_records):
            customer_id = random.choice(customers)
            cur.execute("""
                INSERT INTO ecommerce.orders 
                (customer_id, order_date, total_amount, status, shipping_address)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                customer_id,
                fake.date_time_this_year(),
                round(random.uniform(50.0, 2000.0), 2),
                random.choice(['pending', 'processing', 'shipped', 'delivered', 'cancelled'])[:20],
                fake.address()[:200]
            ))
            order_id = cur.lastrowid
            orders.append(order_id)
            
            # Generate 1-5 items per order
            num_items = random.randint(1, 5)
            for _ in range(num_items):
                product_id = random.choice(products)
                quantity = random.randint(1, 5)
                cur.execute("""
                    INSERT INTO ecommerce.order_items 
                    (order_id, product_id, quantity, unit_price)
                    VALUES (%s, %s, %s, %s)
                """, (
                    order_id,
                    product_id,
                    quantity,
                    round(random.uniform(10.0, 500.0), 2)
                ))
            
            # Add delay every 50 orders
            if (i + 1) % 50 == 0:
                conn.commit()
                print(f"Inserted {i + 1} orders...")
                time.sleep(0.1)
        
        conn.commit()
        print(f"Successfully generated {num_records} orders!")
        return orders
    except Exception as e:
        print(f"Error generating orders: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_reviews(conn, num_records=1000, customers=None, products=None):
    print(f"Generating {num_records} reviews...")
    cur = conn.cursor(dictionary=True)
    try:
        for i in range(num_records):
            cur.execute("""
                INSERT INTO ecommerce.reviews 
                (product_id, customer_id, rating, comment, created_at)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                random.choice(products),
                random.choice(customers),
                random.randint(1, 5),
                fake.text(max_nb_chars=200),
                fake.date_time_this_year()
            ))
            
            # Add delay every 100 records
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} reviews...")
                time.sleep(0.1)
        
        conn.commit()
        print(f"Successfully generated {num_records} reviews!")
    except Exception as e:
        print(f"Error generating reviews: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_cart_data(conn, num_records=1000, customers=None, products=None):
    print(f"Generating {num_records} cart records...")
    cur = conn.cursor(dictionary=True)
    try:
        for i in range(num_records):
            customer_id = random.choice(customers)
            cur.execute("""
                INSERT INTO ecommerce.cart 
                (customer_id, created_at)
                VALUES (%s, %s)
            """, (
                customer_id,
                fake.date_time_this_year()
            ))
            cart_id = cur.lastrowid
            
            # Generate 1-5 items per cart
            num_items = random.randint(1, 5)
            for _ in range(num_items):
                cur.execute("""
                    INSERT INTO ecommerce.cart_items 
                    (cart_id, product_id, quantity)
                    VALUES (%s, %s, %s)
                """, (
                    cart_id,
                    random.choice(products),
                    random.randint(1, 5)
                ))
            
            # Add delay every 100 records
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} cart records...")
                time.sleep(0.1)
        
        conn.commit()
        print(f"Successfully generated {num_records} cart records!")
    except Exception as e:
        print(f"Error generating cart data: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_promotions(conn, num_records=100):
    print(f"Generating {num_records} promotions...")
    cur = conn.cursor(dictionary=True)
    promotions = []
    try:
        for i in range(num_records):
            start_date = fake.date_time_this_year()
            end_date = start_date + timedelta(days=random.randint(7, 30))
            cur.execute("""
                INSERT INTO ecommerce.promotions 
                (name, description, discount_percentage, start_date, end_date)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                fake.catch_phrase()[:100],
                fake.text(max_nb_chars=200),
                round(random.uniform(5.0, 50.0), 2),
                start_date,
                end_date
            ))
            promotions.append(cur.lastrowid)
            
            # Add delay every 25 records
            if (i + 1) % 25 == 0:
                conn.commit()
                print(f"Inserted {i + 1} promotions...")
                time.sleep(0.05)
        
        conn.commit()
        print(f"Successfully generated {num_records} promotions!")
        return promotions
    except Exception as e:
        print(f"Error generating promotions: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_finance_data(conn, num_records=1000, customers=None):
    print(f"Generating finance data for {num_records} records...")
    cur = conn.cursor(dictionary=True)
    accounts = []
    
    try:
        # Generate accounts
        print("Generating accounts...")
        for i in range(num_records):
            # Generate unique account number using timestamp + counter
            account_number = generate_unique_id("ACC", 6, f"{i:04d}")
            
            cur.execute("""
                INSERT INTO finance.accounts 
                (account_number, account_type, balance, created_at)
                VALUES (%s, %s, %s, %s)
            """, (
                account_number,
                random.choice(['checking', 'savings', 'investment'])[:50],
                round(random.uniform(100.0, 10000.0), 2),
                fake.date_time_this_year()
            ))
            accounts.append(cur.lastrowid)
            
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} accounts...")
                time.sleep(0.1)
        
        conn.commit()
        
        # Generate transactions
        print("Generating transactions...")
        for i in range(num_records * 2):
            cur.execute("""
                INSERT INTO finance.transactions 
                (account_id, transaction_type, amount, description, transaction_date)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                random.choice(accounts),
                random.choice(['deposit', 'withdrawal', 'transfer'])[:50],
                round(random.uniform(10.0, 1000.0), 2),
                fake.sentence()[:200],
                fake.date_time_this_year()
            ))
            
            if (i + 1) % 200 == 0:
                conn.commit()
                print(f"Inserted {i + 1} transactions...")
                time.sleep(0.1)
        
        conn.commit()
        
        # Generate invoices
        print("Generating invoices...")
        for i in range(num_records):
            # Generate unique invoice number using timestamp + counter
            invoice_number = generate_unique_id("INV", 8, f"{i:04d}")
            
            cur.execute("""
                INSERT INTO finance.invoices 
                (customer_id, invoice_number, amount, status, due_date, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                random.choice(customers),
                invoice_number,
                round(random.uniform(100.0, 5000.0), 2),
                random.choice(['pending', 'paid', 'overdue'])[:20],
                fake.date_time_this_year() + timedelta(days=30),
                fake.date_time_this_year()
            ))
            
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} invoices...")
                time.sleep(0.1)
        
        conn.commit()
        print("Successfully generated finance data!")
        
    except Exception as e:
        print(f"Error generating finance data: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def generate_analytics_data(conn, num_records=1000, customers=None, products=None):
    print(f"Generating analytics data for {num_records} records...")
    cur = conn.cursor(dictionary=True)
    
    try:
        # Generate user activity
        print("Generating user activity...")
        for i in range(num_records):
            cur.execute("""
                INSERT INTO analytics.user_activity 
                (user_id, activity_type, activity_date, details)
                VALUES (%s, %s, %s, %s)
            """, (
                random.choice(customers),
                random.choice(['login', 'logout', 'search', 'view_product'])[:50],
                fake.date_time_this_year(),
                json.dumps({'browser': fake.user_agent()[:100], 'ip': fake.ipv4()})
            ))
            
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} user activities...")
                time.sleep(0.1)
        
        conn.commit()
        
        # Generate page views
        print("Generating page views...")
        for i in range(num_records):
            cur.execute("""
                INSERT INTO analytics.page_views 
                (page_url, user_id, view_date, session_id)
                VALUES (%s, %s, %s, %s)
            """, (
                fake.url()[:200],
                random.choice(customers),
                fake.date_time_this_year(),
                str(uuid.uuid4())[:100]
            ))
            
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} page views...")
                time.sleep(0.1)
        
        conn.commit()
        
        # Generate product views
        print("Generating product views...")
        for i in range(num_records):
            cur.execute("""
                INSERT INTO analytics.product_views 
                (product_id, user_id, view_date, view_duration)
                VALUES (%s, %s, %s, %s)
            """, (
                random.choice(products),
                random.choice(customers),
                fake.date_time_this_year(),
                random.randint(10, 3600)
            ))
            
            if (i + 1) % 100 == 0:
                conn.commit()
                print(f"Inserted {i + 1} product views...")
                time.sleep(0.1)
        
        conn.commit()
        print("Successfully generated analytics data!")
        
    except Exception as e:
        print(f"Error generating analytics data: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def clear_tables(conn):
    print("Clearing all tables...")
    cur = conn.cursor()
    try:
        # Disable foreign key checks
        cur.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # List of tables to clear in order
        tables_to_clear = [
            'analytics.product_views',
            'analytics.page_views', 
            'analytics.user_activity',
            'finance.invoices',
            'finance.transactions',
            'finance.accounts',
            'ecommerce.cart_items',
            'ecommerce.cart',
            'ecommerce.reviews',
            'ecommerce.order_items',
            'ecommerce.orders',
            'ecommerce.products',
            'ecommerce.categories',
            'ecommerce.customers',
            'ecommerce.promotions'
        ]
        
        # Clear each table individually
        for table in tables_to_clear:
            try:
                cur.execute(f"TRUNCATE TABLE {table}")
                print(f"Cleared {table}")
                time.sleep(0.1)  # Small delay between operations
            except Exception as e:
                print(f"Warning: Could not clear {table}: {e}")
        
        # Re-enable foreign key checks
        cur.execute("SET FOREIGN_KEY_CHECKS = 1")
        conn.commit()
        print("All tables cleared successfully!")
        
    except Exception as e:
        print(f"Error clearing tables: {e}")
        conn.rollback()
        raise
    finally:
        cur.close()

def main():
    conn = None
    try:
        # Create connection
        conn = create_connection()
        print("Connected to database successfully!")
        
        # Clear existing data first
        clear_tables(conn)
        
        # Generate base data
        generate_customers(conn, 1000)
        categories = generate_categories(conn, 50)
        products = generate_products(conn, 1000, categories)
        
        # Get all customer IDs
        cur = conn.cursor(dictionary=True)
        cur.execute("SELECT customer_id FROM ecommerce.customers")
        customers = [row['customer_id'] for row in cur.fetchall()]
        cur.close()
        
        print(f"Retrieved {len(customers)} customer IDs")
        
        # Generate related data
        orders = generate_orders(conn, 1000, customers, products)
        generate_reviews(conn, 1000, customers, products)
        generate_cart_data(conn, 1000, customers, products)
        promotions = generate_promotions(conn, 100)
        generate_finance_data(conn, 1000, customers)
        generate_analytics_data(conn, 1000, customers, products)
        
        print("Data generation completed successfully!")
        
    except Exception as e:
        print(f"An error occurred: {e}")
        if conn:
            try:
                conn.rollback()
            except:
                pass
    finally:
        if conn:
            try:
                conn.close()
                print("Database connection closed.")
            except:
                pass

if __name__ == "__main__":
    main()