import mysql.connector
from test_keys import *

# Create connection
conn = mysql.connector.connect(
    host=MYSQL_HOST,
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    port=MYSQL_PORT
)

# Create cursor
cur = conn.cursor(dictionary=True)

# Create databases first
databases = ['ecommerce', 'finance', 'analytics']

for db in databases:
    try:
        # Drop database if it exists
        cur.execute(f'DROP DATABASE IF EXISTS {db};')
        print(f"Dropped database '{db}' if it existed")
        
        # Create the database
        cur.execute(f'CREATE DATABASE {db};')
        print(f"Created database '{db}'")
        
        # Commit after each database operation
        conn.commit()
        
    except Exception as e:
        print(f"Error handling database '{db}': {e}")
        conn.rollback()  # Rollback on error

print("Database creation process completed.")

# Now create tables in each database
# Ecommerce Schema Tables
cur.execute('USE ecommerce;')

# Define ecommerce tables as separate statements
ecommerce_tables = [
    """CREATE TABLE IF NOT EXISTS customers (
        customer_id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""",
    
    """CREATE TABLE IF NOT EXISTS categories (
        category_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        parent_category_id INT,
        FOREIGN KEY (parent_category_id) REFERENCES categories(category_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS products (
        product_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        stock_quantity INT NOT NULL,
        category_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(category_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS orders (
        order_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) NOT NULL,
        shipping_address TEXT,
        payment_id INT,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS order_items (
        order_item_id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT,
        product_id INT,
        quantity INT NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders(order_id),
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS reviews (
        review_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        customer_id INT,
        rating INT CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(product_id),
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS cart (
        cart_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS cart_items (
        cart_item_id INT AUTO_INCREMENT PRIMARY KEY,
        cart_id INT,
        product_id INT,
        quantity INT NOT NULL,
        FOREIGN KEY (cart_id) REFERENCES cart(cart_id),
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS promotions (
        promotion_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        discount_percentage DECIMAL(5,2),
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS product_promotions (
        product_promotion_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        promotion_id INT,
        FOREIGN KEY (product_id) REFERENCES products(product_id),
        FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS shipping (
        shipping_id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT,
        carrier VARCHAR(50) NOT NULL,
        tracking_number VARCHAR(100),
        status VARCHAR(20) NOT NULL,
        estimated_delivery TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(order_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS returns (
        return_id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT,
        customer_id INT,
        reason TEXT NOT NULL,
        status VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(order_id),
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS return_items (
        return_item_id INT AUTO_INCREMENT PRIMARY KEY,
        return_id INT,
        order_item_id INT,
        quantity INT NOT NULL,
        condition VARCHAR(20) NOT NULL,
        FOREIGN KEY (return_id) REFERENCES returns(return_id),
        FOREIGN KEY (order_item_id) REFERENCES order_items(order_item_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS wishlist (
        wishlist_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        product_id INT,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS product_images (
        image_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        image_url TEXT NOT NULL,
        is_primary BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )"""
]

# Execute each ecommerce table creation
for table_sql in ecommerce_tables:
    try:
        cur.execute(table_sql)
        print(f"Created ecommerce table")
    except Exception as e:
        print(f"Error creating ecommerce table: {e}")

conn.commit()
print("Ecommerce tables created successfully!")

# Finance Schema Tables
cur.execute("USE finance;")

finance_tables = [
    """CREATE TABLE IF NOT EXISTS accounts (
        account_id INT AUTO_INCREMENT PRIMARY KEY,
        account_number VARCHAR(20) UNIQUE NOT NULL,
        account_type VARCHAR(50) NOT NULL,
        balance DECIMAL(15,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""",
    
    """CREATE TABLE IF NOT EXISTS transactions (
        transaction_id INT AUTO_INCREMENT PRIMARY KEY,
        account_id INT,
        transaction_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts(account_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS invoices (
        invoice_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        invoice_number VARCHAR(20) UNIQUE NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        status VARCHAR(20) NOT NULL,
        due_date TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""",
    
    """CREATE TABLE IF NOT EXISTS payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id INT,
        amount DECIMAL(15,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(20) NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS expenses (
        expense_id INT AUTO_INCREMENT PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        expense_date TIMESTAMP NOT NULL,
        payment_method VARCHAR(50)
    )""",
    
    """CREATE TABLE IF NOT EXISTS budgets (
        budget_id INT AUTO_INCREMENT PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS tax_records (
        tax_record_id INT AUTO_INCREMENT PRIMARY KEY,
        tax_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        tax_period VARCHAR(20) NOT NULL,
        due_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS credit_cards (
        credit_card_id INT AUTO_INCREMENT PRIMARY KEY,
        card_number VARCHAR(20) NOT NULL,
        card_holder VARCHAR(100) NOT NULL,
        expiry_date DATE NOT NULL,
        credit_limit DECIMAL(15,2) NOT NULL,
        current_balance DECIMAL(15,2) NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS credit_card_transactions (
        transaction_id INT AUTO_INCREMENT PRIMARY KEY,
        credit_card_id INT,
        amount DECIMAL(15,2) NOT NULL,
        merchant VARCHAR(100) NOT NULL,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (credit_card_id) REFERENCES credit_cards(credit_card_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS loans (
        loan_id INT AUTO_INCREMENT PRIMARY KEY,
        loan_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        interest_rate DECIMAL(5,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS loan_payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        loan_id INT,
        amount DECIMAL(15,2) NOT NULL,
        payment_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL,
        FOREIGN KEY (loan_id) REFERENCES loans(loan_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS investments (
        investment_id INT AUTO_INCREMENT PRIMARY KEY,
        investment_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP,
        return_rate DECIMAL(5,2)
    )""",
    
    """CREATE TABLE IF NOT EXISTS investment_returns (
        return_id INT AUTO_INCREMENT PRIMARY KEY,
        investment_id INT,
        amount DECIMAL(15,2) NOT NULL,
        return_date TIMESTAMP NOT NULL,
        FOREIGN KEY (investment_id) REFERENCES investments(investment_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS currency_exchange (
        exchange_id INT AUTO_INCREMENT PRIMARY KEY,
        from_currency VARCHAR(3) NOT NULL,
        to_currency VARCHAR(3) NOT NULL,
        rate DECIMAL(10,4) NOT NULL,
        exchange_date TIMESTAMP NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS financial_reports (
        report_id INT AUTO_INCREMENT PRIMARY KEY,
        report_type VARCHAR(50) NOT NULL,
        period VARCHAR(20) NOT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        content TEXT NOT NULL
    )"""
]

# Execute each finance table creation
for table_sql in finance_tables:
    try:
        cur.execute(table_sql)
        print(f"Created finance table")
    except Exception as e:
        print(f"Error creating finance table: {e}")

conn.commit()
print("Finance tables created successfully!")

# Analytics Schema Tables
cur.execute("USE analytics;")

analytics_tables = [
    """CREATE TABLE IF NOT EXISTS user_activity (
        activity_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        activity_type VARCHAR(50) NOT NULL,
        activity_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details JSON
    )""",
    
    """CREATE TABLE IF NOT EXISTS page_views (
        view_id INT AUTO_INCREMENT PRIMARY KEY,
        page_url TEXT NOT NULL,
        user_id INT,
        view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        session_id VARCHAR(100)
    )""",
    
    """CREATE TABLE IF NOT EXISTS search_queries (
        query_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        query_text TEXT NOT NULL,
        search_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        results_count INT
    )""",
    
    """CREATE TABLE IF NOT EXISTS product_views (
        view_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        user_id INT,
        view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        view_duration INT
    )""",
    
    """CREATE TABLE IF NOT EXISTS conversion_funnel (
        funnel_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        stage VARCHAR(50) NOT NULL,
        stage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        conversion_value DECIMAL(15,2)
    )""",
    
    """CREATE TABLE IF NOT EXISTS customer_segments (
        segment_id INT AUTO_INCREMENT PRIMARY KEY,
        segment_name VARCHAR(50) NOT NULL,
        criteria JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""",
    
    """CREATE TABLE IF NOT EXISTS segment_members (
        member_id INT AUTO_INCREMENT PRIMARY KEY,
        segment_id INT,
        customer_id INT,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (segment_id) REFERENCES customer_segments(segment_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS marketing_campaigns (
        campaign_id INT AUTO_INCREMENT PRIMARY KEY,
        campaign_name VARCHAR(100) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        budget DECIMAL(15,2) NOT NULL,
        status VARCHAR(20) NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS campaign_metrics (
        metric_id INT AUTO_INCREMENT PRIMARY KEY,
        campaign_id INT,
        metric_name VARCHAR(50) NOT NULL,
        metric_value DECIMAL(15,2) NOT NULL,
        metric_date TIMESTAMP NOT NULL,
        FOREIGN KEY (campaign_id) REFERENCES marketing_campaigns(campaign_id)
    )""",
    
    """CREATE TABLE IF NOT EXISTS sales_forecast (
        forecast_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        forecast_date TIMESTAMP NOT NULL,
        predicted_sales DECIMAL(15,2) NOT NULL,
        confidence_interval DECIMAL(5,2)
    )""",
    
    """CREATE TABLE IF NOT EXISTS inventory_forecast (
        forecast_id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        forecast_date TIMESTAMP NOT NULL,
        predicted_stock INT NOT NULL,
        confidence_interval DECIMAL(5,2)
    )""",
    
    """CREATE TABLE IF NOT EXISTS customer_lifetime_value (
        clv_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        calculated_date TIMESTAMP NOT NULL,
        predicted_value DECIMAL(15,2) NOT NULL,
        confidence_interval DECIMAL(5,2)
    )""",
    
    """CREATE TABLE IF NOT EXISTS churn_prediction (
        prediction_id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        prediction_date TIMESTAMP NOT NULL,
        churn_probability DECIMAL(5,2) NOT NULL,
        risk_level VARCHAR(20) NOT NULL
    )""",
    
    """CREATE TABLE IF NOT EXISTS performance_metrics (
        metric_id INT AUTO_INCREMENT PRIMARY KEY,
        metric_name VARCHAR(50) NOT NULL,
        metric_value DECIMAL(15,2) NOT NULL,
        metric_date TIMESTAMP NOT NULL,
        category VARCHAR(50) NOT NULL
    )"""
]

# Execute each analytics table creation
for table_sql in analytics_tables:
    try:
        cur.execute(table_sql)
        print(f"Created analytics table")
    except Exception as e:
        print(f"Error creating analytics table: {e}")

conn.commit()
print("Analytics tables created successfully!")

# Close cursor and connection
cur.close()
conn.close()

print("Database schemas and tables created successfully!")