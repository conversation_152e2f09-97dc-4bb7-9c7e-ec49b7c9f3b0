

import psycopg2
import psycopg2.extras

from test_keys import *

# Create connection
conn = psycopg2.connect(
    host=POSTGRES_HOST,
    database="ai_copilot",
    user=POSTGRES_USER,
    password=POSTGRES_PASSWORD,
    port=POSTGRES_PORT
)

# Create cursor
cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

# Create schemas
schemas = ['ecommerce', 'finance', 'analytics']
for schema in schemas:
    cur.execute(f'CREATE SCHEMA IF NOT EXISTS {schema};')

# Ecommerce Schema Tables
cur.execute("""
    -- Ecommerce Schema Tables
    CREATE TABLE IF NOT EXISTS ecommerce.customers (
        customer_id SERIAL PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.products (
        product_id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        stock_quantity INTEGER NOT NULL,
        category_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.categories (
        category_id SERIAL PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        parent_category_id INTEGER REFERENCES ecommerce.categories(category_id)
    );

    CREATE TABLE IF NOT EXISTS ecommerce.orders (
        order_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) NOT NULL,
        shipping_address TEXT,
        payment_id INTEGER
    );

    CREATE TABLE IF NOT EXISTS ecommerce.order_items (
        order_item_id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES ecommerce.orders(order_id),
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS ecommerce.reviews (
        review_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.cart (
        cart_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.cart_items (
        cart_item_id SERIAL PRIMARY KEY,
        cart_id INTEGER REFERENCES ecommerce.cart(cart_id),
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        quantity INTEGER NOT NULL
    );

    CREATE TABLE IF NOT EXISTS ecommerce.promotions (
        promotion_id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        discount_percentage DECIMAL(5,2),
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL
    );

    CREATE TABLE IF NOT EXISTS ecommerce.product_promotions (
        product_promotion_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        promotion_id INTEGER REFERENCES ecommerce.promotions(promotion_id)
    );

    CREATE TABLE IF NOT EXISTS ecommerce.shipping (
        shipping_id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES ecommerce.orders(order_id),
        carrier VARCHAR(50) NOT NULL,
        tracking_number VARCHAR(100),
        status VARCHAR(20) NOT NULL,
        estimated_delivery TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.returns (
        return_id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES ecommerce.orders(order_id),
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        reason TEXT NOT NULL,
        status VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.return_items (
        return_item_id SERIAL PRIMARY KEY,
        return_id INTEGER REFERENCES ecommerce.returns(return_id),
        order_item_id INTEGER REFERENCES ecommerce.order_items(order_item_id),
        quantity INTEGER NOT NULL,
        condition VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS ecommerce.wishlist (
        wishlist_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS ecommerce.product_images (
        image_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        image_url TEXT NOT NULL,
        is_primary BOOLEAN DEFAULT FALSE
    );
""")

# Finance Schema Tables
cur.execute("""
    -- Finance Schema Tables
    CREATE TABLE IF NOT EXISTS finance.accounts (
        account_id SERIAL PRIMARY KEY,
        account_number VARCHAR(20) UNIQUE NOT NULL,
        account_type VARCHAR(50) NOT NULL,
        balance DECIMAL(15,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS finance.transactions (
        transaction_id SERIAL PRIMARY KEY,
        account_id INTEGER REFERENCES finance.accounts(account_id),
        transaction_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS finance.invoices (
        invoice_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        invoice_number VARCHAR(20) UNIQUE NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        status VARCHAR(20) NOT NULL,
        due_date TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS finance.payments (
        payment_id SERIAL PRIMARY KEY,
        invoice_id INTEGER REFERENCES finance.invoices(invoice_id),
        amount DECIMAL(15,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.expenses (
        expense_id SERIAL PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT,
        expense_date TIMESTAMP NOT NULL,
        payment_method VARCHAR(50)
    );

    CREATE TABLE IF NOT EXISTS finance.budgets (
        budget_id SERIAL PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.tax_records (
        tax_record_id SERIAL PRIMARY KEY,
        tax_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        tax_period VARCHAR(20) NOT NULL,
        due_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.credit_cards (
        credit_card_id SERIAL PRIMARY KEY,
        card_number VARCHAR(20) NOT NULL,
        card_holder VARCHAR(100) NOT NULL,
        expiry_date DATE NOT NULL,
        credit_limit DECIMAL(15,2) NOT NULL,
        current_balance DECIMAL(15,2) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.credit_card_transactions (
        transaction_id SERIAL PRIMARY KEY,
        credit_card_id INTEGER REFERENCES finance.credit_cards(credit_card_id),
        amount DECIMAL(15,2) NOT NULL,
        merchant VARCHAR(100) NOT NULL,
        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS finance.loans (
        loan_id SERIAL PRIMARY KEY,
        loan_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        interest_rate DECIMAL(5,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.loan_payments (
        payment_id SERIAL PRIMARY KEY,
        loan_id INTEGER REFERENCES finance.loans(loan_id),
        amount DECIMAL(15,2) NOT NULL,
        payment_date TIMESTAMP NOT NULL,
        status VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.investments (
        investment_id SERIAL PRIMARY KEY,
        investment_type VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP,
        return_rate DECIMAL(5,2)
    );

    CREATE TABLE IF NOT EXISTS finance.investment_returns (
        return_id SERIAL PRIMARY KEY,
        investment_id INTEGER REFERENCES finance.investments(investment_id),
        amount DECIMAL(15,2) NOT NULL,
        return_date TIMESTAMP NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.currency_exchange (
        exchange_id SERIAL PRIMARY KEY,
        from_currency VARCHAR(3) NOT NULL,
        to_currency VARCHAR(3) NOT NULL,
        rate DECIMAL(10,4) NOT NULL,
        exchange_date TIMESTAMP NOT NULL
    );

    CREATE TABLE IF NOT EXISTS finance.financial_reports (
        report_id SERIAL PRIMARY KEY,
        report_type VARCHAR(50) NOT NULL,
        period VARCHAR(20) NOT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        content TEXT NOT NULL
    );
""")

# Analytics Schema Tables
cur.execute("""
    -- Analytics Schema Tables
    CREATE TABLE IF NOT EXISTS analytics.user_activity (
        activity_id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES ecommerce.customers(customer_id),
        activity_type VARCHAR(50) NOT NULL,
        activity_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details JSONB
    );

    CREATE TABLE IF NOT EXISTS analytics.page_views (
        view_id SERIAL PRIMARY KEY,
        page_url TEXT NOT NULL,
        user_id INTEGER REFERENCES ecommerce.customers(customer_id),
        view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        session_id VARCHAR(100)
    );

    CREATE TABLE IF NOT EXISTS analytics.search_queries (
        query_id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES ecommerce.customers(customer_id),
        query_text TEXT NOT NULL,
        search_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        results_count INTEGER
    );

    CREATE TABLE IF NOT EXISTS analytics.product_views (
        view_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        user_id INTEGER REFERENCES ecommerce.customers(customer_id),
        view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        view_duration INTEGER
    );

    CREATE TABLE IF NOT EXISTS analytics.conversion_funnel (
        funnel_id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES ecommerce.customers(customer_id),
        stage VARCHAR(50) NOT NULL,
        stage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        conversion_value DECIMAL(15,2)
    );

    CREATE TABLE IF NOT EXISTS analytics.customer_segments (
        segment_id SERIAL PRIMARY KEY,
        segment_name VARCHAR(50) NOT NULL,
        criteria JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS analytics.segment_members (
        member_id SERIAL PRIMARY KEY,
        segment_id INTEGER REFERENCES analytics.customer_segments(segment_id),
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS analytics.marketing_campaigns (
        campaign_id SERIAL PRIMARY KEY,
        campaign_name VARCHAR(100) NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        budget DECIMAL(15,2) NOT NULL,
        status VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS analytics.campaign_metrics (
        metric_id SERIAL PRIMARY KEY,
        campaign_id INTEGER REFERENCES analytics.marketing_campaigns(campaign_id),
        metric_name VARCHAR(50) NOT NULL,
        metric_value DECIMAL(15,2) NOT NULL,
        metric_date TIMESTAMP NOT NULL
    );

    CREATE TABLE IF NOT EXISTS analytics.sales_forecast (
        forecast_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        forecast_date TIMESTAMP NOT NULL,
        predicted_sales DECIMAL(15,2) NOT NULL,
        confidence_interval DECIMAL(5,2)
    );

    CREATE TABLE IF NOT EXISTS analytics.inventory_forecast (
        forecast_id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES ecommerce.products(product_id),
        forecast_date TIMESTAMP NOT NULL,
        predicted_stock INTEGER NOT NULL,
        confidence_interval DECIMAL(5,2)
    );

    CREATE TABLE IF NOT EXISTS analytics.customer_lifetime_value (
        clv_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        calculated_date TIMESTAMP NOT NULL,
        predicted_value DECIMAL(15,2) NOT NULL,
        confidence_interval DECIMAL(5,2)
    );

    CREATE TABLE IF NOT EXISTS analytics.churn_prediction (
        prediction_id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES ecommerce.customers(customer_id),
        prediction_date TIMESTAMP NOT NULL,
        churn_probability DECIMAL(5,2) NOT NULL,
        risk_level VARCHAR(20) NOT NULL
    );

    CREATE TABLE IF NOT EXISTS analytics.performance_metrics (
        metric_id SERIAL PRIMARY KEY,
        metric_name VARCHAR(50) NOT NULL,
        metric_value DECIMAL(15,2) NOT NULL,
        metric_date TIMESTAMP NOT NULL,
        category VARCHAR(50) NOT NULL
    );
""")

# Commit the changes
conn.commit()

# Close cursor and connection
cur.close()
conn.close()

print("Database schemas and tables created successfully!")