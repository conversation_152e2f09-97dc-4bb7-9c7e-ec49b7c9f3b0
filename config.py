from dotenv import load_dotenv
import os
import json
import asyncio
import random


load_dotenv(override=True)


current_dir = os.path.dirname(os.path.abspath(__file__))

class Config:
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") or "********************************************************************************************************************************************************************"
    REDIS_URL = os.getenv("REDIS_URL") or "redis://localhost:6379/0"
    RABBITMQ_URL = os.getenv("RABBITMQ_URL") or "guest:guest@localhost:5672/"
    ANTHROPIC_API_KEY_2 = os.getenv("ANTHROPIC_API_KEY_2") or "************************************************************************************************************"
    SECRET_KEY = os.getenv("SECRET_KEY") or "52ef9387c3b146c063f708ce10bcd65856cd592f13d50fc5bb76c4c8cf502260a52ca933637c9d8f9274940227d49e44439721f96468c4ab6fcf6eb34e1f3af3e0f4c3ba9db1b4d484399b45ab4b375adadfe989c78c578ffccdf72c8179dafbf698d018fd8750e227404019263c679c4a655f548458549e5327e2f00e8fef3d34dfd710a1de25914ba6d2dc85b69ff18f003e43b7721b1b454f97d23582c5487b425738d99ef1739e3cccae8999a072b1c1c98fa6215ab6202106aa7d3d9dac79453c27518d1faf8b7e9fa6999b12ae1ff683dc63d793322038d293ef07d3f285f74673623b8fad4354ebda800bff3c9407a4ec2d9b15650de4d"
    ALGORITHM = os.getenv("ALGORITHM") or "HS256"
    SLACK_CLIENT_ID = os.getenv("SLACK_CLIENT_ID") or '495187095732.8752982830483'
    SLACK_SECRET_ID = os.getenv("SLACK_SECRET_ID") or '75adca3914f08d9d8c902cd8bd504a3c'
    REDIRECT_URI = os.getenv("REDIRECT_URI") or 'https://a5b265cb5d5c.ngrok-free.app/api/auth/slack/oauth/callback'
    BASE_URL = "https://sql-copilot-v2.afx-server.com"

    SLACK_BOT_TOKEN = os.getenv("SLACK_BOT_TOKEN") 
    SLACK_APP_TOKEN = os.getenv("SLACK_APP_TOKEN") 

    DB_HOST = os.getenv("DB_HOST") or "localhost"
    DB_PORT = os.getenv("DB_PORT") or 3306
    DB_USER = os.getenv("DB_USER") or "root"
    DB_PASSWORD = os.getenv("DB_PASSWORD") or "sammy509"
    DB_NAME = os.getenv("DB_NAME") or "finance"
    
    DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

    MAILGUN_API_KEY = os.getenv("MAILGUN_API_KEY") 

    MAILGUN_API_DOMAIN = os.getenv("MAILGUN_API_DOMAIN") 
    FERNET_SECRET_KEY = os.getenv("FERNET_SECRET_KEY")

    


TEST_MONGO_DB_PATH  = os.path.join(current_dir,"data/sample_data/schema_analysis_mongo.json")
TEST_SNOQFLAKE_PATH  = os.path.join(current_dir,"data/sample_data/schema_analysis._snowflake.json")
TEST_MYSQL_PATH  = os.path.join(current_dir,"data/sample_data/schema_analysis_mysql.json")

TEST_POSTGRES_TRANSFORMED_PATH  = os.path.join(current_dir,"data/sample_data/postgres_transformed_data.json")
TEST_SNOWFLAEK_TRANSFORMED_PATH  = os.path.join(current_dir,"data/sample_data/snowflake_transformed_data.json")
TEST_MYSQL_TRANSFORMED_PATH= os.path.join(current_dir,"data/sample_data/mysql_transformed_data.json")

# Set a seed for random number generation for consistency
random.seed(42)



test_params = {
   "user":os.getenv("SNOWFLAKE_USER"),
   "password":os.getenv("SNOWFLAKE_PASSWORD"),
   "account":os.getenv("SNOWFLAKE_ACCOUNT"),
   "role":os.getenv("ROLE"), 
   "database":os.getenv("DATABASE"),
   "warehouse":os.getenv("SNOWFLAKE_WAREHOUSE")

}


MYSQL_HOST = "localhost"
MYSQL_PASSWORD = "sammy509"
MYSQL_PORT = 3306
MYSQL_USER = "root"
test_params_mysql = {


}


with open(TEST_MONGO_DB_PATH, "r") as f:
    MONGO_TEST_DB = json.load(f)
    collections = MONGO_TEST_DB["collections"]
    # pick 7 random elements (seeded for consistency)
    moongo_sample_db = random.sample(collections, 7)
    #print("Mongo Sample DB:", [collection["collection_name"] for collection in moongo_sample_db])  # Print the selected collection names


with open(TEST_SNOQFLAKE_PATH, "r") as f:
    db = json.load(f)
    tables = db["tables"]
    # pick 7 random elements (seeded for consistency)
    snowflake_sample_db = random.sample(tables, 7)
    #print("Snowflake Sample DB:", [table["table_name"] for table in tables])  # Print the selected table names



