import psycopg2
import psycopg2.extras
from faker import Faker
import random
from datetime import datetime, timedelta
import json
import uuid

# Database connection parameters
POSTGRES_HOST = "database-1.cw7tvjq91kjw.us-east-1.rds.amazonaws.com"
POSTGRES_PASSWORD = "afri-Sam22*3"
POSTGRES_PORT = 5432
POSTGRES_USER = "afriex_samuel"

# Initialize Faker
fake = Faker()

# Create connection
conn = psycopg2.connect(
    host=POSTGRES_HOST,
    database="ai_copilot",
    user=POSTGRES_USER,
    password=POSTGRES_PASSWORD,
    port=POSTGRES_PORT
)

# Create cursor
cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

def generate_phone():
    # Generate a phone number that fits within 20 characters
    return f"+{random.randint(1, 999)}-{random.randint(100, 999)}-{random.randint(1000, 9999)}"

def generate_unique_email():
    # Generate a unique email by adding a UUID
    base_email = fake.email()
    unique_id = str(uuid.uuid4())[:8]  # Take first 8 characters of UUID
    username, domain = base_email.split('@')
    return f"{username}_{unique_id}@{domain}"

def generate_customers(num_records=1000):
    print("Generating customers...")
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO ecommerce.customers 
            (first_name, last_name, email, phone, address, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING customer_id
        """, (
            fake.first_name()[:50],  # Limit to 50 chars
            fake.last_name()[:50],   # Limit to 50 chars
            generate_unique_email()[:100],  # Limit to 100 chars
            generate_phone(),  # Already limited to 20 chars
            fake.address()[:200],  # Limit to 200 chars
            fake.date_time_this_year()
        ))
    conn.commit()

def generate_categories(num_records=50):
    print("Generating categories...")
    categories = []
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO ecommerce.categories 
            (name, description, parent_category_id)
            VALUES (%s, %s, %s)
            RETURNING category_id
        """, (
            fake.word().capitalize()[:50],  # Limit to 50 chars
            fake.sentence()[:200],  # Limit to 200 chars
            random.choice([None] + categories) if categories else None
        ))
        categories.append(cur.fetchone()[0])
    conn.commit()
    return categories

def generate_products(num_records=1000, categories=None):
    print("Generating products...")
    products = []
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO ecommerce.products 
            (name, description, price, stock_quantity, category_id, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING product_id
        """, (
            fake.catch_phrase()[:100],  # Limit to 100 chars
            fake.text(max_nb_chars=200),  # Already limited to 200 chars
            round(random.uniform(10.0, 1000.0), 2),
            random.randint(0, 1000),
            random.choice(categories) if categories else None,
            fake.date_time_this_year()
        ))
        products.append(cur.fetchone()[0])
    conn.commit()
    return products

def generate_orders(num_records=1000, customers=None, products=None):
    print("Generating orders...")
    orders = []
    for _ in range(num_records):
        customer_id = random.choice(customers)
        cur.execute("""
            INSERT INTO ecommerce.orders 
            (customer_id, order_date, total_amount, status, shipping_address)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING order_id
        """, (
            customer_id,
            fake.date_time_this_year(),
            round(random.uniform(50.0, 2000.0), 2),
            random.choice(['pending', 'processing', 'shipped', 'delivered', 'cancelled'])[:20],  # Limit to 20 chars
            fake.address()[:200]  # Limit to 200 chars
        ))
        order_id = cur.fetchone()[0]
        orders.append(order_id)
        
        # Generate 1-5 items per order
        num_items = random.randint(1, 5)
        for _ in range(num_items):
            product_id = random.choice(products)
            quantity = random.randint(1, 5)
            cur.execute("""
                INSERT INTO ecommerce.order_items 
                (order_id, product_id, quantity, unit_price)
                VALUES (%s, %s, %s, %s)
            """, (
                order_id,
                product_id,
                quantity,
                round(random.uniform(10.0, 500.0), 2)
            ))
    conn.commit()
    return orders

def generate_reviews(num_records=1000, customers=None, products=None):
    print("Generating reviews...")
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO ecommerce.reviews 
            (product_id, customer_id, rating, comment, created_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            random.choice(products),
            random.choice(customers),
            random.randint(1, 5),
            fake.text(max_nb_chars=200),  # Limit to 200 chars
            fake.date_time_this_year()
        ))
    conn.commit()

def generate_cart_data(num_records=1000, customers=None, products=None):
    print("Generating cart data...")
    for _ in range(num_records):
        customer_id = random.choice(customers)
        cur.execute("""
            INSERT INTO ecommerce.cart 
            (customer_id, created_at)
            VALUES (%s, %s)
            RETURNING cart_id
        """, (
            customer_id,
            fake.date_time_this_year()
        ))
        cart_id = cur.fetchone()[0]
        
        # Generate 1-5 items per cart
        num_items = random.randint(1, 5)
        for _ in range(num_items):
            cur.execute("""
                INSERT INTO ecommerce.cart_items 
                (cart_id, product_id, quantity)
                VALUES (%s, %s, %s)
            """, (
                cart_id,
                random.choice(products),
                random.randint(1, 5)
            ))
    conn.commit()

def generate_promotions(num_records=100):
    print("Generating promotions...")
    promotions = []
    for _ in range(num_records):
        start_date = fake.date_time_this_year()
        end_date = start_date + timedelta(days=random.randint(7, 30))
        cur.execute("""
            INSERT INTO ecommerce.promotions 
            (name, description, discount_percentage, start_date, end_date)
            VALUES (%s, %s, %s, %s, %s)
            RETURNING promotion_id
        """, (
            fake.catch_phrase()[:100],  # Limit to 100 chars
            fake.text(max_nb_chars=200),  # Limit to 200 chars
            round(random.uniform(5.0, 50.0), 2),
            start_date,
            end_date
        ))
        promotions.append(cur.fetchone()[0])
    conn.commit()
    return promotions

def generate_finance_data(num_records=1000, customers=None):
    print("Generating finance data...")
    # Generate accounts
    accounts = []
    for _ in range(num_records):
        # Generate a shorter account number that fits within 20 characters
        account_number = f"ACC-{random.randint(100000, 999999)}-{random.randint(1000, 9999)}"
        cur.execute("""
            INSERT INTO finance.accounts 
            (account_number, account_type, balance, created_at)
            VALUES (%s, %s, %s, %s)
            RETURNING account_id
        """, (
            account_number,
            random.choice(['checking', 'savings', 'investment'])[:50],  # Limit to 50 chars
            round(random.uniform(100.0, 10000.0), 2),
            fake.date_time_this_year()
        ))
        accounts.append(cur.fetchone()[0])
    
    # Generate transactions
    for _ in range(num_records * 2):
        cur.execute("""
            INSERT INTO finance.transactions 
            (account_id, transaction_type, amount, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s)
        """, (
            random.choice(accounts),
            random.choice(['deposit', 'withdrawal', 'transfer'])[:50],  # Limit to 50 chars
            round(random.uniform(10.0, 1000.0), 2),
            fake.sentence()[:200],  # Limit to 200 chars
            fake.date_time_this_year()
        ))
    
    # Generate invoices
    for _ in range(num_records):
        invoice_number = f"INV-{random.randint(100000, 999999)}"  # Generate shorter invoice number
        cur.execute("""
            INSERT INTO finance.invoices 
            (customer_id, invoice_number, amount, status, due_date, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            random.choice(customers),
            invoice_number,
            round(random.uniform(100.0, 5000.0), 2),
            random.choice(['pending', 'paid', 'overdue'])[:20],  # Limit to 20 chars
            fake.date_time_this_year() + timedelta(days=30),
            fake.date_time_this_year()
        ))
    
    conn.commit()

def generate_analytics_data(num_records=1000, customers=None, products=None):
    print("Generating analytics data...")
    # Generate user activity
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO analytics.user_activity 
            (user_id, activity_type, activity_date, details)
            VALUES (%s, %s, %s, %s)
        """, (
            random.choice(customers),
            random.choice(['login', 'logout', 'search', 'view_product'])[:50],  # Limit to 50 chars
            fake.date_time_this_year(),
            json.dumps({'browser': fake.user_agent()[:100], 'ip': fake.ipv4()})  # Limit browser to 100 chars
        ))
    

    # Generate page views
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO analytics.page_views 
            (page_url, user_id, view_date, session_id)
            VALUES (%s, %s, %s, %s)
        """, (
            fake.url()[:200],  # Limit to 200 chars
            random.choice(customers),
            fake.date_time_this_year(),
            str(uuid.uuid4())[:100]  # Limit to 100 chars
        ))
    
    # Generate product views
    for _ in range(num_records):
        cur.execute("""
            INSERT INTO analytics.product_views 
            (product_id, user_id, view_date, view_duration)
            VALUES (%s, %s, %s, %s)
        """, (
            random.choice(products),
            random.choice(customers),
            fake.date_time_this_year(),
            random.randint(10, 3600)
        ))
    
    conn.commit()

def clear_tables():
    print("Clearing all tables...")
    try:
        # Clear tables in reverse order of dependencies
        cur.execute("""
            TRUNCATE TABLE 
            analytics.product_views,
            analytics.page_views,
            analytics.user_activity,
            finance.invoices,
            finance.transactions,
            finance.accounts,
            ecommerce.cart_items,
            ecommerce.cart,
            ecommerce.reviews,
            ecommerce.order_items,
            ecommerce.orders,
            ecommerce.products,
            ecommerce.categories,
            ecommerce.customers
            CASCADE;
        """)
        conn.commit()
        print("All tables cleared successfully!")
    except Exception as e:
        print(f"Error clearing tables: {e}")
        conn.rollback()

def main():
    try:
        # Clear existing data first
        clear_tables()
        
        # Generate base data
        generate_customers(1000)
        categories = generate_categories(50)
        products = generate_products(1000, categories)
        
        # Get all customer IDs
        cur.execute("SELECT customer_id FROM ecommerce.customers")
        customers = [row[0] for row in cur.fetchall()]
        
        # Generate related data
        orders = generate_orders(1000, customers, products)
        generate_reviews(1000, customers, products)
        generate_cart_data(1000, customers, products)
        promotions = generate_promotions(100)
        generate_finance_data(1000, customers)
        generate_analytics_data(1000, customers, products)
        
        print("Data generation completed successfully!")
        
    except Exception as e:
        print(f"An error occurred: {e}")
        conn.rollback()
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    main() 



