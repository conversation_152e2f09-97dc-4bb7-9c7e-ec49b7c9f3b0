{"report": {"total_schemas": 1, "analyzed_schemas": [{"schema_name": "INGEST", "tables_count": 20, "successful_tables": 20, "failed_tables": 0}], "total_tables_found": 20, "successfully_analyzed": 20, "failed_analysis": 0, "failed_tables": [], "execution_time": 551.38}, "tables": [{"schema_name": "INGEST", "table_name": "RATES_BATCHES", "full_table_name": "INGEST.RATES_BATCHES", "description": "The RATES_BATCHES table stores information about batches of rates, including their tolerance levels, administrative details, arbitrage information, links to rates, and timestamps for creation and updates.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each rates batch record."}, {"name": "tolerance", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The acceptable range of variation for the rates in this batch.", "found_categorical_values": [1e-05, 0.001, 0.25, 1.0, 3.0, 4.0]}, {"name": "admin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the administrator responsible for this rates batch.", "variant": "json_variant"}, {"name": "arbitrageInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Details related to arbitrage opportunities associated with the rates in this batch.", "variant": "json_variant"}, {"name": "ratesLink", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A URL or reference link to the source of the rates data."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the rates batch record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the rates batch record was created."}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"tolerance\": 0.001,\n  \"admin\": \"{\\n  \\\"id\\\": \\\"abcdef********90abcdef12\\\",\\n  \\\"name\\\": \\\"Alice\\\",\\n  \\\"role\\\": \\\"operations.manager\\\"\\n}\",\n  \"arbitrageInfo\": \"null\",\n  \"ratesLink\": \"rates-2025-01-15T09:30:45.123Z.csv\",\n  \"updatedAt\": \"2025-01-15T09:30:48.456000\",\n  \"createdAt\": \"2025-01-15T09:30:48.456000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "DISPUTES", "full_table_name": "INGEST.DISPUTES", "description": "The DISPUTES table stores information about disputes raised by users regarding transactions. It tracks the details of each dispute, including the parties involved, the amount in question, and the status of the dispute resolution process.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each dispute record."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the dispute was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp when the dispute record was last updated."}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who raised the dispute."}, {"name": "paymentId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the payment transaction associated with the dispute."}, {"name": "currency", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which the disputed amount is denominated.", "found_categorical_values": ["CAD", "ETB", "EUR", "GBP", "GHS", "KES", "NGN", "UGX", "USD"]}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The monetary amount that is being disputed."}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the dispute (e.g., pending, resolved, rejected).", "found_categorical_values": ["closed", "created", "funds_reinstated", "funds_withdrawn", "lost", "updated"]}, {"name": "reason", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A brief description of the reason for raising the dispute."}, {"name": "internalData", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional internal data related to the dispute, used for processing and tracking.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"5f6b2c3d4e5f6g7h8i9j0k1l2\",\n  \"createdAt\": \"2024-08-15T09:12:45.120000\",\n  \"updatedAt\": \"2024-08-20T11:45:32.842000\",\n  \"userId\": \"5a6b7c8002e800153571b87e\",\n  \"paymentId\": \"5b6c7d8e9f134b5667ef1762\",\n  \"currency\": \"EUR\",\n  \"amount\": 250.0,\n  \"status\": \"pending\",\n  \"reason\": \"suspicious_activity\",\n  \"internalData\": \"{\\n  \\\"provider\\\": \\\"PAYPAL\\\",\\n  \\\"providerId\\\": \\\"px_1Aqz3IB8kyqH90e5Zifg3pQR\\\",\\n  \\\"providerReason\\\": \\\"suspicious_activity\\\",\\n  \\\"providerStatus\\\": \\\"awaiting_response\\\"\\n}\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "PAYMENTMETHODS", "full_table_name": "INGEST.PAYMENTMETHODS", "description": "The PAYMENTMETHODS table stores information about various payment methods available for users, including their capabilities and current status.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each payment method entry."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the payment method was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the payment method was created."}, {"name": "channel", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The channel through which the payment method is available (e.g., online, in-store).", "found_categorical_values": ["ACH_BANK_ACCOUNT", "BANK_ACCOUNT", "CARD", "INTERAC", "MOBILE_MONEY", "RFP", "UPI", "VIRTUAL_BANK_ACCOUNT", "VIRTUAL_CARD"]}, {"name": "capabilities", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A list of capabilities supported by the payment method (e.g., refunds, recurring payments).", "found_categorical_values": ["DEPOSIT", "DEPOSIT,WITHDRAW", "WITHDRAW", "WITHDRAW,DEPOSIT"]}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user associated with the payment method."}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The payment processor that handles transactions for this payment method.", "found_categorical_values": ["ALPAY", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "MONIEPOINT", "ONAFRIQ", "PAYAZA", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "WAPIPAY", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current status of the payment method (e.g., active, inactive, suspended).", "found_categorical_values": ["active", "deleted", "expired", "pending"]}, {"name": "info", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional information or metadata about the payment method.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"5f4e3d2c1b0a4c3b8e9f0a1b\",\n  \"updatedAt\": \"2023-06-15T12:45:30.123000\",\n  \"createdAt\": \"2023-06-01T09:30:15.456000\",\n  \"channel\": \"BANK_TRANSFER\",\n  \"capabilities\": \"WITHDRAWAL\",\n  \"userId\": \"5f4e3d2c1b0a4c3b8e9f0a2c\",\n  \"processor\": \"PAYPAL\",\n  \"status\": \"active\",\n  \"info\": \"{\\n  \\\"brand\\\": \\\"MasterCard\\\",\\n  \\\"country\\\": \\\"CA\\\",\\n  \\\"currency\\\": \\\"CAD\\\",\\n  \\\"expiration\\\": {\\n    \\\"month\\\": 6,\\n    \\\"year\\\": 2026\\n  },\\n  \\\"fingerPrint\\\": \\\"random_fingerprint\\\",\\n  \\\"last4\\\": \\\"1234\\\",\\n  \\\"locations\\\": {\\n    \\\"address\\\": {\\n      \\\"city\\\": \\\"Toronto\\\",\\n      \\\"country\\\": \\\"ca\\\",\\n      \\\"line1\\\": \\\"456 Maple Street, Toronto, ON, Canada\\\",\\n      \\\"line2\\\": null,\\n      \\\"state\\\": \\\"ON\\\",\\n      \\\"zip\\\": \\\"M5H 2N2\\\"\\n    },\\n    \\\"distanceCheck\\\": 2,\\n    \\\"latitude\\\": 43.65107,\\n    \\\"longitude\\\": -79.347015,\\n    \\\"zip\\\": \\\"M5H 1J1\\\"\\n  },\\n  \\\"name\\\": {\\n    \\\"fullName\\\": \\\"Jane Doe\\\"\\n  },\\n  \\\"stripe\\\": {\\n    \\\"deposit\\\": {\\n      \\\"id\\\": \\\"card_1AB2CD3EFGHIJKLmnopqrs\\\",\\n      \\\"token\\\": \\\"tok_1AB2CD3EFGHIJKLmnopqrs\\\"\\n    },\\n    \\\"withdraw\\\": {\\n      \\\"id\\\": \\\"\\\",\\n      \\\"token\\\": \\\"\\\"\\n    }\\n  },\\n  \\\"verifications\\\": []\\n}\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "CONTACTS", "full_table_name": "INGEST.CONTACTS", "description": "The CONTACTS table stores information about users' contacts, including their identifiers, phone numbers, and encryption details for secure storage.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each contact record."}, {"name": "hash", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A hashed representation of the contact's phone number for privacy."}, {"name": "isAfriexUser", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the contact is a registered Afriex user.", "found_categorical_values": [true]}, {"name": "contacts", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "A list of contact names associated with the user.", "variant": "json_variant"}, {"name": "phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The phone number of the contact."}, {"name": "encryptedContact", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "An encrypted version of the contact's information for security purposes."}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"hash\": \"3f2a1b4c5d6e7f8g9h0i1j2k3l4m5n6o7p8q9r0s1t2u3v4w5x6y7z8a9b0c1d2\",\n  \"isAfriexUser\": false,\n  \"contacts\": \"[\\n  \\\"********90abcdef********\\\",\\n  \\\"abcdef********90abcdef12\\\",\\n  \\\"7890abcdef********90abcd\\\",\\n  \\\"4567890abcdef********90ab\\\",\\n  \\\"234567890abcdef********90\\\",\\n  \\\"abcdef********90abcdef34\\\",\\n  \\\"567890abcdef********90abc\\\",\\n  \\\"90abcdef********90abcdef\\\"\\n]\",\n  \"phone\": \"+14125551234\",\n  \"encryptedContact\": \"xYzAbC123D4EfGhIjKlMnOp==:QwErTyUiOpAsDfGhJkLzXcV==\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "OTC_VOLUMES", "full_table_name": "INGEST.OTC_VOLUMES", "description": "The OTC_VOLUMES table stores information about over-the-counter (OTC) trading volumes, including details about currency exchanges, transaction amounts, and user interactions.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each record in the OTC_VOLUMES table."}, {"name": "fromSymbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The symbol of the currency being exchanged (e.g., BTC for Bitcoin).", "found_categorical_values": ["USD"]}, {"name": "toSymbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The symbol of the currency being received in the exchange (e.g., USD for US Dollar).", "found_categorical_values": ["GHS", "NGN", "UGX"]}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country where the transaction is taking place.", "found_categorical_values": ["GH", "NG", "UG", "US"]}, {"name": "rate", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The exchange rate applied to the transaction between the fromSymbol and toSymbol."}, {"name": "fromAmount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of the fromSymbol currency being exchanged."}, {"name": "toAmount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of the toSymbol currency received from the exchange."}, {"name": "availableAmount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The amount of currency available for trading in the OTC market."}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the user initiating the transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction was recorded."}, {"name": "approvedBy", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the user who approved the transaction.", "variant": "json_variant"}, {"name": "submittedBy", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the user who submitted the transaction for processing.", "variant": "json_variant"}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the record was created."}], "status": "active", "sample_rows": "{\n  \"_id\": \"f3a1b2c3d4e5f67890123456\",\n  \"fromSymbol\": \"EUR\",\n  \"toSymbol\": \"JPY\",\n  \"country\": \"FR\",\n  \"rate\": \"134.5678901234567\",\n  \"fromAmount\": 123456.78,\n  \"toAmount\": 16543210.12,\n  \"availableAmount\": 7.************345,\n  \"userId\": null,\n  \"timestamp\": \"2024-05-15T14:45:00\",\n  \"approvedBy\": \"null\",\n  \"submittedBy\": \"{\\n  \\\"id\\\": \\\"abcdef********90abcdef12\\\",\\n  \\\"name\\\": \\\"<PERSON><PERSON><PERSON>\\\",\\n  \\\"role\\\": \\\"finance.manager\\\"\\n}\",\n  \"updatedAt\": \"2024-12-01T10:15:30.123000\",\n  \"createdAt\": \"2024-06-10T11:20:45.456000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "MEDICI_BALANCES", "full_table_name": "INGEST.MEDICI_BALANCES", "description": "The MEDICI_BALANCES table stores financial balance information for various accounts, tracking transactions and associated metadata to manage and analyze account balances over time.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "key", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique key representing the account or entity associated with the balance.", "found_categorical_values": ["\u0001", "\u0001\u0002l~»¿ìV06øË½§Â«ÙsVï", "\u0001\u0002L×", "\u0001\u0002ãAId9{þú\u001bg\u0010'¤¨\u0004", "\u0001\u0003Ø¶àýc¬2¢ÙQ¯AÙ\u0005Å", "\u0001\u0004Äµ\u0011¹ë÷\\5jUnX'\u0004;", "\u0001\u0005ñÊ\u0014»oe\rBÎçùÉ", "\u0001\u0006ñ­ÇöaÂ\u0017X§ðZJ3²Aø", "\u0001\u0007\u000fdñ\u001cÝC\u000bÆ\\Îs^½(<\fd", "\u0001\u0007/?ua¶ò'ìYºµO¦5m", "\u0001\u0007øÆ#ZÖû\u0012«\bìf3L+QJlk", "\u0001\t\"\u0017ÕÄ\u001agA³\u001cO;Ò+\u0003", "\u0001\t¯b++\u001cáÉ©\u001f\u001fÞ)xPÌÃ", "\u0001\tÂê-+[\u001e°îä¿î*Ñ·«Õ", "\u0001\n5¢ZærÐW¸kòÂº\u000f\u0010\u00043", "\u0001\nô½×È£n{ßÅg\u0003¡h\u0011þ\t>", "\u0001\u000bUúó¹ù<å\u0004\u0002\\¯C\u0018´", "\u0001\u000bÑ#oÓt`CÖ.#\u0019æbD", "\u0001\f\u0007OÀNÎÓ²", "\u0001\fl@È÷J#0çI Ö÷<PERSON>ß", "\u0001\rI[¶*l!'²y¨pñ|U:", "\u0001\ri\n_CB\u001eÌ8>ÂoNï\u000bXÓ6", "\u0001\rØÖ¾üQ}\u0001c\u001b\u0017~\trdêj", "\u0001\u000e|Å~\u001c\u000eý\u0018ÉN\u0017ôjÍÆÀ", "\u0001\u000fQÉ/\u001d}\n\u0007D±<\u0019^½\fî", "\u0001\u000fUáÏ»a\u001f*;N\u0001]Xè¯X\u001f", "\u0001\u000f»ÛãB\u0010\u0015±O/¨\nÐXÅ<0", "\u0001\u0011ÓHð\tÈÛEã;|y}»õ", "\u0001\u0012:\u0005­Y>æ[\n®ÄÓÕîê", "\u0001\u0012ð\bÁøy4PJ©·Ô~T\u0007>pZ", "\u0001\u0013\u0013Û\\åÝ@\u0015¯ù[;g>8ËÛ\u0019", "\u0001\u0014¾Ù3BÙ7×e\u0012 ¨a\u0019f´", "\u0001\u0016ð«]?=\u001dYtù3X\u0015©", "\u0001\u0017H° å\u0006=$à¶\u000ey:«²", "\u0001\u0017áJÔ\u0012w#hÆÚI\u0001]¹h\fc", "\u0001\u0018|Å#Å;ÈÃ¬`~cg°ò}ì", "\u0001\u0018¤ø´\u001a\u0006ÁÄ»§ß@SË¹ ¤", "\u0001\u0018¯ú\f¢Ü\n]­ßj \u00036óÕ", "\u0001\u0019\t[¼:\t Ä6ñìX\u0001d\u001d", "\u0001\u0019cLs7oZ§JÕÇ_:\u001c\u0010´\u0006", "\u0001\u0019w\u0014·ÝÛ&ì­â", "\u0001\u0019ûûiØÇÉìðK7\f-ÓÉÏ(>", "\u0001\u001c¥¿Yð", "\u0001\u001c³×\f¡ôBÙTD\u000eGoÀ¥Yaâ", "\u0001\u001d\u0006'ò\u00182îåHÄÉ3u\tÃp±S", "\u0001\u001d¼Ù6½Tc×i\bC#óûÁ", "\u0001\u001e \u0004±ÈÀ\u000bO¤\u000ehæ4B¶£\u000e\u001d", "\u0001\u001e xKbJ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>»Ú6\rGa", "\u0001\u001e\u0004´\u0019pØxí\u0017ºmpÉ{\u0003", "\u0001 \u0011, åMnä ã\u0006!®ª r®"]}, {"name": "<PERSON><PERSON><PERSON>", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The raw format of the key, used for internal processing or reference.", "found_categorical_values": ["AfriexBook;5c6eab975f70e3001430e6a1:GHS", "AfriexBook;5c6eab975f70e3001430e6a1:KES", "AfriexBook;5c6eab975f70e3001430e6a1:NGN", "AfriexBook;5c6eab975f70e3001430e6a1:USD", "AfriexBook;5c6ead695f70e3001430e6a3:ETB", "AfriexBook;5c6ead695f70e3001430e6a3:GHS", "AfriexBook;5c6ead695f70e3001430e6a3:KES", "AfriexBook;5c6ead695f70e3001430e6a3:NGN", "AfriexBook;5c6ead695f70e3001430e6a3:UGX", "AfriexBook;5c6ead695f70e3001430e6a3:USD", "AfriexBook;5c6ead695f70e3001430e6a3:XAF", "AfriexBook;5ca55ee7e42cd90014db6142:NGN", "AfriexBook;5cde917ef80e7700145c8dd3:NGN", "AfriexBook;5cde917ef80e7700145c8dd3:USD", "AfriexBook;5cdf2b688fc775001425f6f2:NGN", "AfriexBook;5cdf2b688fc775001425f6f2:USD", "AfriexBook;5cdf91728fc775001425f6f7:NGN", "AfriexBook;5cdf91728fc775001425f6f7:USD", "AfriexBook;5de2e4e2e7182400149f0ad6:USD", "AfriexBook;5e0e198b1a409e001464ef74:ETB", "AfriexBook;5e0e198b1a409e001464ef74:GHS", "AfriexBook;5e0e198b1a409e001464ef74:HTG", "AfriexBook;5e0e198b1a409e001464ef74:NGN", "AfriexBook;5e0e198b1a409e001464ef74:USD", "AfriexBook;5e3b01bcb8d2360014c2f004:NGN", "AfriexBook;5e3b01bcb8d2360014c2f004:USD", "AfriexBook;5e4033eaf4b82d00149d1d1b:NGN", "AfriexBook;5e4442db31e8dd00148b0133:USD", "AfriexBook;5e4521977996840014b9b9f1:NGN", "AfriexBook;5e4521977996840014b9b9f1:USD", "AfriexBook;5e516e25aede080014822e96:NGN", "AfriexBook;5e516e25aede080014822e96:USD", "AfriexBook;5e5d737acc386e0014349a57:USD", "AfriexBook;5e5e613ed554e2001422c5a4:NGN", "AfriexBook;5e5e613ed554e2001422c5a4:USD", "AfriexBook;5e66e8b9c8e4850014ba2aab:NGN", "AfriexBook;5e66e8b9c8e4850014ba2aab:USD", "AfriexBook;5e6ab94ff024d400143ce866:NGN", "AfriexBook;5e6ab94ff024d400143ce866:USD", "AfriexBook;5e6af862f024d400143ce872:NGN", "AfriexBook;5e6af862f024d400143ce872:USD", "AfriexBook;5e6b6693ad90800014a12cb6:NGN", "AfriexBook;5e6b6693ad90800014a12cb6:USD", "AfriexBook;5e6ec922207bb900147db7ea:NGN", "AfriexBook;5e6ec922207bb900147db7ea:USD", "AfriexBook;5e6f151c207bb900147db7f4:NGN", "AfriexBook;5e6f151c207bb900147db7f4:USD", "AfriexBook;5e762226eea5680015a11c93:NGN", "AfriexBook;5e762226eea5680015a11c93:USD", "AfriexBook;5e8395f768111e00158107e1:NGN"]}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The financial book or ledger to which this balance entry belongs.", "found_categorical_values": ["AfriexBook"]}, {"name": "account", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The account identifier that this balance is associated with.", "found_categorical_values": ["5c6eab975f70e3001430e6a1:GHS", "5c6eab975f70e3001430e6a1:KES", "5c6eab975f70e3001430e6a1:NGN", "5c6eab975f70e3001430e6a1:USD", "5c6ead695f70e3001430e6a3:ETB", "5c6ead695f70e3001430e6a3:GHS", "5c6ead695f70e3001430e6a3:KES", "5c6ead695f70e3001430e6a3:NGN", "5c6ead695f70e3001430e6a3:UGX", "5c6ead695f70e3001430e6a3:USD", "5c6ead695f70e3001430e6a3:XAF", "5ca55ee7e42cd90014db6142:NGN", "5cde917ef80e7700145c8dd3:NGN", "5cde917ef80e7700145c8dd3:USD", "5cdf2b688fc775001425f6f2:NGN", "5cdf2b688fc775001425f6f2:USD", "5cdf91728fc775001425f6f7:NGN", "5cdf91728fc775001425f6f7:USD", "5de2e4e2e7182400149f0ad6:USD", "5e0e198b1a409e001464ef74:ETB", "5e0e198b1a409e001464ef74:GHS", "5e0e198b1a409e001464ef74:HTG", "5e0e198b1a409e001464ef74:NGN", "5e0e198b1a409e001464ef74:USD", "5e3b01bcb8d2360014c2f004:NGN", "5e3b01bcb8d2360014c2f004:USD", "5e4033eaf4b82d00149d1d1b:NGN", "5e4442db31e8dd00148b0133:USD", "5e4521977996840014b9b9f1:NGN", "5e4521977996840014b9b9f1:USD", "5e516e25aede080014822e96:NGN", "5e516e25aede080014822e96:USD", "5e5d737acc386e0014349a57:USD", "5e5e613ed554e2001422c5a4:NGN", "5e5e613ed554e2001422c5a4:USD", "5e66e8b9c8e4850014ba2aab:NGN", "5e66e8b9c8e4850014ba2aab:USD", "5e6ab94ff024d400143ce866:NGN", "5e6ab94ff024d400143ce866:USD", "5e6af862f024d400143ce872:NGN", "5e6af862f024d400143ce872:USD", "5e6b6693ad90800014a12cb6:NGN", "5e6b6693ad90800014a12cb6:USD", "5e6ec922207bb900147db7ea:NGN", "5e6ec922207bb900147db7ea:USD", "5e6f151c207bb900147db7f4:NGN", "5e6f151c207bb900147db7f4:USD", "5e762226eea5680015a11c93:NGN", "5e762226eea5680015a11c93:USD", "5e8395f768111e00158107e1:NGN"]}, {"name": "meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the balance entry, such as status or type.", "variant": "json_variant"}, {"name": "transaction", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the transaction that affected this balance."}, {"name": "balance", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "The current balance amount for the associated account."}, {"name": "notes", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Any additional notes or comments regarding the balance entry.", "found_categorical_values": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record was created."}, {"name": "expireAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the balance record will expire or be considered outdated."}], "status": "active", "sample_rows": "{\n  \"_id\": \"7f3a1b2c4d5e6f7890a1b2c3d\",\n  \"key\": \"\\u0099~.\\u00eb\\u00ab\\u009a\\tr\\u00d7\\u00f4a\\u00bd\\u00c64\\u0085B6\\u00ca\\u0004\\u00f2\",\n  \"rawKey\": \"DummyBook;1234abcd5678efghijklmnop:EUR\",\n  \"book\": \"DummyBook\",\n  \"account\": \"1234abcd5678efghijklmnop:EUR\",\n  \"meta\": \"null\",\n  \"transaction\": \"7a8b9c0d1e2f3g4h5i6j7k8l9\",\n  \"balance\": 9.***************,\n  \"notes\": 15,\n  \"createdAt\": \"2024-09-01T10:20:30.601000\",\n  \"expireAt\": \"2024-09-03T10:20:30.601000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "MEDICI_TRANSACTIONS", "full_table_name": "INGEST.MEDICI_TRANSACTIONS", "description": "This table records all financial transactions related to the Medici accounting system, capturing details of credits and debits, along with associated metadata and timestamps.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each transaction record."}, {"name": "credit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Amount of money credited in the transaction.", "found_categorical_values": [0.0, 9.89530235528946e-09, 9.953510016202927e-09, 9.98261384665966e-09, 9.997165761888027e-09, 9.998984751291573e-09, 9.999894245993346e-09, 9.999951089412207e-09, 9.999979511121637e-09, 9.999993721976352e-09, 9.999997274690031e-09, 9.99999905104687e-09, 9.99999993922529e-09, 9.999999994736442e-09, 9.999999998205888e-09, 9.999999999940612e-09, 1e-08, 1.0000000001675335e-08, 1.000000000861423e-08, 1.0000000050247593e-08, 1.0000000161269895e-08, 1.000000082740371e-08, 1.0000007932831068e-08, 1.000012161966879e-08, 1.0000803740695119e-08, 1.000444171950221e-08, 1.0011717677116394e-08, 1.999999987845058e-08, 2e-08, 2.0001607481390238e-08, 2.347e-05, 5.073463755625e-05, 0.00011999, 0.00011999999998124622, 0.00019998999778181314, 0.00019999, 0.0001999900000555499, 0.00020000000006348273, 0.00020193, 0.00026779, 0.00031999999998788553, 0.00039999, 0.0005194805194, 0.00052, 0.0005999899876769632, 0.0005999899999551417, 0.********, 0.********00000119851, 0.********00000688285, 0.********99993946403]}, {"name": "debit", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Amount of money debited in the transaction.", "found_categorical_values": [0.0, 9.89530235528946e-09, 9.953510016202927e-09, 9.98261384665966e-09, 9.997165761888027e-09, 9.998984751291573e-09, 9.999894245993346e-09, 9.999951089412207e-09, 9.999979511121637e-09, 9.999993721976352e-09, 9.999997274690031e-09, 9.99999905104687e-09, 9.99999993922529e-09, 9.999999994736442e-09, 9.999999998205888e-09, 9.999999999940612e-09, 1e-08, 1.0000000001675335e-08, 1.000000000861423e-08, 1.0000000050247593e-08, 1.0000000161269895e-08, 1.000000082740371e-08, 1.0000007932831068e-08, 1.000012161966879e-08, 1.0000803740695119e-08, 1.000444171950221e-08, 1.0011717677116394e-08, 1.999999987845058e-08, 2e-08, 2.0001607481390238e-08, 2.347e-05, 5.073463755625e-05, 0.00011999, 0.00011999999998124622, 0.00019998999778181314, 0.00019999, 0.0001999900000555499, 0.00020000000006348273, 0.00020193, 0.00026779, 0.00031999999998788553, 0.00039999, 0.0005194805194, 0.00052, 0.0005999899876769632, 0.0005999899999551417, 0.********, 0.********00000119851, 0.********00000688285, 0.********99993946403]}, {"name": "meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the transaction, stored as a JSON object.", "variant": "json_variant"}, {"name": "datetime", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Date and time when the transaction occurred."}, {"name": "account_path", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Path to the account associated with the transaction, indicating its hierarchy.", "variant": "json_variant"}, {"name": "accounts", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "List of accounts affected by the transaction.", "found_categorical_values": ["5c6eab975f70e3001430e6a1:GHS", "5c6eab975f70e3001430e6a1:KES", "5c6eab975f70e3001430e6a1:NGN", "5c6eab975f70e3001430e6a1:USD", "5c6ead695f70e3001430e6a3:ETB", "5c6ead695f70e3001430e6a3:GHS", "5c6ead695f70e3001430e6a3:KES", "5c6ead695f70e3001430e6a3:NGN", "5c6ead695f70e3001430e6a3:UGX", "5c6ead695f70e3001430e6a3:USD", "5c6ead695f70e3001430e6a3:XAF", "5c70fffaf121d60014dd9701:USD", "5c8d5729d8b77c001444f0a9:USD", "5c909536a37fc60014208da8:USD", "5ca55ee7e42cd90014db6142:NGN", "5cab2e0ee7525f001400d7f1:USD", "5cab6ab0e7525f001400d7f3:USD", "5cc5593f49455c0014dc935a:USD", "5cde917ef80e7700145c8dd3:NGN", "5cde917ef80e7700145c8dd3:USD", "5cdf2b688fc775001425f6f2:NGN", "5cdf2b688fc775001425f6f2:USD", "5cdf91728fc775001425f6f7:NGN", "5cdf91728fc775001425f6f7:USD", "5ced57d16867a20014277fe9:NGN", "5d2f688b82545b001408a22a:NGN", "5d402a34a8fac500148e53a4:NGN", "5d68d5ba53a4ee001436d1de:USD", "5d6fdc9d92f8390014d24b96:NGN", "5dcc5049e3d8f40014d6d7dc:NGN", "5dcc5049e3d8f40014d6d7dc:USD", "5de2e4e2e7182400149f0ad6:USD", "5df922e04c933f0014f16e7b:USD", "5dfaa7ed6e5fde001423cef2:USD", "5dfdd44f65e22b0014af8b54:USD", "5dfe282e37ff420014b9afe4:USD", "5e0a8c9af4e76b0014fc2891:NGN", "5e0e198b1a409e001464ef74:ETB", "5e0e198b1a409e001464ef74:GHS", "5e0e198b1a409e001464ef74:HTG", "5e0e198b1a409e001464ef74:NGN", "5e0e198b1a409e001464ef74:USD", "5e16fd347a991000142f9cc6:USD", "5e3b01bcb8d2360014c2f004:NGN", "5e3b01bcb8d2360014c2f004:USD", "5e4033eaf4b82d00149d1d1b:NGN", "5e4442db31e8dd00148b0133:USD", "5e44b5f331e8dd00148b0135:NGN", "5e44bd257996840014b9b9ef:NGN", "5e44bd257996840014b9b9ef:USD"]}, {"name": "book", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Reference to the accounting book or ledger where the transaction is recorded.", "found_categorical_values": ["AfriexBook"]}, {"name": "memo", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Optional notes or comments regarding the transaction.", "found_categorical_values": ["5c6eab975f70e3001430e6a1 sent 1 USD ~ 710.4 NGN to 5cde917ef80e7700145c8dd3", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 718.22055 NGN to 5cde917ef80e7700145c8dd3", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 718.22055 NGN to 5cdf2b688fc775001425f6f2", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 718.481 NGN to 5ec1283291497c47af2781e0", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 720.0058 NGN to 5cde917ef80e7700145c8dd3", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 723.943 NGN to 5cde917ef80e7700145c8dd3", "5c6eab975f70e3001430e6a1 sent 1 USD ~ 723.943 NGN to 5cdf2b688fc775001425f6f2", "5c6eab975f70e3001430e6a1 sent 100 NGN ~ 100 NGN to 5cde917ef80e7700145c8dd3", "5c6eab975f70e3001430e6a1 sent 200 NGN ~ 0.***************04 USD to 5c6ead695f70e3001430e6a3", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 1 USD to 5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 1 USD to 5e0e198b1a409e001464ef74", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 10.5732 GHS to 5f6f464b1a3e780017495b4f", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 11.187 GHS to 5f47f3644e5c4a00141fc156", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 719.6005 NGN to 5cde917ef80e7700145c8dd3", "5c6ead695f70e3001430e6a3 sent 1 USD ~ 720.0058 NGN to 5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3 sent 2 USD ~ 22.374 GHS to 5f47f3644e5c4a00141fc156", "5c6ead695f70e3001430e6a3 sent 2.5 USD ~ 27.967499999999998 GHS to 5f6f464b1a3e780017495b4f", "5c6ead695f70e3001430e6a3 sent 3 USD ~ 3 USD to 5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3 sent 5 USD ~ 3591.10275 NGN to 5cde917ef80e7700145c8dd3", "5c6ead695f70e3001430e6a3 sent 5 USD ~ 3619.715 NGN to 5cde917ef80e7700145c8dd3", "5c6ead695f70e3001430e6a3 sent 5 USD ~ 5 USD to 5c6eab975f70e3001430e6a1", "5ca55ee7e42cd90014db6142 sent 1000 NGN ~ 1.305263158 USD to 61e57177e7c21e49f58bf889", "5cde917ef80e7700145c8dd3 sent 10 NGN ~ 10 NGN to 5c6eab975f70e3001430e6a1", "5cde917ef80e7700145c8dd3 sent 50 NGN ~ 50 NGN to 5c6eab975f70e3001430e6a1", "5cde917ef80e7700145c8dd3 sent 730 NGN ~ 0.96832252476 USD to 5c6eab975f70e3001430e6a1", "5cde917ef80e7700145c8dd3 sent 753.62 NGN ~ 0.9999880152925601 USD to 5c6eab975f70e3001430e6a1", "5cdf91728fc775001425f6f7 sent 1 NGN ~ 0.1639328627 KES to 636a08730c9f0bc8a2adfed5", "5cdf91728fc775001425f6f7 sent 10 NGN ~ 1.6393286269999998 KES to 6213cce5e4df5a67ed0ffa38", "5cdf91728fc775001425f6f7 sent 500 NGN ~ 500 NGN to 640c45082e1f769914d37dfd", "5e0e198b1a409e001464ef74 sent 1 USD ~ 1 USD to 5c6eab975f70e3001430e6a1", "5e0e198b1a409e001464ef74 sent 1 USD ~ 1 USD to 6386c7143476f5e74df3b6ea", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.01 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.2335 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.25 GHS to 621e687c71826f2af541da29", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.339 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.34 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.3596 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 10.41 GHS to 621e687c71826f2af541da29", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.186 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.3 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.4022 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.417 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.76 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 11.809 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 12.0638 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 12.276 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 1 USD ~ 12.375 GHS to 621e687c71826f2af541da29", "5e0e198b1a409e001464ef74 sent 1 USD ~ 12.375 GHS to 63b8f08e67e5257c071b20f4", "5e0e198b1a409e001464ef74 sent 100 USD ~ 1318.1 GHS to 6254277665670c138e3f42b5", "5e0e198b1a409e001464ef74 sent 1000 USD ~ 12375 GHS to 621e687c71826f2af541da29"]}, {"name": "_journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the journal entry associated with this transaction."}, {"name": "timestamp", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was created or last updated."}, {"name": "voided", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Boolean flag indicating whether the transaction has been voided.", "found_categorical_values": [true]}, {"name": "void_reason", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Reason for voiding the transaction, if applicable.", "found_categorical_values": ["ALPAY Payout Failed", "ALPAY WITHDRAW transaction failed", "ALPAY Withdrawal Failed", "Admin Initiated Refund", "Alpay Payout Failed", "CELLULANT Payout Failed", "CELLULANT WITHDRAW transaction failed", "CELLULANT Withdrawal Failed", "CHAPA Withdrawal Failed", "Cellulant disbursement failed", "DLOCAL WITHDRAW transaction failed", "DLOCAL Withdrawal Failed", "Error creating payment intent", "FINCRA Withdrawal Failed", "Fincra Payout Failed", "JUICYWAY WITHDRAW transaction failed", "JUICYWAY Withdrawal Failed", "MONIEPOINT Payout Failed", "MONIEPOINT WITHDRAW transaction failed", "MONIEPOINT Withdrawal Failed", "Moniepoint Payout Failed", "STRIPE Payout Failed", "STRIPE Withdrawal Failed", "Stripe Charge Failed", "Stripe Payout Failed", "Transaction 63d5ba22507980130b822dda Reversed", "Transaction 643595eb4123e5a9b1f53444 Reversed", "Transaction 646422c08892bf619b9d7754 Reversed", "Transaction 646593175fe7ef90cf2f09dc Reversed", "Transaction 6468c66084ab344fc2bd86fc Reversed", "Transaction 646a75de5e7185a0229c1693 Reversed", "Transaction 646a9e5a5e7185a0229c97bf Reversed", "Transaction 646ad96f5e7185a0229d3e99 Reversed", "Transaction 646ad9f15e7185a0229d417a Reversed", "Transaction 646adaa95e7185a0229d4274 Reversed", "Transaction 646adadd5e7185a0229d4352 Reversed", "Transaction 646adb775e7185a0229d45fc Reversed", "Transaction 646adbd75e7185a0229d473e Reversed", "Transaction 646adc015e7185a0229d4893 Reversed", "Transaction 646adc415e7185a0229d490f Reversed", "Transaction 646adcad5e7185a0229d49bc Reversed", "Transaction 646adcbf5e7185a0229d49df Reversed", "Transaction 646add575e7185a0229d4bf3 Reversed", "Transaction 646addcf5e7185a0229d4cf2 Reversed", "Transaction 646ade755e7185a0229d4d3a Reversed", "Transaction 646adebf5e7185a0229d4da5 Reversed", "Transaction 646adf335e7185a0229d4e9a Reversed", "Transaction 646adfb75e7185a0229d4fbd Reversed", "Transaction 646ae0695e7185a0229d509c Reversed", "Transaction 646ae14e5e7185a0229d512b Reversed"]}, {"name": "_original_journal", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Reference to the original journal entry before any modifications."}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"credit\": 45.0,\n  \"debit\": 10.0,\n  \"meta\": \"{\\n  \\\"channel\\\": \\\"CREDIT_CARD\\\",\\n  \\\"destinationAmount\\\": \\\"12345.67890\\\",\\n  \\\"destinationAsset\\\": \\\"EUR\\\",\\n  \\\"paymentMethodId\\\": \\\"a1b2c3d4e5f67890abcdef13\\\",\\n  \\\"processor\\\": \\\"PAYPAL\\\",\\n  \\\"receiverId\\\": \\\"abcdef********90abcdef14\\\",\\n  \\\"senderId\\\": \\\"********90abcdef********\\\",\\n  \\\"sourceAmount\\\": 45,\\n  \\\"sourceAsset\\\": \\\"GBP\\\",\\n  \\\"transactionId\\\": \\\"Payment-********90abcdef********-*************\\\"\\n}\",\n  \"datetime\": \"2024-11-20T10:15:30.123000\",\n  \"account_path\": \"[\\n  \\\"abcdef********90abcdef14\\\",\\n  \\\"GBP\\\"\\n]\",\n  \"accounts\": \"abcdef********90abcdef14:GBP\",\n  \"book\": \"SampleBook\",\n  \"memo\": \"deposit\",\n  \"_journal\": \"a1b2c3d4e5f67890abcdef15\",\n  \"timestamp\": \"2024-11-20T10:15:30.123000\",\n  \"voided\": false,\n  \"void_reason\": \"Transaction Successful\",\n  \"_original_journal\": null\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "REVIEWS", "full_table_name": "INGEST.REVIEWS", "description": "The REVIEWS table stores user-generated reviews for products or services, allowing users to provide feedback and ratings based on their experiences.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each review entry."}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who submitted the review."}, {"name": "rating", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The numerical rating given by the user, typically on a scale from 1 to 5.", "found_categorical_values": [1.0, 2.0, 3.0]}, {"name": "comment", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The textual feedback provided by the user regarding their experience."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the review was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the review was last updated."}], "status": "active", "sample_rows": "{\n  \"_id\": \"7f3b2c1d4e5f6a7b8c9d0e1f\",\n  \"userId\": \"4a5b6c7d8e9f0a1b2c3d4e5f\",\n  \"rating\": 4.5,\n  \"comment\": \"The service was excellent and very helpful!\",\n  \"createdAt\": \"2024-02-15T10:30:45.123000\",\n  \"updatedAt\": \"2024-02-15T10:30:45.123000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "ACCOUNTS", "full_table_name": "INGEST.ACCOUNTS", "description": "The ACCOUNTS table stores user account information, including personal details, authentication credentials, and account activity history. It is essential for managing user access and roles within the application.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each account record."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the account record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the account record was created."}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier assigned to each user, often used for referencing in other tables."}, {"name": "referrer", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the user who referred this account, if applicable."}, {"name": "email", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Email address associated with the account, used for communication and login."}, {"name": "phone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Phone number associated with the account, used for communication and verification."}, {"name": "country", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Country of residence for the account holder.", "found_categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}, {"name": "userName", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Chosen username for the account, used for login and display purposes."}, {"name": "isAdmin", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Boolean flag indicating whether the user has administrative privileges.", "found_categorical_values": [false, true]}, {"name": "password", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Hashed password for account authentication.", "variant": "json_variant"}, {"name": "history", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Record of past activities or changes made to the account.", "variant": "json_variant"}, {"name": "role", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Role assigned to the user, defining their permissions and access levels.", "found_categorical_values": ["compliance.manager", "compliance.member", "customer.support.manager", "customer.support.member", "customer_care", "engineering.manager", "engineering.member", "finance.manager", "finance.member", "growth.manager", "growth.member", "operations.manager", "operations.member", "product.eng.qa.design.member", "user"]}, {"name": "devices", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of devices associated with the account for security and access tracking.", "variant": "json_variant"}, {"name": "otp", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "One-time password used for two-factor authentication.", "variant": "json_variant"}, {"name": "permissions", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Specific permissions granted to the user, defining their capabilities within the application.", "variant": "json_variant"}, {"name": "attempts", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Count of failed login attempts to monitor and prevent unauthorized access.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"b1c2d3e4f5g6h7i8j9k0l1m2\",\n  \"updatedAt\": \"2024-07-12T12:34:56.789000\",\n  \"createdAt\": \"2022-12-01T09:15:45.123000\",\n  \"userId\": \"a1b2c3d4e5f6g7h8i9j0k1l2\",\n  \"referrer\": \"randomuser@1234\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+15551234567\",\n  \"country\": \"CA\",\n  \"userName\": \"randomuser1234\",\n  \"isAdmin\": true,\n  \"password\": \"{\\n  \\\"hash\\\": \\\"$2b$10$randomhashvalue********90\\\",\\n  \\\"isDeactivated\\\": true,\\n  \\\"version\\\": 2\\n}\",\n  \"history\": \"[\\n  {\\n    \\\"_id\\\": {\\n      \\\"$oid\\\": \\\"randomoid********90\\\"\\n    },\\n    \\\"password\\\": {\\n      \\\"hash\\\": \\\"$2b$10$anotherhashvalue0987654321\\\",\\n      \\\"isDeactivated\\\": true,\\n      \\\"version\\\": 2\\n    },\\n    \\\"updatedAt\\\": {\\n      \\\"$date\\\": \\\"2024-02-20T11:22:33.444Z\\\"\\n    }\\n  },\\n  {\\n    \\\"_id\\\": {\\n      \\\"$oid\\\": \\\"anotherrandomoid0987654321\\\"\\n    },\\n    \\\"password\\\": {\\n      \\\"hash\\\": \\\"$2b$10$randomhashvalue********90\\\",\\n      \\\"isDeactivated\\\": true,\\n      \\\"version\\\": 2\\n    },\\n    \\\"updatedAt\\\": {\\n      \\\"$date\\\": \\\"2024-04-07T15:30:45.678Z\\\"\\n    }\\n  }\\n]\",\n  \"role\": \"admin\",\n  \"devices\": \"[\\n  {\\n    \\\"appVersion\\\": \\\"12.0.0\\\",\\n    \\\"createdAt\\\": \\\"Tue Feb 20 2024 11:22:33 GMT+0000 (Coordinated Universal Time)\\\",\\n    \\\"deviceCountry\\\": \\\"CA\\\",\\n    \\\"deviceId\\\": \\\"A1B2C3D4-E5F6-7G8H-9I0J-K1L2M3N4O5P6\\\",\\n    \\\"deviceIp\\\": \\\"***********\\\",\\n    \\\"deviceName\\\": \\\"Samsung\\\",\\n    \\\"deviceToken\\\": \\\"\\\",\\n    \\\"deviceType\\\": \\\"android\\\",\\n    \\\"isDeactivated\\\": true,\\n    \\\"isDefault\\\": false,\\n    \\\"updatedAt\\\": {\\n      \\\"$date\\\": \\\"2024-02-20T11:22:33.999Z\\\"\\n    }\\n  },\\n  {\\n    \\\"appVersion\\\": \\\"12.0.1\\\",\\n    \\\"createdAt\\\": \\\"Thu Jan 10 2024 14:30:00 GMT+0000 (Coordinated Universal Time)\\\",\\n    \\\"deviceCountry\\\": \\\"CA\\\",\\n    \\\"deviceId\\\": \\\"A1B2C3D4-E5F6-7G8H-9I0J-K1L2M3N4O5P6\\\",\\n    \\\"deviceIp\\\": \\\"2601:145:8000:1dd0:91e4:9127:43bb:67fc\\\",\\n    \\\"deviceName\\\": \\\"Samsung\\\",\\n    \\\"deviceToken\\\": \\\"randomtoken********90\\\",\\n    \\\"deviceType\\\": \\\"android\\\",\\n    \\\"isDeactivated\\\": true,\\n    \\\"isDefault\\\": false,\\n    \\\"updatedAt\\\": {\\n      \\\"$date\\\": \\\"2024-01-10T14:30:00.123Z\\\"\\n    }\\n  },\\n  {\\n    \\\"appVersion\\\": \\\"12.0.2\\\",\\n    \\\"createdAt\\\": \\\"Fri Jan 05 2024 09:00:00 GMT+0000 (Coordinated Universal Time)\\\",\\n    \\\"deviceCountry\\\": \\\"CA\\\",\\n    \\\"deviceId\\\": \\\"A1B2C3D4-E5F6-7G8H-9I0J-K1L2M3N4O5P6\\\",\\n    \\\"deviceIp\\\": \\\"********\\\",\\n    \\\"deviceName\\\": \\\"Samsung\\\",\\n    \\\"deviceToken\\\": \\\"randomtoken0987654321\\\",\\n    \\\"deviceType\\\": \\\"android\\\",\\n    \\\"isDeactivated\\\": true,\\n    \\\"isDefault\\\": false,\\n    \\\"updatedAt\\\":", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "RATES_OTCS", "full_table_name": "INGEST.RATES_OTCS", "description": "The RATES_OTCS table stores exchange rate information for over-the-counter (OTC) transactions between different currency symbols. It facilitates the tracking and management of currency conversions and their associated metadata.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each record in the table."}, {"name": "fromSymbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol from which the conversion is made.", "found_categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "GNF", "HTG", "INR", "KES", "MGA", "MWK", "MXN", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "toSymbol", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency symbol to which the conversion is made.", "found_categorical_values": ["CAD", "CNY", "EGP", "ETB", "EUR", "GBP", "GHS", "GNF", "HTG", "INR", "KES", "MGA", "MWK", "MXN", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "inverse", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A boolean indicating whether the rate is the inverse of the standard conversion.", "found_categorical_values": ["0", "0.0001129935842", "0.000112994", "0.0001153197037", "0.0001153568055", "0.0001154734411", "0.000115499702", "0.0001155268022", "0.0001155401502", "0.000115548828", "0.0001155700208", "0.0001155958846", "0.0001156004147", "0.0001156093021", "0.0001156228964", "0.0001156496934", "0.0001156953999", "0.0001157167001", "0.0001157434066", "0.0001159352947", "0.0001160131568", "0.0001161964217", "0.0001164196939", "0.0001171363581", "0.0002113981216", "0.0002116402116", "0.0002118644068", "0.0002124459776", "0.0002127659574", "0.0002131462194", "0.0002132196162", "0.000213229482", "0.0002133229118", "0.0002133815405", "0.0002134426615", "0.0002137140749", "0.000213829509", "0.0002139473108", "0.0002141979849", "0.0002144971319", "0.0002147621488", "0.0002148873066", "0.0002164937964", "0.0002172153127", "0.0002583979328", "0.000258398", "0.0002628120894", "0.0002666666667", "0.00027027", "0.0002702702703"]}, {"name": "value", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The exchange rate value for the conversion from fromSymbol to toSymbol."}, {"name": "source", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The source from which the exchange rate data was obtained.", "found_categorical_values": ["afriex", "binance", "bybit", "cellulant", "chapa", "dlocal", "ejara", "google", "stripe", "terrapay", "zeepay"]}, {"name": "batchId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "An identifier for the batch of transactions associated with this rate.", "found_categorical_values": ["651d93912e1e0cfd7689cde8", "651ed9b42e1e0cfd768b1956", "651efd692e1e0cfd768b5019", "651f136a2e1e0cfd768b6db7", "651fdae72e1e0cfd768c11c2", "65211da104552872854b14ec", "6524157912e58e7a1aadf4c1", "6524262b12e58e7a1aae054f", "6524268e12e58e7a1aae0655", "6524676712e58e7a1aae5706", "652673f361b7957ed4a376ea", "6527cfb361b7957ed4a560bb", "65294076be199b94c32a1323", "6529658094158902ffbc9dfc", "652afc710a6fbb3c9faf11af", "652c37b46b1f2aeaea44ec68", "652e8bbe3c414d178789d582", "6530ef561be3a36881501474", "65323539487e11eeb810c691", "653255e9487e11eeb810f5ac", "65325a6f487e11eeb810fb68", "6533d2734310984c9d9e6b5d", "653653a60e4c17f79cc0545b", "6536585d0e4c17f79cc05936", "65377a4d44648087844a8d5b", "6538cd6e867d15b1b2e4844e", "65397a68dcd49c066fb7dae3", "653a35e87f7cd4e1101c76a3", "653b65dcfda80edc8b4cc52c", "653b9912fda80edc8b4cf432", "653b9e69fda80edc8b4cfaf4", "653c3c3efda80edc8b4e5089", "653e2928902c1ca70b605608", "653ff5b139ad4dbe1336ae4c", "65423c7ae3d0e1412badf62e", "654294ace3d0e1412bae8680", "6543b78b1613d23a434b0988", "654420e51613d23a434ba4b8", "654529631613d23a434cb9f0", "6546789cfe17732fe44044ce", "6547a4e9cf587034be1bbdfa", "6548d5aa09e1e9e4fa7c6565", "6548d90709e1e9e4fa7c69ee", "654a18498e19cf90da0a66d9", "654b6883928b15fb8abae3b8", "654bbcc6419fb049fe0937f2", "654c13c3e23f62262c446cec", "654ccd83e23f62262c45fbbf", "654cfa17e23f62262c465a9b", "654d28d6001febdf407b39e9"]}, {"name": "OTCVolumeId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "An identifier linking to the specific OTC volume transaction.", "found_categorical_values": ["01022021_300k", "01022024_100K", "01022024_1M", "01022024_300K", "01022024_500K", "02012024_1M", "02022024_1M", "02022024_300k", "02022024_70k", "04022024_300K", "07022024_100K", "07022024_176K", "07022024_200K", "07022024_200k", "08022020_200k", "08022024_200K", "08022024_200k", "09022024_1.5M", "09022024_197K", "11022024_186117205", "11022024_369740643", "12022024_400k", "13022024_300K", "13022024_300k", "14022024_400K", "14022024_400k", "15022024_200k", "15022024_220K", "15022024_220k", "15022024_250k", "15022024_300K", "16022024_200k", "16022024_2M", "17022024_1861931776", "19022024", "19022024_193625360", "19022024_528644126", "20022024_", "20022024_161735.93", "20022024_564981523", "20022024_92240436", "21022024", "21022024_222305067", "22022024", "22022024_150k", "23022024", "24022024", "25022024", "26012024_500k", "26022024"]}, {"name": "volumeId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "An identifier for the volume of transactions related to this exchange rate."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp of when the record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp of when the record was created."}], "status": "active", "sample_rows": "{\n  \"_id\": \"72f30ecde16a8f054bd542f8\",\n  \"fromSymbol\": \"EUR\",\n  \"toSymbol\": \"JPY\",\n  \"inverse\": \"0.0075\",\n  \"value\": \"42\",\n  \"source\": \"examplepay\",\n  \"batchId\": \"72f30ecde16a8f054bd542ee\",\n  \"OTCVolumeId\": \"19022024_203725360\",\n  \"volumeId\": null,\n  \"updatedAt\": \"2024-03-01T10:25:30.875000\",\n  \"createdAt\": \"2024-03-01T10:25:30.875000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "REWARDS_CARDS", "full_table_name": "INGEST.REWARDS_CARDS", "description": "The REWARDS_CARDS table stores information about reward cards issued to users, including their status and redemption details.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each reward card record."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the reward card was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the reward card record was last updated."}, {"name": "rewardId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the specific reward associated with the card."}, {"name": "hash", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A hash value used for secure identification of the reward card."}, {"name": "isDeactivated", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the reward card is deactivated.", "found_categorical_values": [false, true]}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user to whom the reward card is issued."}, {"name": "redeemedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the reward card was redeemed."}, {"name": "redeemed<PERSON><PERSON>", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who redeemed the reward card.", "variant": "json_variant"}, {"name": "unique_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique string that serves as an additional identifier for the reward card."}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"createdAt\": \"2024-12-01T12:00:00.000000\",\n  \"updatedAt\": \"2025-04-01T08:00:00.000000\",\n  \"rewardId\": \"a1b2c3d4e5f67890abcdef13\",\n  \"hash\": \"randomHashValue********90==\",\n  \"isDeactivated\": false,\n  \"userId\": \"a1b2c3d4e5f67890abcdef14\",\n  \"redeemedAt\": \"2025-04-01T08:00:00.000000\",\n  \"redeemedBy\": \"{\\n  \\\"name\\\": \\\"Random User\\\",\\n  \\\"redeemedAt\\\": {\\n    \\\"$date\\\": \\\"2025-04-01T08:00:00.000Z\\\"\\n  },\\n  \\\"rewardTier\\\": \\\"gold\\\",\\n  \\\"userId\\\": \\\"a1b2c3d4e5f67890abcdef14\\\"\\n}\",\n  \"unique_id\": \"a1b2c3d4e5f67890abcdef14567a1b2c3d4e5f67890abcdef14\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "REWARDS", "full_table_name": "INGEST.REWARDS", "description": "The REWARDS table stores information about various rewards that can be earned or redeemed by users. It includes details such as point values, monetary values, and eligibility criteria for different countries.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each reward entry in the table."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the reward was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating the last time the reward details were updated."}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The category or type of the reward (e.g., discount, gift card, etc.).", "found_categorical_values": ["coupon", "gift_card"]}, {"name": "pointsValue", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The number of points required to redeem the reward.", "found_categorical_values": [400.0, 500.0, 600.0, 700.0, 750.0, 1000.0, 1500.0, 2000.0, 2200.0, 2500.0, 2700.0, 3000.0, 3500.0, 4000.0, 5000.0]}, {"name": "dollarValue", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The monetary value of the reward in dollars.", "found_categorical_values": [4.0, 5.0, 6.0, 7.0, 10.0, 15.0, 20.0, 22.0, 25.0, 27.0, 30.0, 35.0, 40.0, 50.0]}, {"name": "description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A brief description of the reward and its benefits."}, {"name": "name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The name of the reward."}, {"name": "imageLink", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A URL link to an image representing the reward."}, {"name": "supportedCountries", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "A list of countries where the reward can be redeemed.", "variant": "json_variant"}, {"name": "ttl", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The time-to-live for the reward, indicating how long it remains valid."}, {"name": "isDeactivated", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the reward is currently active or deactivated.", "found_categorical_values": [false, true]}, {"name": "tier", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The tier level associated with the reward, indicating its exclusivity or rank.", "found_categorical_values": ["blue", "gold", "member"]}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"createdAt\": \"2024-11-15T09:45:12.345000\",\n  \"updatedAt\": \"2025-05-10T14:22:33.456000\",\n  \"type\": \"coupon\",\n  \"pointsValue\": 1500.0,\n  \"dollarValue\": 15.0,\n  \"description\": \"\\n15% Discount\",\n  \"name\": \"ExampleCorp\",\n  \"imageLink\": \"reward_card_image_b8c9d0e1-2f3a-4b5c-6d7e-8f9a0b1c2d3e\",\n  \"supportedCountries\": \"[\\n  \\\"DE\\\",\\n  \\\"CA\\\",\\n  \\\"JP\\\",\\n  \\\"AU\\\",\\n  \\\"ES\\\",\\n  \\\"NL\\\",\\n  \\\"SE\\\",\\n  \\\"NO\\\",\\n  \\\"FI\\\",\\n  \\\"DK\\\",\\n  \\\"CZ\\\",\\n  \\\"BG\\\"\\n]\",\n  \"ttl\": \"2025-05-30T23:00:00\",\n  \"isDeactivated\": false,\n  \"tier\": \"premium\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "SCHEDULED_TRANSACTIONS", "full_table_name": "INGEST.SCHEDULED_TRANSACTIONS", "description": "This table stores information about transactions that are scheduled to occur at specified intervals. It tracks the details of each transaction, including timing, user association, and status, allowing for automated processing of recurring transactions.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each scheduled transaction."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was created."}, {"name": "userId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the user associated with the scheduled transaction."}, {"name": "interval", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Frequency at which the transaction is scheduled to occur (e.g., daily, weekly, monthly).", "found_categorical_values": ["MONTHLY", "TWICE_A_MONTH", "WEEKLY"]}, {"name": "nextRunDate", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction is next scheduled to run."}, {"name": "lastRunDate", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the transaction was last executed."}, {"name": "recurrenceCount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The number of times the transaction has been executed.", "found_categorical_values": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 17, 19, 20, 22, 33, 35, 36, 37, 41]}, {"name": "startDate", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the scheduled transaction should start."}, {"name": "endDate", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The date and time when the scheduled transaction should end or stop recurring."}, {"name": "transactionRequest", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Details of the transaction to be executed, including parameters and amounts.", "variant": "json_variant"}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Current status of the scheduled transaction (e.g., active, completed, canceled).", "found_categorical_values": ["ACTIVE", "CANCELLED", "PAUSED"]}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"updatedAt\": \"2025-01-15T00:00:00.340000\",\n  \"createdAt\": \"2024-12-15T15:18:48.402000\",\n  \"userId\": \"b2c3d4e5f67890abcdef1234\",\n  \"interval\": \"WEEKLY\",\n  \"nextRunDate\": \"2025-01-22T00:00:00.339000\",\n  \"lastRunDate\": \"2025-01-15T00:00:00.339000\",\n  \"recurrenceCount\": 5,\n  \"startDate\": null,\n  \"endDate\": null,\n  \"transactionRequest\": \"{\\n  \\\"_id\\\": {\\n    \\\"$oid\\\": \\\"a1b2c3d4e5f67890abcdef34\\\"\\n  },\\n  \\\"amount\\\": 25,\\n  \\\"destinationAsset\\\": \\\"EUR\\\",\\n  \\\"destinationChannel\\\": \\\"WALLET\\\",\\n  \\\"destinationPaymentMethodId\\\": {\\n    \\\"$oid\\\": \\\"a1b2c3d4e5f67890abcdef56\\\"\\n  },\\n  \\\"sourceAsset\\\": \\\"GBP\\\",\\n  \\\"sourceChannel\\\": \\\"BANK\\\",\\n  \\\"sourcePaymentMethodId\\\": {\\n    \\\"$oid\\\": \\\"a1b2c3d4e5f67890abcdef78\\\"\\n  }\\n}\",\n  \"status\": \"INACTIVE\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "REFERRALS_TIERS", "full_table_name": "INGEST.REFERRALS_TIERS", "description": "This table stores information about different referral tiers in a referral program, including their thresholds, bonuses, and settings for automation and payouts.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each referral tier."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the referral tier was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating the last time the referral tier was updated."}, {"name": "name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The name of the referral tier."}, {"name": "cumulativeThreshold", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The cumulative number of referrals required to reach this tier.", "found_categorical_values": [5.0, 10.0, 20.0, 30.0, 50.0, 100.0, 150.0]}, {"name": "transactionWindow", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The time period during which referrals must be made to qualify for this tier.", "found_categorical_values": [0.0, 30.0, 200.0, 300.0, 357.0, 358.0, 360.0, 361.0, 362.0, 363.0, 364.0, 365.0, 366.0, 368.0, 369.0, 3654.0]}, {"name": "bonusAmount<PERSON>er<PERSON><PERSON><PERSON><PERSON>", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded for each successful referral made by a referree.", "found_categorical_values": [1.0, 5.0, 10.0, 15.0, 20.0]}, {"name": "bonusAmount<PERSON>er<PERSON><PERSON>errer", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The bonus amount awarded for each successful referral made by a referrer.", "found_categorical_values": [1.0, 2.0, 5.0, 10.0, 15.0, 20.0]}, {"name": "referralCode", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The unique code associated with this referral tier."}, {"name": "currencyCode", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The currency in which bonuses are awarded.", "found_categorical_values": ["USD"]}, {"name": "shouldAutomatePayout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether payouts for this tier should be automated.", "found_categorical_values": [true]}, {"name": "shouldSkipReferrerPayout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether to skip payouts to the referrer for this tier.", "found_categorical_values": [false, true]}, {"name": "shouldSkipReferreePayout", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether to skip payouts to the referree for this tier.", "found_categorical_values": [false, true]}, {"name": "isDeactivated", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether this referral tier is currently deactivated.", "found_categorical_values": [false, true]}, {"name": "admin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the admin who created or manages this referral tier.", "variant": "json_variant"}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of referral tier, which may define its characteristics or eligibility.", "found_categorical_values": ["referral"]}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"createdAt\": \"2024-08-15T12:34:56.789000\",\n  \"updatedAt\": \"2024-08-15T12:34:56.789000\",\n  \"name\": \"RandomName42\",\n  \"cumulativeThreshold\": 15.0,\n  \"transactionWindow\": 45.0,\n  \"bonusAmountPerReferree\": 10.0,\n  \"bonusAmountPerReferrer\": 7.0,\n  \"referralCode\": \"randomcode42\",\n  \"currencyCode\": \"EUR\",\n  \"shouldAutomatePayout\": false,\n  \"shouldSkipReferrerPayout\": false,\n  \"shouldSkipReferreePayout\": true,\n  \"isDeactivated\": \"null\",\n  \"admin\": \"{\\n  \\\"id\\\": \\\"abcdef********90abcdef12\\\",\\n  \\\"name\\\": \\\"JaneDoe\\\",\\n  \\\"role\\\": \\\"marketing.manager\\\"\\n}\",\n  \"type\": \"referral\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "REFERRALS", "full_table_name": "INGEST.REFERRALS", "description": "The REFERRALS table stores information about referral transactions between users, tracking the details of who referred whom, the amounts involved, and the status of each referral.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each referral record."}, {"name": "fromUserName", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who made the referral."}, {"name": "toUserName", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The username of the user who is being referred."}, {"name": "to<PERSON>ame", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The full name of the user who is being referred."}, {"name": "toUserId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for the user who is being referred."}, {"name": "fromUserId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for the user who made the referral."}, {"name": "cumulativeTransactionAmount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "Total amount of transactions associated with the referral."}, {"name": "fromAmount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount credited to the referring user.", "found_categorical_values": [0.0, 1.0, 2.0, 5.0, 10.0, 15.0, 20.0]}, {"name": "toAmount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The amount credited to the referred user.", "found_categorical_values": [0.0, 5.0, 10.0, 15.0, 20.0]}, {"name": "isFromFulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the referral from the referring user has been fulfilled.", "found_categorical_values": [true]}, {"name": "isToFulfilled", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the referral to the referred user has been fulfilled.", "found_categorical_values": [true]}, {"name": "isFromStaff", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates if the referring user is a staff member.", "found_categorical_values": [false, true]}, {"name": "yearFulfilled", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The year in which the referral was fulfilled.", "found_categorical_values": [2023, 2024, 2025]}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Current status of the referral (e.g., pending, completed, cancelled).", "found_categorical_values": ["joined", "qualified", "resolved", "transacted"]}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the referral record was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the referral record was last updated."}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890********\",\n  \"fromUserName\": \"randomuser1234\",\n  \"toUserName\": \"anotheruser5678\",\n  \"toName\": \"<PERSON>\",\n  \"toUserId\": \"a1b2c3d4e5f6789012345679\",\n  \"fromUserId\": \"a1b2c3d4e5f6789012345680\",\n  \"cumulativeTransactionAmount\": 1500.0,\n  \"fromAmount\": 10.0,\n  \"toAmount\": 10.0,\n  \"isFromFulfilled\": false,\n  \"isToFulfilled\": true,\n  \"isFromStaff\": true,\n  \"yearFulfilled\": 2024,\n  \"status\": \"pending\",\n  \"createdAt\": \"2024-01-15T12:30:45.123000\",\n  \"updatedAt\": \"2024-02-20T15:45:30.456000\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "SECURITYNOTES", "full_table_name": "INGEST.SECURITYNOTES", "description": "The SECURITYNOTES table is designed to store security-related notes associated with user accounts. It tracks changes, reasons for actions taken, and administrative details to ensure accountability and transparency in user management.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each security note entry."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the security note was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the security note was created."}, {"name": "user", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user associated with the security note."}, {"name": "note", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The content of the security note providing details about the user's situation."}, {"name": "reason", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The reason for creating the security note, explaining the context of the action.", "found_categorical_values": ["\tAge limit exceeded. https://afriex.freshdesk.com/a/tickets/476786", "\tDeactivation requested https://afriex.freshdesk.com/a/tickets/488105", "ACCOUNT_TAKEOVER_CASE", "ACH resolution", "ACH_FRAUD", "Account deactivation request-https://afriex.freshdesk.com/a/tickets/507772", "Account deactivation requested by the user https://afriex.freshdesk.com/a/tickets/494768", "Account deactivation requested https://afriex-org.freshchat.com/a/***************/inbox/open/0/conversation/***************", "Account deactivation requested, User claims they did not open the account. https://afriex.freshdesk.com/a/tickets/490843", "Account <NAME_EMAIL>\n\n", "Account takeover report https://afriex.freshdesk.com/a/tickets/506804", "Account was hacked https://afriex.freshdesk.com/a/tickets/467569", "Account was hacked https://afriex.freshdesk.com/a/tickets/481202", "Age limit exceeded", "Age limit exceeded https://afriex.freshdesk.com/a/tickets/472641", "Age limit exceeded,, Offboarded", "Age limit hit", "Age limit reached", "Age limit risk", "Attempted deceit, device screen used. User offboarded.", "Blocklisted User", "CONFIRMED_FRAUDULENT_ACTIVITY", "CX Requested for deactivataion - https://afriex.freshdesk.com/a/tickets/397104", "CX claims the account was opened fraudulently-https://afriex.freshdesk.com/a/tickets/505679", "CX requested for account deactivation-https://afriex.freshdesk.com/a/tickets/480962", "CX requested for account deactivation-https://afriex.freshdesk.com/a/tickets/484405", "CX requested for deactivation", "CX wants to deactivate the account because he thinks someone hacked his account and that he didn't make the transaction to NGN https://afriex.freshdesk.com/a/tickets/467220", "<PERSON><PERSON> would like to deactivate this account and open a GBP account. He has relocated to the UK.\nhttps://afriex.freshdesk.com/a/tickets/498582", "Claims not to have opened the account https://afriex.freshdesk.com/a/tickets/461171", "Compromised of Login reported - https://afriex.freshdesk.com/a/tickets/468641", "Customer exceeded age limit", "Customer requested for deactivation - https://afriex.freshdesk.com/a/tickets/455594", "Customer's Request - https://afriex.freshdesk.com/a/tickets/471804", "Cx reqested for account deactivation", "Cx request", "<PERSON><PERSON> requested Account Deactivation because he'll be unable to cashout when his sister sends him money from the US. https://afriex.freshdesk.com/a/tickets/470409", "C<PERSON> requested account deactivation with reason he doesn't need the account again", "Cx requested account deactivation. https://afriex.freshdesk.com/a/tickets/492611", "Cx requested acct deactivation  https://afriex.freshdesk.com/a/tickets/493736", "C<PERSON> requested bcs he has another account", "Cx requested for accounr deactivation", "Cx requested for account deactivation", "Cx requested for account deactivation ", "Cx requested for account deactivation https://afriex.freshdesk.com/a/tickets/492567", "<PERSON><PERSON> requested for account deactivation, saying he didn't open this account himself.https://afriex.freshdesk.com/a/tickets/492646", "Cx requested for account deactivation-https://afriex.freshdesk.com/a/tickets/465117", "Cx requested for account deactivation-https://afriex.freshdesk.com/a/tickets/469333", "Cx requested for account deactivation-https://afriex.freshdesk.com/a/tickets/471761", "Cx requested for account deactivation-https://afriex.freshdesk.com/a/tickets/473185"]}, {"name": "admin", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The identifier of the administrator who created or modified the security note.", "found_categorical_values": ["5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3", "5cde917ef80e7700145c8dd3", "5cdf2b688fc775001425f6f2", "5dcc5049e3d8f40014d6d7dc", "5e0e198b1a409e001464ef74", "5e5813a83ada170014846dac", "5e8395f768111e00158107e1", "5ec1283291497c47af2781e0", "5ee80e1e5ce4f00015d0a737", "5f3ed3e3af84b10014ce805c", "5f60ad5c378da70014772686", "60651088ef645e0fa63d93d6", "6067dbc348bd511b297addc9", "608d1cb67057ae0fbdf0cc57", "60dd13ffdabb7c7b18afe1ad", "61004a2b249b205ea7c1b80d", "610d5f5a6ab3ad7d04e704f4", "6110d6b0f09f7c45a75411bf", "61179d8c000f8a5735c2f03f", "6142f7068a042e1b141f8f12", "61693561dff3876ec56ac78b", "61a48d4afbeaa10d81e7c571", "61a7991b6fb36c19e515da7f", "61c4d22150d34519d7868230", "61dccc856bb9b81aea063ac9", "61f1b6b5ed666325e553d579", "61f8e6a37846b437a573c9f3", "6214a7d3e4df5a67ed3ee8ef", "621cdf4d1e9344c17715e8bb", "621d20108e49f44ea91af0f2", "62206393fbe448bace89cf72", "623f1851ff43dcbddc6033ed", "6294e904014530ff25e9fa7d", "629ff864e394f65ccd1814b8", "62a05613e394f65ccd1b3456", "62a6717cac101bfa83300531", "62e2474065ee30492dd10e85", "62e7e684fc73110553a50b6d", "62eb9c92079641c3f6c75bd8", "62eb9dee079641c3f6c77086", "62eba3c9079641c3f6c7ecbe", "62eba7b3079641c3f6c82583", "62eba8cb6892255231670393", "62ebc69b199cdbd69005057a", "62ed8c66bb1a513018967220", "631f100b09e606efd7b953b3", "63209f66e9c1a4cd2fa91cfd", "632c30743d4146a732c04be5", "632c312a3d4146a732c052d6"]}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of security action or note, categorizing the nature of the entry.", "found_categorical_values": ["Blocked", "UNDEFINED", "Unblocked", "card-error", "kyc-verification", "transactional"]}, {"name": "adminInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional information about the administrator, such as their role or department.", "variant": "json_variant"}, {"name": "unblockReason", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The reason provided for unblocking a user, if applicable.", "found_categorical_values": ["Activated to enable a refund requested by userhttps://afriex.slack.com/archives/C040X06EYBY/p1740786897487899", "Activated to enable refund", "CARDS_REVIEWED_ON_STRIPE", "DISPUTE_FUNDS_RECOVERED_FROM_WALLET", "DISPUTE_RESOLVED", "EDD_PROVIDED", "KYC_IN_PLACE", "LEGAL_NAMES_UPDATED", "OTHERS", "Reactivation request https://afriex.freshdesk.com/a/tickets/476587", "Swap glitch reconciliation done.", "User needs to access the funds in the account.", "User requested for account reactivation -https://afriex.freshdesk.com/a/tickets/489701", "User requested for reactivation", "User was blocked in error", "WATCHLIST_MATCH_CLEARED", "Will deactivate back", "unblocking campaign", "wrong account for deactivation"]}, {"name": "userState", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current state of the user, indicating whether they are active, blocked, or in another status.", "found_categorical_values": ["BLOCKED", "NON_ACTION", "UNBLOCKED"]}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"updatedAt\": \"2025-01-01T15:45:30.123000\",\n  \"createdAt\": \"2025-01-01T15:45:30.123000\",\n  \"user\": \"b2c3d4e5f67890abcdef1234\",\n  \"note\": \"suspicious activity - multiple accounts linked to a single email\",\n  \"reason\": \"suspicious activity\",\n  \"admin\": \"c3d4e5f67890abcdef123456\",\n  \"type\": null,\n  \"adminInfo\": \"\\\"{\\\\\\\"id\\\\\\\":\\\\\\\"c3d4e5f67890abcdef123456\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"<PERSON>\\\\\\\",\\\\\\\"role\\\\\\\":\\\\\\\"security.officer\\\\\\\"}\\\"\",\n  \"unblockReason\": null,\n  \"userState\": \"BLOCKED\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "PEOPLE", "full_table_name": "INGEST.PEOPLE", "description": "The PEOPLE table stores information about individuals using the system, including their personal details, contact information, verification status, and account activity.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each person in the database."}, {"name": "searchId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "A unique identifier used for searching and referencing the person."}, {"name": "searchEmail", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Email address used for searching the person's account."}, {"name": "name", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Full name of the person.", "variant": "json_variant"}, {"name": "usernames", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of usernames associated with the person.", "variant": "json_variant"}, {"name": "kyc", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Know Your Customer status indicating if the person's identity has been verified.", "variant": "json_variant"}, {"name": "addresses", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of addresses associated with the person.", "variant": "json_variant"}, {"name": "dateOfBirth", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Date of birth of the person.", "variant": "json_variant"}, {"name": "emails", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of email addresses associated with the person.", "variant": "json_variant"}, {"name": "phones", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of phone numbers associated with the person.", "variant": "json_variant"}, {"name": "devices", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of devices used by the person to access the system.", "variant": "json_variant"}, {"name": "locations", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of geographical locations associated with the person.", "variant": "json_variant"}, {"name": "sendingCountries", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of countries from which the person can send transactions.", "variant": "json_variant"}, {"name": "currentSendingCountry", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The country from which the person is currently sending transactions.", "found_categorical_values": ["AT", "BE", "CA", "CI", "CM", "CN", "EG", "ET", "GB", "GH", "HR", "HT", "IN", "KE", "MG", "MW", "MX", "MZ", "NG", "PH", "PK", "RW", "SL", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "metadata", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional information about the person stored in a key-value format.", "variant": "json_variant"}, {"name": "flags", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Flags indicating special statuses or conditions related to the person.", "variant": "json_variant"}, {"name": "externalAccounts", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of external accounts linked to the person's profile.", "variant": "json_variant"}, {"name": "referralInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the person's referral status and referrer.", "variant": "json_variant"}, {"name": "status", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Current status of the person's account (e.g., active, suspended)."}, {"name": "limits", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Transaction limits associated with the person's account.", "variant": "json_variant"}, {"name": "stats", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Statistical data related to the person's account activity.", "variant": "json_variant"}, {"name": "tierInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the person's tier level within the system.", "variant": "json_variant"}, {"name": "rewardInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Details about rewards earned by the person.", "variant": "json_variant"}, {"name": "isSecurityFlagged", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether the person's account is flagged for security reasons.", "found_categorical_values": [false, true]}, {"name": "isPhoneVerified", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "Indicates whether the person's phone number has been verified.", "found_categorical_values": [false, true]}, {"name": "currentCountry", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The current country of residence of the person.", "found_categorical_values": ["AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AT", "AU", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BM", "BO", "BR", "BS", "BW", "BY", "CA", "CD", "CF", "CG", "CH", "CI", "CL", "CM", "CN", "CO", "CR", "CU", "CV", "CW", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE"]}, {"name": "currentEmail", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The primary email address currently associated with the person."}, {"name": "currentPhone", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The primary phone number currently associated with the person."}, {"name": "currentPhoto", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "URL or path to the person's current profile photo."}, {"name": "currentDeviceInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the current device being used by the person.", "variant": "json_variant"}, {"name": "permissions", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of permissions granted to the person within the system.", "variant": "json_variant"}, {"name": "transactionPin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "PIN code used for authorizing transactions.", "variant": "json_variant"}, {"name": "gender", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Gender of the person."}, {"name": "referrer", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "The person who referred this individual to the system."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the person's record was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the person's record was last updated."}, {"name": "lastLoggedInAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating the last time the person logged into their account."}, {"name": "paymentMethods", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of payment methods associated with the person's account.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"f3a1b2c3d4e5f6g7h8i9j0k1l\",\n  \"searchId\": \"f3a1b2c3d4e5f6g7h8i9j0k1l\",\n  \"searchEmail\": \"<EMAIL>\",\n  \"name\": \"{\\n  \\\"firstName\\\": \\\"John\\\",\\n  \\\"fullName\\\": \\\"<PERSON>\\\",\\n  \\\"lastName\\\": \\\"Smith\\\",\\n  \\\"userName\\\": \\\"john_doe_123\\\"\\n}\",\n  \"usernames\": \"[]\",\n  \"kyc\": \"{\\n  \\\"address\\\": {\\n    \\\"addressType\\\": \\\"residential\\\",\\n    \\\"city\\\": \\\"Los Angeles\\\",\\n    \\\"country\\\": \\\"United States\\\",\\n    \\\"fullAddress\\\": \\\"12345 Elm St, Los Angeles, CA, USA\\\",\\n    \\\"isDeactivated\\\": false,\\n    \\\"isDefault\\\": true,\\n    \\\"line1\\\": \\\"12345 Elm Street\\\",\\n    \\\"line2\\\": \\\"Downtown\\\",\\n    \\\"state\\\": \\\"California\\\",\\n    \\\"zipCode\\\": \\\"90001\\\"\\n  },\\n  \\\"createdAt\\\": {\\n    \\\"$date\\\": \\\"2024-10-01T10:54:39.894Z\\\"\\n  },\\n  \\\"dateOfBirth\\\": \\\"1990-01-01\\\",\\n  \\\"images\\\": {\\n    \\\"back\\\": \\\"back-f3a1b2c3d4e5f6g7h8i9j0k1l.jpg\\\",\\n    \\\"front\\\": \\\"front-f3a1b2c3d4e5f6g7h8i9j0k1l.jpg\\\",\\n    \\\"selfie\\\": \\\"selfie-f3a1b2c3d4e5f6g7h8i9j0k1l.jpg\\\"\\n  },\\n  \\\"isPossibleSocialEngineering\\\": false,\\n  \\\"meta\\\": {\\n    \\\"gptResponse\\\": \\\"yes, the person has light skin, a narrow nose, thin lips, and blue eyes\\\"\\n  },\\n  \\\"primary\\\": {\\n    \\\"idNumber\\\": \\\"B123-456-78-910-1\\\",\\n    \\\"idType\\\": \\\"passport\\\",\\n    \\\"issuingCountry\\\": \\\"US\\\",\\n    \\\"name\\\": \\\"Passport\\\",\\n    \\\"status\\\": \\\"success\\\"\\n  },\\n  \\\"requestedAdditionalDocuments\\\": [],\\n  \\\"stage\\\": \\\"submit\\\",\\n  \\\"status\\\": \\\"success\\\",\\n  \\\"updatedAt\\\": {\\n    \\\"$date\\\": \\\"2024-10-01T22:54:14.815Z\\\"\\n  },\\n  \\\"verification\\\": {\\n    \\\"veriff\\\": {\\n      \\\"attempts\\\": 1,\\n      \\\"createdAt\\\": \\\"2024-10-01T22:54:10.248Z\\\",\\n      \\\"result\\\": {\\n        \\\"status\\\": \\\"success\\\",\\n        \\\"technicalData\\\": {\\n          \\\"ip\\\": \\\"***********\\\"\\n        },\\n        \\\"verification\\\": {\\n          \\\"acceptanceTime\\\": \\\"2024-10-01T10:46:32.559273Z\\\",\\n          \\\"additionalVerifiedData\\\": {},\\n          \\\"code\\\": 1234,\\n          \\\"comments\\\": [],\\n          \\\"decisionTime\\\": \\\"2024-10-01T10:52:58.814517Z\\\",\\n          \\\"document\\\": {\\n            \\\"country\\\": \\\"US\\\",\\n            \\\"number\\\": \\\"B123-456-78-910-1\\\",\\n            \\\"state\\\": null,\\n            \\\"type\\\": \\\"PASSPORT\\\",\\n            \\\"validFrom\\\": null,\\n            \\\"validUntil\\\": \\\"2030-01-01\\\"\\n          },\\n          \\\"endUserId\\\": null,\\n          \\\"id\\\": \\\"abcdef12-3456-7890-abcd-ef********90\\\",\\n          \\\"person\\\": {\\n            \\\"citizenship\\\": null,\\n            \\\"dateOfBirth\\\": \\\"1990-01-01\\\",\\n            \\\"firstName\\\": \\\"JOHN DOE\\\",\\n            \\\"gender\\\": null,\\n            \\\"idNumber\\\": null,\\n            \\\"lastName\\\": \\\"SMITH\\\",\\n            \\\"nationality\\\": null,\\n            \\\"pepSanctionMatch\\\": null,\\n            \\\"placeOfBirth\\\": null,\\n            \\\"yearOfBirth\\\": null\\n          },\\n          \\\"reason\\\": \\\"Document is valid\\\",\\n          \\\"reasonCode\\\": 200,\\n          \\\"status\\\": \\\"approved\\\",\\n          \\\"vendorData\\\": \\\"f3a1b2c3d4e5f6g7h8i9j0k1l\\\"\\n        }\\n      },", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "TIERS", "full_table_name": "INGEST.TIERS", "description": "The TIERS table is used to define different tiers or levels of service or benefits that can be assigned to customers, including their associated discounts and transaction thresholds.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique identifier for each tier record.", "found_categorical_values": ["6508190706aea0746444a4e9", "6508262f06aea0746444bded", "6516e5a6b5d8df9340ffac3b", "6516e5b4b5d8df9340ffac42", "654b539b928b15fb8abacf07", "659fcd34858bf55b9d7a1d2f", "66914c8bee6bfc188e7fba37", "669bac5798e8ad730201c38b", "669fc47a49d3c6d6b840d967", "66a0df9049d3c6d6b841fe01", "670567bb7949b84952d86117", "670567ee7949b84952d86219", "6705681d7949b84952d86230", "670568517949b84952d86244", "670568757949b84952d8624e", "6707ad2a7949b84952dcc9f3", "6707bf587949b84952dced99", "6707bfa37949b84952dcefd7", "6707c04b7949b84952dcf26c", "6707c0cf7949b84952dcf420", "67406a915b62d09b9813a94e", "67cc4545ce02680d78a20b24", "67cc473ace02680d78a20bd2", "67cc69b1ce02680d78a2366c", "67cc6bf4ce02680d78a23acc", "67cc6cb7ce02680d78a23b30", "67cc6d0bce02680d78a23b86", "67cc715cce02680d78a24b67", "67cc7240ce02680d78a24cad", "67cc73f7ce02680d78a252b1", "67d59a41257acb4f52a89839", "67d59aa7257acb4f52a898c9"]}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the tier record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "The timestamp indicating when the tier record was created."}, {"name": "name", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The name of the tier, which describes the level of service or benefits.", "found_categorical_values": ["ACH_DISCOUNT", "Churned User (USD/GHS)", "Churned User (USD/NGN)", "Churned User Promo GH", "Churned User Promo KES", "Churned User Promo UGX", "Churned User Promo XOF", "Churned Users Promo NG", "Churned user Promo XAF", "New User (CAD/ETB)", "New User (CAD/GHS)", "New User (CAD/NGN)", "New User (CAD/UGX)", "New User (CAD/XOF)", "New User (GBP/GHS)", "New User (GBP/NGN)", "New User (USD/GHS)", "New User Cameroun", "New User Ethiopia", "New User GBP", "New User Kenya", "New User Nigeria(USA)", "New User Senegal", "New User Uganda", "New User(USD/NGN)", "New Users Ghana", "Regular Users Promo", "Regulated cards", "Test Promo Live", "Unregulated Cards", "simple promo"]}, {"name": "discount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The percentage discount associated with the tier.", "found_categorical_values": [-0.0168, 0.0019, 0.004, 0.0097, 0.0099, 0.01, 0.015, 0.0168, 0.0169, 0.02, 0.0224, 0.0231, 0.1, 1.0]}, {"name": "isActive", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "A boolean flag indicating whether the tier is currently active.", "found_categorical_values": [false, true]}, {"name": "transactionCountThreshold", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The minimum number of transactions required to qualify for this tier.", "found_categorical_values": [0, 1, 2, 3, 4, 1000, 3000, 5000]}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "The type of tier, which may categorize it into different service levels.", "found_categorical_values": ["paymentMethod", "promotion"]}, {"name": "transactionValueThreshold", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "The minimum total transaction value required to qualify for this tier.", "found_categorical_values": [0.0, 200.0, 300.0, 1000.0]}, {"name": "code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "A unique code representing the tier, often used for reference in transactions.", "found_categorical_values": ["CHURNED_USER", "NEW_USER", "REGULAR_USER", "REGULATED_DEBIT", "UNREGULATED_DEBIT"]}, {"name": "admin", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "The identifier of the admin user who created or last modified the tier.", "variant": "json_variant"}, {"name": "currencies", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "A list of currencies applicable for this tier.", "variant": "json_variant"}, {"name": "allowedSourceChannels", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "A list of channels from which transactions can originate for this tier.", "variant": "json_variant"}, {"name": "allowedDestChannels", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "A list of channels to which transactions can be directed for this tier.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890********\",\n  \"updatedAt\": \"2025-05-12T12:34:56.789000\",\n  \"createdAt\": \"2023-10-15T09:12:34.567000\",\n  \"name\": \"Test Users Nigeria\",\n  \"discount\": 0.0456,\n  \"isActive\": true,\n  \"transactionCountThreshold\": 3,\n  \"type\": \"offer\",\n  \"transactionValueThreshold\": 150.0,\n  \"code\": \"TEST_USER\",\n  \"admin\": \"\\\"{\\\\\\\"id\\\\\\\":\\\\\\\"9f8e7d6c5b4a3b2c1a0e9f8d\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"JaneDoe\\\\\\\",\\\\\\\"role\\\\\\\":\\\\\\\"marketing.director\\\\\\\"}\\\"\",\n  \"currencies\": \"\\\"[\\\\\\\"NGN\\\\\\\"]\\\"\",\n  \"allowedSourceChannels\": \"\\\"[\\\\\\\"CASH\\\\\\\",\\\\\\\"BANK_TRANSFER\\\\\\\"]\\\"\",\n  \"allowedDestChannels\": \"\\\"[\\\\\\\"E_WALLET\\\\\\\",\\\\\\\"CREDIT_CARD\\\\\\\"]\\\"\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "PLAIDTRANSACTIONS", "full_table_name": "INGEST.PLAIDTRANSACTIONS", "description": "The PLAIDTRANSACTIONS table stores transaction data retrieved from financial institutions via the Plaid API, enabling users to track their financial activities and manage their accounts effectively.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each transaction record."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was created."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was last updated."}, {"name": "userId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the user associated with the transaction.", "found_categorical_values": ["5c6ead695f70e3001430e6a3", "5e69f77ff024d400143ce832", "5ec2f992bb19a10015a77dec", "5ec44c41d8b703001561ee00", "5ec7afa987dcd500150678ab", "5ec9fa2690fffd0015da8e52", "5ed42ea45c46790015af38b1", "5edcd254a7ceb000154be87c", "5eddda45522e2900158bb015", "5edde45d522e2900158bb019", "5ee0599223c3a0001567eee5", "5ee18e4808021b0015ef1b6e", "5ef3b4a88552210015227d75", "5ef582aaa253dd00153b0685", "5ef6f0555b6c370015621c91", "5ef969be7ebef60015c67954", "5efebb2c9799aa001465649a", "5f0147fb8ae4c300143bcc9a", "5f19cfecc2e4fd0014adca7a", "5f1e09a862ba540014bbb444", "5f21e46802f54c00146feaa2", "5f29c639d8ba1f00143417d5", "5f33481a0d23da0014609b99", "5f459182c7f743001453f7d5", "5f5a6cee6df01900148da2db", "5f61518a4d96fa00145147a9", "5f91c9dda3fcb10016bd3e63", "5f9278217b9ee20016b0b72f", "5f956fee7ac32600161837c3", "5fb4cbc9cf479b1e548a43d9", "5fb98bc83d6ac003bc826682", "5fcb8ff5220c3f1013f73a9c", "5fccd8a9e3af80101f825bf8", "5fce8a77d37d0d100facc3fb", "5fcead15d37d0d100facc457", "5fd3831dc0367c4f06bcf476", "5fd3d3e1c29f9355a99763db", "5fd5ed5eeb98120fe8e7b76f", "5fd63f710f0aa61006789486", "5fe0118cd7b7684a1c711bfc", "5fe9bd652a66b1105471829f", "5fea552b02bd49104ae72467", "5ff1133656c14445b0072844", "5ff5a24e5552a71095f77b81", "60075c7c67055910587f0845", "600c6c8ff27a5510b03840a5", "601118150d0dab1074d70748", "60121efcebea7a12553be15d", "60127bb7ebea7a12553be1e6", "601464d6bb65af10975e8eb5"]}, {"name": "account_id", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the account from which the transaction originated.", "found_categorical_values": ["00VNdv7VKzt56OPZeqQnt0B0DRY8x1tvwArjq", "038R4wemVgsood6APp98cJALaMw5KPsB1w9jk4", "03DEQ6eoXmibB7nP8YAvCAEj0geZ8AfAvPz49", "0447aykpdKc4447eKLzVtzD5QqM0RNfda98VX", "04Ej3LVxQNCDwmErL8Pnuyxk1OeBONhx4aMPg", "04PzMXzgercnz4jrdZgwTvRvoO3kPwCVqN66N", "07RP6moBLxtmnwjARBEgu05L6E3JAeHo1r66L", "0BEJdN4Z41uoX17kD6eySbvVVM93ywI90Q7Q3", "0Bo93OXJn0IrJkxEDNJyFz9p1xaxRAt9jVVjb", "0J7DB83NAxHDOEAenz5QIA3m3zrPqYtBPoYEAJ", "0LwQon9x50tDxbA44aMjs0ajqXwV59HVEEa0o", "0M78QL83m1UorxErrgzqtZm9jBmogYC9xz6aq", "0PQmjZL9MysnrLeRVVa0sbXO7MLRoAfB8ODoLL", "0Y87dZmZn1tozPPKNqb8ueJ3bMQVv1uJ93QND", "0e6eoRarJ4Tx1p9BZm0PFR0rVrwK4zIxVPVg6", "0jQBR8KYoNUmxejVqKzqCA6YQarbj9sAaddda", "0p1xYykzrkCL36X4V8jQIYa4Qnby1KIJ6aX9B", "0q0Qopoa6qTZmOEEgqDNSzK0DYXZx1UvjevRa", "0zRekN9pb0t4kZKk9Pd8U8J7K94vRAf9epZQd", "0zY0ZPv85wCVP7yoKvkXcYdbeN7B09S55NRmm", "0zm09nw8vLUR5vqaKPyQTYZk9jbvwKHVwJozV", "10Ra8k9do1s6e4J6PgKqfMeMyYv1PXSjvVmmO", "1Dm8nKJE9BtPJmr3d3grsM91zeg6REfY0jvqZ", "1EkNzrJexruPbmDdzX5VHg9JPx6nNZIoomnMK", "1VQn457EN4s3AxLQqXM4u58Q08eZ3aFjKDLr4", "1X6arzKvgMSLjZYqBweJC5b616PBoKfgXOL6Q", "1dEbwxmgdgt1XdDbJyq1C5qgMqZzQKHA6gzPE", "1ja8Qy31gbHXzJ47ZJJ8hMdY8dME3afY9XX3p", "1jm9nmLBRnIPEKKO5BL3C5vdg5O096sYe4oAA", "1jqoAnY6BxF1MK8z5O6zCkA4ozzDJZCY65Qo4", "1vnDN609BjtXOQJ4w8BJfeJzAxwb5BCYdzeyg", "35oYb7mkyQUPdodrA4qPfAJvk09nr7TAExQxQ", "364vp5N4Oyfjbg5QQBAkFkEvkVqLk3hzdej3Q", "36DKJK59peiNNqqnJLd8ia00QxnypVHVXLYAa", "36nvvqw8dxcY4KrE7EyKTx04a0dzAzC0RLbAZ", "3DMOwQAOXQCJeeQ6eMmZizQEDm7kdbC7RALRv", "3KB4AwgQ7rIP56AV3P4rugZ1p5oJ7wIAgKgNe", "3Kaq0kpzyyfLLZ3Q3abeF8omX69D33U7yZARv", "3KbRZ7OEymTow4N3NOo4I9eLNJBweDhAO4qzr", "3KgQyaZ1v4CPPw9qaMyVtaeQQJydJzcPQYQ7Q", "3KjRAL17DjTNJL9pM7LMskzmKY5zkwFd8g3KQ", "3P0ppaem3EfwjXdqZpqEsaAxy4jK6DFAnvaJr", "3XxVX96x7wCDYBRrarv4cA0rNrZd1AI7844bd", "3e4zPn51qNtL1PY1LjVBudVR0Rb7pJf7Dn0d1", "3ev69gXnretY7ed8n4oZCD3wedd1eqHzdnZJq", "3mAy7E8dVYt8N4VpymwXH1Ajj4PxPJf7Rk7BY", "3oAExobNZPt8bqdaA3AVSbO81LJjR7f7Nyrp8", "3oYD8n4gQnCa3Vm1zaByfVAOMmnV33U7JqQ99", "3p5dVJe9jVc9NQ6KdoYos1e4394kLktznBDNk", "3r6aBeVRAvSD7EeA9jd8UA0x7y06wzcPxNy9b"]}, {"name": "amount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "Monetary value of the transaction."}, {"name": "iso_currency_code", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "ISO 4217 currency code representing the currency of the transaction amount.", "found_categorical_values": ["USD"]}, {"name": "unofficial_currency_code", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unofficial currency code, if applicable, for the transaction."}, {"name": "category", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Category classification of the transaction (e.g., groceries, utilities).", "variant": "json_variant"}, {"name": "category_id", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the category of the transaction.", "found_categorical_values": ["10000000", "10001000", "10002000", "10004000", "10005000", "10006000", "10007000", "10009000", "12004000", "12005000", "12008000", "12008003", "12008009", "12009000", "12012000", "12013000", "12015000", "12015001", "12015003", "12018004", "13000000", "13001000", "13005000", "13005007", "13005012", "13005032", "13005043", "13005048", "13005049", "14001000", "14001008", "14001009", "14001010", "14001012", "15001000", "15002000", "16000000", "16001000", "16002000", "16003000", "17000000", "17001000", "17001001", "17001009", "17001014", "17015000", "17018000", "17041000", "17042000", "17043000"]}, {"name": "check_number", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Check number associated with the transaction, if applicable."}, {"name": "date", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Date when the transaction occurred.", "found_categorical_values": ["2023-01-24", "2023-01-25", "2023-01-26", "2023-01-27", "2023-01-28", "2023-01-29", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-04", "2023-02-05", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-11", "2023-02-12", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-18", "2023-02-19", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-25", "2023-02-26", "2023-02-27", "2023-02-28", "2023-03-01", "2023-03-02", "2023-03-03", "2023-03-04", "2023-03-05", "2023-03-06", "2023-03-07", "2023-03-08", "2023-03-09", "2023-03-10", "2023-03-11", "2023-03-12", "2023-03-13", "2023-03-14"]}, {"name": "location", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Geographical location where the transaction took place.", "variant": "json_variant"}, {"name": "name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Name of the merchant or entity involved in the transaction."}, {"name": "merchant_name", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Name of the merchant as recognized by the financial institution."}, {"name": "original_description", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Original description of the transaction as provided by the financial institution."}, {"name": "payment_meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Metadata related to the payment, including additional details.", "variant": "json_variant"}, {"name": "pending", "data_type": "BOOLEAN", "is_categorical": true, "is_datetime": false, "description": "<PERSON><PERSON><PERSON> indicating whether the transaction is pending.", "found_categorical_values": [false, true]}, {"name": "pending_transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the pending transaction, if applicable."}, {"name": "account_owner", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Name of the account owner associated with the transaction.", "found_categorical_values": ["0005", "0013", "0038", "0047", "0267", "0328", "0382", "0397", "0406", "0411", "0421", "0620", "0694", "0710", "0725", "0788", "0799", "0824", "0892", "0905", "0963", "1005", "1033", "1099", "1101", "1190", "1280", "1397", "1428", "1435", "1458", "1486", "1491", "1515", "1568", "1641", "1743", "1822", "1851", "1852", "1945", "2160", "2172", "2175", "2220", "2245", "2283", "2335", "2526", "2542"]}, {"name": "transaction_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for the transaction."}, {"name": "transaction_type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Type of transaction (e.g., debit, credit).", "found_categorical_values": ["digital", "place", "special", "unresolved"]}, {"name": "logo_url", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "URL of the merchant's logo.", "found_categorical_values": ["https://plaid-merchant-logos.plaid.com/1800flowers_1506.png", "https://plaid-merchant-logos.plaid.com/24_hour_fitness_1.png", "https://plaid-merchant-logos.plaid.com/365_retail_markets_1183.png", "https://plaid-merchant-logos.plaid.com/76_gas_stations_1184.png", "https://plaid-merchant-logos.plaid.com/7eleven_3.png", "https://plaid-merchant-logos.plaid.com/85c_bakery_cafe_1621.png", "https://plaid-merchant-logos.plaid.com/99_cents_only_stores_4.png", "https://plaid-merchant-logos.plaid.com/99_ranch_market_1185.png", "https://plaid-merchant-logos.plaid.com/aaa_1239.png", "https://plaid-merchant-logos.plaid.com/aaa_insurance_5.png", "https://plaid-merchant-logos.plaid.com/aarons_6.png", "https://plaid-merchant-logos.plaid.com/aarp_1243.png", "https://plaid-merchant-logos.plaid.com/abc_fine_wine_spirits_1495.png", "https://plaid-merchant-logos.plaid.com/abcmouse_7.png", "https://plaid-merchant-logos.plaid.com/abercrombie_fitch_1398.png", "https://plaid-merchant-logos.plaid.com/acacia_energy_2139.png", "https://plaid-merchant-logos.plaid.com/academy_sports_outdoors_8.png", "https://plaid-merchant-logos.plaid.com/acceptance_insurance_1622.png", "https://plaid-merchant-logos.plaid.com/ace_hardware_9.png", "https://plaid-merchant-logos.plaid.com/ace_parking_10.png", "https://plaid-merchant-logos.plaid.com/acme_markets_12.png", "https://plaid-merchant-logos.plaid.com/actblue_14.png", "https://plaid-merchant-logos.plaid.com/adidas_16.png", "https://plaid-merchant-logos.plaid.com/adobe_17.png", "https://plaid-merchant-logos.plaid.com/adore_me_18.png", "https://plaid-merchant-logos.plaid.com/adt_20.png", "https://plaid-merchant-logos.plaid.com/advance_auto_parts_22.png", "https://plaid-merchant-logos.plaid.com/aeropostale_23.png", "https://plaid-merchant-logos.plaid.com/aflac_25.png", "https://plaid-merchant-logos.plaid.com/agoda_27.png", "https://plaid-merchant-logos.plaid.com/aig_28.png", "https://plaid-merchant-logos.plaid.com/air_france_1561.png", "https://plaid-merchant-logos.plaid.com/airbnb_29.png", "https://plaid-merchant-logos.plaid.com/alabama_power_31.png", "https://plaid-merchant-logos.plaid.com/alaska_airlines_32.png", "https://plaid-merchant-logos.plaid.com/albertsons_34.png", "https://plaid-merchant-logos.plaid.com/aldi_35.png", "https://plaid-merchant-logos.plaid.com/aldo_1569.png", "https://plaid-merchant-logos.plaid.com/alibaba_36.png", "https://plaid-merchant-logos.plaid.com/aliexpress_37.png", "https://plaid-merchant-logos.plaid.com/alipay_38.png", "https://plaid-merchant-logos.plaid.com/allianz_40.png", "https://plaid-merchant-logos.plaid.com/allstate_41.png", "https://plaid-merchant-logos.plaid.com/allsups_convenience_stores_42.png", "https://plaid-merchant-logos.plaid.com/alon_43.png", "https://plaid-merchant-logos.plaid.com/amazon_44.png", "https://plaid-merchant-logos.plaid.com/amazon_kids_plus_45.png", "https://plaid-merchant-logos.plaid.com/amazon_kindle_46.png", "https://plaid-merchant-logos.plaid.com/amazon_music_1227.png", "https://plaid-merchant-logos.plaid.com/amazon_prime_1987.png"]}, {"name": "website", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Website URL of the merchant.", "found_categorical_values": ["1800flowers.com", "24hourfitness.com", "365retailmarkets.com", "7-eleven.com", "76.com", "85cbakerycafe.com", "99only.com", "99ranch.com", "aa.com", "aaa.com", "aarons.com", "aarp.org", "abc.virginia.gov", "abcfws.com", "abcmouse.com", "abercrombie.com", "abwingsmd.com", "acaciaenergy.com", "academy.com", "acceptanceinsurance.com", "acehardware.com", "aceparking.com", "acmemarkets.com", "actblue.com", "adidas.com", "adobe.com", "adoreme.com", "ads.google.com", "adt.com", "advanceautoparts.com", "ae.com", "aeropostale.com", "aflac.com", "agoda.com", "aig.com", "ailife.com", "airbnb.com", "alabamapower.com", "alaskaair.com", "albertsons.com", "aldi.us", "aldoshoes.com", "alibaba.com", "aliexpress.com", "allianz.com", "allstate.com", "allsups.com", "amazon.com", "amazon.com/Kindle-eBooks", "amazon.com/amazonprime"]}, {"name": "authorized_date", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Date when the transaction was authorized.", "found_categorical_values": ["2023-01-24", "2023-01-25", "2023-01-26", "2023-01-27", "2023-01-28", "2023-01-29", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-04", "2023-02-05", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-11", "2023-02-12", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-18", "2023-02-19", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-25", "2023-02-26", "2023-02-27", "2023-02-28", "2023-03-01", "2023-03-02", "2023-03-03", "2023-03-04", "2023-03-05", "2023-03-06", "2023-03-07", "2023-03-08", "2023-03-09", "2023-03-10", "2023-03-11", "2023-03-12", "2023-03-13", "2023-03-14"]}, {"name": "authorized_datetime", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Date and time when the transaction was authorized."}, {"name": "datetime", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Date and time when the transaction occurred."}, {"name": "payment_channel", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Channel through which the payment was made (e.g., online, in-store).", "found_categorical_values": ["in store", "online", "other"]}, {"name": "personal_finance_category", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "User-defined category for personal finance tracking.", "variant": "json_variant"}, {"name": "transaction_code", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Code representing the type of transaction.", "variant": "json_variant"}, {"name": "personal_finance_category_icon_url", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "URL for the icon representing the personal finance category.", "found_categorical_values": ["https://plaid-category-icons.plaid.com/PFC_BANK_FEES.png", "https://plaid-category-icons.plaid.com/PFC_ENTERTAINMENT.png", "https://plaid-category-icons.plaid.com/PFC_FOOD_AND_DRINK.png", "https://plaid-category-icons.plaid.com/PFC_GENERAL_MERCHANDISE.png", "https://plaid-category-icons.plaid.com/PFC_GENERAL_SERVICES.png", "https://plaid-category-icons.plaid.com/PFC_GOVERNMENT_AND_NON_PROFIT.png", "https://plaid-category-icons.plaid.com/PFC_HOME_IMPROVEMENT.png", "https://plaid-category-icons.plaid.com/PFC_INCOME.png", "https://plaid-category-icons.plaid.com/PFC_LOAN_PAYMENTS.png", "https://plaid-category-icons.plaid.com/PFC_MEDICAL.png", "https://plaid-category-icons.plaid.com/PFC_OTHER.png", "https://plaid-category-icons.plaid.com/PFC_PERSONAL_CARE.png", "https://plaid-category-icons.plaid.com/PFC_RENT_AND_UTILITIES.png", "https://plaid-category-icons.plaid.com/PFC_TRANSFER_IN.png", "https://plaid-category-icons.plaid.com/PFC_TRANSFER_OUT.png", "https://plaid-category-icons.plaid.com/PFC_TRANSPORTATION.png", "https://plaid-category-icons.plaid.com/PFC_TRAVEL.png"]}, {"name": "counterparties", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "List of counterparties involved in the transaction.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890********\",\n  \"createdAt\": \"2024-05-10T15:30:45.123000\",\n  \"updatedAt\": \"2024-05-10T15:30:45.123000\",\n  \"userId\": \"b2c3d4e5f67890********90\",\n  \"account_id\": \"xYzAbcDeFgHiJkLmNoPqRsTuVwXyZ123456\",\n  \"amount\": 42.75,\n  \"iso_currency_code\": \"EUR\",\n  \"unofficial_currency_code\": null,\n  \"category\": \"[\\n  \\\"Electronics\\\",\\n  \\\"Online Purchase\\\"\\n]\",\n  \"category_id\": \"********\",\n  \"check_number\": null,\n  \"date\": \"2024-04-25\",\n  \"location\": \"{\\n  \\\"address\\\": null,\\n  \\\"city\\\": null,\\n  \\\"country\\\": null,\\n  \\\"lat\\\": null,\\n  \\\"lon\\\": null,\\n  \\\"postal_code\\\": null,\\n  \\\"region\\\": null,\\n  \\\"store_number\\\": null\\n}\",\n  \"name\": \"GOOGLE.COM/BILL\",\n  \"merchant_name\": \"Google\",\n  \"original_description\": null,\n  \"payment_meta\": \"{\\n  \\\"by_order_of\\\": null,\\n  \\\"payee\\\": null,\\n  \\\"payer\\\": null,\\n  \\\"payment_method\\\": null,\\n  \\\"payment_processor\\\": null,\\n  \\\"ppd_id\\\": null,\\n  \\\"reason\\\": null,\\n  \\\"reference_number\\\": null\\n}\",\n  \"pending\": true,\n  \"pending_transaction_id\": \"2CdEfGhIjKlmNoPqRsTuVwXyZ********9\",\n  \"account_owner\": null,\n  \"transaction_id\": \"B1c2D3e4F5g6H7i8J9k0L1mN2oP3qR4s5t6\",\n  \"transaction_type\": \"digital\",\n  \"logo_url\": \"https://plaid-merchant-logos.plaid.com/google_63.png\",\n  \"website\": \"google.com\",\n  \"authorized_date\": \"2024-04-25\",\n  \"authorized_datetime\": \"2024-04-25T04:00:00Z\",\n  \"datetime\": \"2024-04-27T12:45:00Z\",\n  \"payment_channel\": \"mobile\",\n  \"personal_finance_category\": \"{\\n  \\\"detailed\\\": \\\"GENERAL_MERCHANDISE_TECHNOLOGY\\\",\\n  \\\"primary\\\": \\\"GENERAL_MERCHANDISE\\\"\\n}\",\n  \"transaction_code\": \"null\",\n  \"personal_finance_category_icon_url\": \"https://plaid-category-icons.plaid.com/PFC_GENERAL_MERCHANDISE.png\",\n  \"counterparties\": \"[\\n  {\\n    \\\"logo_url\\\": \\\"https://plaid-merchant-logos.plaid.com/google_63.png\\\",\\n    \\\"name\\\": \\\"Google\\\",\\n    \\\"type\\\": \\\"merchant\\\",\\n    \\\"website\\\": \\\"google.com\\\"\\n  }\\n]\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}, {"schema_name": "INGEST", "table_name": "TRANSACTIONS", "full_table_name": "INGEST.TRANSACTIONS", "description": "The TRANSACTIONS table records all financial transactions processed within the system, capturing details about the source and destination of funds, transaction status, and associated metadata.", "fields": [{"name": "_id", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for each transaction record."}, {"name": "updatedAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was last updated."}, {"name": "createdAt", "data_type": "TIMESTAMP_NTZ", "is_categorical": false, "is_datetime": true, "description": "Timestamp indicating when the transaction record was created."}, {"name": "processor", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Name of the payment processor handling the transaction.", "found_categorical_values": ["ALPAY", "BEYONIC", "BRIDGECARD", "CELLULANT", "CHAPA", "DLOCAL", "FAIRMONEY", "FINCRA", "FLICK", "HUB2", "JUICYWAY", "KORAPAY", "MONIEPOINT", "MONO", "ONAFRIQ", "PAYSTACK", "PROVIDUS", "STRIPE", "TERRAPAY", "TRICE", "VFD", "ZEEPAY", "ZENITH", "ZILMONEY"]}, {"name": "processorTransactionId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier assigned by the payment processor for the transaction."}, {"name": "internalTransactionId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Unique identifier for the transaction used internally within the system."}, {"name": "sourceUserId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the user initiating the transaction.", "found_categorical_values": ["5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3", "5c909536a37fc60014208da8", "5ca55ee7e42cd90014db6142", "5cab6ab0e7525f001400d7f3", "5cde917ef80e7700145c8dd3", "5cdf2b688fc775001425f6f2", "5cdf91728fc775001425f6f7", "5ce3dc18a8d7800014a9ce09", "5d25df753d86850014e9c907", "5dcc5049e3d8f40014d6d7dc", "5e0a8c9af4e76b0014fc2891", "5e0e198b1a409e001464ef74", "5e3b01bcb8d2360014c2f004", "5e4033eaf4b82d00149d1d1b", "5e4442db31e8dd00148b0133", "5e44bd257996840014b9b9ef", "5e4521977996840014b9b9f1", "5e45603c7996840014b9b9f4", "5e4582197996840014b9b9f6", "5e495764b41e8b00144dc5f2", "5e516e25aede080014822e96", "5e5813a83ada170014846dac", "5e5c17f4c378160014cbe7a5", "5e5d9a9d0967070014211974", "5e5e613ed554e2001422c5a4", "5e5ec654d554e2001422c5b1", "5e5ef687eed7c900148487f0", "5e5efcedeed7c900148487f3", "5e5f1b0beed7c900148487ff", "5e60c767d564d00014059665", "5e63053d2ab3fb00140c7667", "5e632fdd2ab3fb00140c766e", "5e635cc72ab3fb00140c7675", "5e6365e02ab3fb00140c7677", "5e642c3f2ab3fb00140c7694", "5e6501a5c9c04200141600c3", "5e650f83c9c04200141600c8", "5e65a244c9c04200141600dd", "5e65a82b5441b600148a0d5b", "5e65dd0f534bf30014e78e65", "5e65f152a002be0014691d21", "5e66e8b9c8e4850014ba2aab", "5e694b2d09d36200144ada90", "5e69f77ff024d400143ce832", "5e6a1ee8f024d400143ce83a", "5e6a5897f024d400143ce855", "5e6ab94ff024d400143ce866", "5e6af862f024d400143ce872", "5e6b03df461044001469f812"]}, {"name": "sourceAsset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Type of asset being transferred from the source (e.g., currency, cryptocurrency).", "found_categorical_values": ["CAD", "EGP", "ETB", "EUR", "GBP", "GHS", "INR", "KES", "NGN", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR"]}, {"name": "sourceAmount", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Amount of the source asset being transferred.", "found_categorical_values": [0.0, 1.3500341558641435e-05, 3.352400715836784e-05, 4.205921938088829e-05, 9.767123861083593e-05, 0.00010894878045458879, 0.0001098761489783235, 0.00011392944484994338, 0.00012624907680362587, 0.00013020833333333333, 0.00013652249890782, 0.00021143437077131256, 0.0002639842165226332, 0.0002663750560789592, 0.00026986183074265976, 0.0003019593041210582, 0.00031264211005002277, 0.00034634673464298575, 0.00036075536623607276, 0.000386943423343744, 0.00039228872450123296, 0.0004022121669180492, 0.00044169837034017, 0.00046515561569688765, 0.0005264470366573384, 0.0005839125278479563, 0.0005921539600296077, 0.0006043188193807792, 0.000616868550919695, 0.00068, 0.0006869672498878421, 0.000707232815602644, 0.000715006729475101, 0.0008035244243717269, 0.0008182937189022381, 0.0008400537634408601, 0.0008411843876177658, 0.0008445945945945946, 0.0008491270973439304, 0.00088739624290083, 0.0008892099924972907, 0.0008900756564307967, 0.0009112830865859131, 0.0009335173309521877, 0.0009765216299541034, 0.001, 0.001004800398523651, 0.0010376529673416022, 0.0010574177857671566, 0.0011155269298185513]}, {"name": "sourceCountry", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Country of the source user initiating the transaction.", "found_categorical_values": ["AT", "BE", "CA", "CI", "CM", "CY", "DE", "EE", "EG", "ES", "ET", "FI", "FR", "GB", "GH", "GR", "HR", "IE", "IN", "IT", "KE", "LT", "LU", "LV", "MT", "NG", "NL", "PK", "PL", "PT", "RW", "SI", "SK", "SN", "TZ", "UG", "US", "ZA"]}, {"name": "destinationUserId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the user receiving the transaction.", "found_categorical_values": ["5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3", "5c780266381b990014592619", "5c909536a37fc60014208da8", "5ca55ee7e42cd90014db6142", "5cab6ab0e7525f001400d7f3", "5cc5593f49455c0014dc935a", "5cde917ef80e7700145c8dd3", "5cdf2b688fc775001425f6f2", "5cdf91728fc775001425f6f7", "5ce3dc18a8d7800014a9ce09", "5ced57d16867a20014277fe9", "5d25df753d86850014e9c907", "5d2f688b82545b001408a22a", "5d402a34a8fac500148e53a4", "5d68d5ba53a4ee001436d1de", "5d6fdc9d92f8390014d24b96", "5dcc5049e3d8f40014d6d7dc", "5e0a8c9af4e76b0014fc2891", "5e0e198b1a409e001464ef74", "5e3b01bcb8d2360014c2f004", "5e4033eaf4b82d00149d1d1b", "5e4442db31e8dd00148b0133", "5e44b5f331e8dd00148b0135", "5e44bd257996840014b9b9ef", "5e4521977996840014b9b9f1", "5e4582197996840014b9b9f6", "5e4866e73ad8b90014c1d98d", "5e495764b41e8b00144dc5f2", "5e516e25aede080014822e96", "5e5813a83ada170014846dac", "5e5e613ed554e2001422c5a4", "5e5ec654d554e2001422c5b1", "5e5ef687eed7c900148487f0", "5e5efcedeed7c900148487f3", "5e5f1b0beed7c900148487ff", "5e5ffc1dd564d000140595e0", "5e6023f2d564d00014059630", "5e609bf8d564d0001405965c", "5e60c767d564d00014059665", "5e635cc72ab3fb00140c7675", "5e6365e02ab3fb00140c7677", "5e642c3f2ab3fb00140c7694", "5e6501a5c9c04200141600c3", "5e65a244c9c04200141600dd", "5e65dd0f534bf30014e78e65", "5e66e8b9c8e4850014ba2aab", "5e69f77ff024d400143ce832", "5e6a1ee8f024d400143ce83a", "5e6ab94ff024d400143ce866"]}, {"name": "destinationAsset", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Type of asset being received by the destination (e.g., currency, cryptocurrency).", "found_categorical_values": ["CAD", "EGP", "ETB", "EUR", "GBP", "GHS", "HTG", "INR", "KES", "MGA", "MWK", "MZN", "NGN", "PHP", "PKR", "RWF", "TZS", "UGX", "USD", "XAF", "XOF", "ZAR", "ZMW"]}, {"name": "destinationAmount", "data_type": "NUMBER", "is_categorical": false, "is_datetime": false, "description": "Amount of the destination asset being received."}, {"name": "destinationCountry", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Country of the destination user receiving the transaction.", "found_categorical_values": ["AT", "BJ", "CA", "CI", "CM", "DE", "EG", "ES", "ET", "FR", "GB", "GH", "HT", "IE", "IN", "KE", "MG", "MW", "MZ", "NG", "NL", "PH", "PK", "RW", "SN", "TZ", "UG", "US", "ZA", "ZM"]}, {"name": "status", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Current status of the transaction (e.g., pending, completed, failed).", "found_categorical_values": ["CANCELLED", "DISPUTE_EVIDENCE_SUBMITTED", "DISPUTE_LOST", "DISPUTE_RESOLVED", "DISPUTE_WON", "FAILED", "IN_REVIEW", "PENDING", "PROCESSING", "REFUNDED", "RETRY", "SUCCESS", "UNKNOWN"]}, {"name": "type", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Type of transaction (e.g., transfer, deposit, withdrawal).", "found_categorical_values": ["DEPOSIT", "REFERRAL", "REVERSAL", "SWAP", "TRANSFER", "VIRTUAL_CARD_LOAD", "VIRTUAL_CARD_SPEND", "WELCOME_BONUS", "WITHDRAW"]}, {"name": "tierId", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the tier level associated with the transaction, often related to user permissions or limits.", "found_categorical_values": ["6508190706aea0746444a4e9", "6516e5a6b5d8df9340ffac3b", "6516e5b4b5d8df9340ffac42", "654b539b928b15fb8abacf07", "670567bb7949b84952d86117", "67cc473ace02680d78a20bd2"]}, {"name": "sourceAccountDetails", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Details of the source account from which the funds are being transferred.", "variant": "json_variant"}, {"name": "sourceAccountId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the source account involved in the transaction."}, {"name": "destinationAccountDetails", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Details of the destination account to which the funds are being transferred.", "variant": "json_variant"}, {"name": "destinationAccountId", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Identifier for the destination account involved in the transaction."}, {"name": "channel", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Channel through which the transaction was initiated (e.g., web, mobile app).", "found_categorical_values": ["ACH_BANK_ACCOUNT", "ADMIN", "BANK_ACCOUNT", "CARD", "INTERAC", "INTERNAL", "MOBILE_MONEY", "RFP", "UPI", "VIRTUAL_BANK_ACCOUNT", "VIRTUAL_CARD", "WIDGET"]}, {"name": "processorResponseMeta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Metadata returned by the payment processor regarding the transaction.", "variant": "json_variant"}, {"name": "rates", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Exchange rates applied to the transaction, if applicable.", "variant": "json_variant"}, {"name": "fee", "data_type": "NUMBER", "is_categorical": true, "is_datetime": false, "description": "Transaction fee charged for processing the transaction.", "found_categorical_values": [0.0, 2e-05, 4e-05, 5e-05, 6e-05, 8e-05, 0.0001, 0.00012, 0.00014, 0.00015, 0.00016, 0.****************, 0.0002, 0.00022, 0.00024, 0.00025, 0.00026, 0.00028, 0.0003, 0.00032, 0.00034, 0.00035, 0.****************, 0.00038, 0.0004, 0.00042, 0.00044, 0.00045, 0.00046, 0.00048, 0.0005, 0.00052, 0.00054, 0.00055, 0.00056, 0.00058, 0.0006, 0.00062, 0.00064, 0.00065, 0.00066, 0.00068, 0.0006999999999999, 0.0007, 0.0007199999999999, 0.00074, 0.00075, 0.00076, 0.00078, 0.0008]}, {"name": "initiatedBy", "data_type": "TEXT", "is_categorical": true, "is_datetime": false, "description": "Identifier for the user or system that initiated the transaction.", "found_categorical_values": ["5c6eab975f70e3001430e6a1", "5c6ead695f70e3001430e6a3", "5cdf2b688fc775001425f6f2", "5e5813a83ada170014846dac", "5e8395f768111e00158107e1", "5ec1283291497c47af2781e0", "5f60ad5c378da70014772686", "608d1cb67057ae0fbdf0cc57", "60dd13ffdabb7c7b18afe1ad", "610d5f5a6ab3ad7d04e704f4", "6110d6b0f09f7c45a75411bf", "61179d8c000f8a5735c2f03f", "6142f7068a042e1b141f8f12", "61693561dff3876ec56ac78b", "61a48d4afbeaa10d81e7c571", "61dccc856bb9b81aea063ac9", "6214a7d3e4df5a67ed3ee8ef", "621cdf4d1e9344c17715e8bb", "621d20108e49f44ea91af0f2", "62206393fbe448bace89cf72", "623f1851ff43dcbddc6033ed", "6294e904014530ff25e9fa7d", "629ff864e394f65ccd1814b8", "62a05613e394f65ccd1b3456", "62a6717cac101bfa83300531", "62e2474065ee30492dd10e85", "62e7e684fc73110553a50b6d", "62eb9c92079641c3f6c75bd8", "62eba8cb6892255231670393", "62ed8c66bb1a513018967220", "631f100b09e606efd7b953b3", "63209f66e9c1a4cd2fa91cfd", "632c30743d4146a732c04be5", "632c312a3d4146a732c052d6", "632c318b3d4146a732c05854", "632c7bee1c5802b57c414a4c", "63370f1592c7c78110d35a1c", "63374112b4036891eda61abd", "633768ac6c0cd4ac13c7c76c", "633772146c0cd4ac13c8d496", "63386194594d653a06d3c078", "63395897854764bde3b22fa2", "6339fcea8f3d2dad2d18bef6", "633ab1652c63b219670a768b", "633ab1e52c63b219670a7d74", "633ab4762c63b219670aa6cf", "633ab4772c63b219670aa6e0", "633d053b62801c2db34a5b63", "6340ce9ff24ef506dd4dc25b", "635a4ec132f9946b2fb7467a"]}, {"name": "ledgerTransactionIds", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "List of identifiers for ledger entries associated with this transaction."}, {"name": "voidTransactionIds", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "List of identifiers for any voided transactions related to this transaction."}, {"name": "ip", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "IP address of the user initiating the transaction."}, {"name": "otcRate", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Over-the-counter rate applied to the transaction, if applicable.", "variant": "json_variant"}, {"name": "depositTransactionReference", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Reference identifier for deposit transactions, if applicable."}, {"name": "deviceInfo", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Information about the device used to initiate the transaction.", "variant": "json_variant"}, {"name": "narration", "data_type": "TEXT", "is_categorical": false, "is_datetime": false, "description": "Narrative or description provided for the transaction."}, {"name": "meta", "data_type": "VARIANT", "is_categorical": false, "is_datetime": false, "description": "Additional metadata related to the transaction.", "variant": "json_variant"}], "status": "active", "sample_rows": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef1234\",\n  \"updatedAt\": \"2024-08-15T12:30:45.123000\",\n  \"createdAt\": \"2024-08-15T09:15:30.456000\",\n  \"processor\": \"PAYPAL\",\n  \"processorTransactionId\": \"PayPal-********90abcdef********90-************3\",\n  \"internalTransactionId\": \"PayPal-********90abcdef********90-************3\",\n  \"sourceUserId\": null,\n  \"sourceAsset\": \"EUR\",\n  \"sourceAmount\": 150.75,\n  \"sourceCountry\": \"FR\",\n  \"destinationUserId\": \"abcdef********90abcdef1234\",\n  \"destinationAsset\": \"NGN\",\n  \"destinationAmount\": 65000.00,\n  \"destinationCountry\": \"NG\",\n  \"status\": \"COMPLETED\",\n  \"type\": \"DEPOSIT\",\n  \"tierId\": null,\n  \"sourceAccountDetails\": \"null\",\n  \"sourceAccountId\": null,\n  \"destinationAccountDetails\": \"{\\n  \\\"accountName\\\": \\\"Emeka Okafor \\\",\\n  \\\"accountPhone\\\": \\\"*************\\\",\\n  \\\"country\\\": \\\"NG\\\",\\n  \\\"currency\\\": \\\"NGN\\\",\\n  \\\"provider\\\": \\\"Airtel\\\"\\n}\",\n  \"destinationAccountId\": \"abcdef********90abcdef5678\",\n  \"channel\": \"BANK_TRANSFER\",\n  \"processorResponseMeta\": \"{\\n  \\\"description\\\": \\\"Transaction successful\\\",\\n  \\\"statusCode\\\": \\\"200\\\",\\n  \\\"statusMessage\\\": \\\"Success\\\",\\n  \\\"transactionReference\\\": \\\"************\\\",\\n  \\\"transactionType\\\": \\\"E2E\\\"\\n}\",\n  \"rates\": \"{\\n  \\\"NGN\\\": {\\n    \\\"NGN\\\": \\\"1\\\",\\n    \\\"EUR\\\": \\\"0.0025\\\"\\n  },\\n  \\\"EUR\\\": {\\n    \\\"NGN\\\": \\\"400.00\\\",\\n    \\\"EUR\\\": \\\"1\\\"\\n  }\\n}\",\n  \"fee\": 5.0,\n  \"initiatedBy\": \"abcdef********90abcdefabcd\",\n  \"ledgerTransactionIds\": \"a1b2c3d4e5f67890abcdef5678\",\n  \"voidTransactionIds\": \"a1b2c3d4e5f67890abcdef8765\",\n  \"ip\": \"***********\",\n  \"otcRate\": \"null\",\n  \"depositTransactionReference\": \"a1b2c3d4e5f67890abcdef4321\",\n  \"deviceInfo\": \"null\",\n  \"narration\": \"\\ud83d\\udcb0 Rent Payment\",\n  \"meta\": \"{\\n  \\\"balanceAfter\\\": [\\n    {\\n      \\\"balances\\\": {\\n        \\\"NGN\\\": 1000,\\n        \\\"EUR\\\": 0.5\\n      },\\n      \\\"userId\\\": \\\"abcdef********90abcdef1234\\\"\\n    }\\n  ],\\n  \\\"rateBatchId\\\": {\\n    \\\"serverRateBatchId\\\": \\\"\\\"\\n  }\\n}\"\n}", "debug_info": {"has_sample_rows": true, "sample_rows_error": null}}]}