{"report": {"total_schemas": 3, "analyzed_schemas": [{"schema_name": "analytics", "tables_count": 14, "successful_tables": 14, "failed_tables": 0, "error": null}, {"schema_name": "finance", "tables_count": 15, "successful_tables": 15, "failed_tables": 0, "error": null}, {"schema_name": "ecommerce", "tables_count": 15, "successful_tables": 15, "failed_tables": 0, "error": null}], "total_tables_found": 44, "successfully_analyzed": 44, "failed_analysis": 0, "failed_tables": [], "execution_time": 99.94}, "tables": [{"schema_name": "analytics", "table_name": "analytics.marketing_campaigns", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is designed to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, enabling effective tracking and management of marketing efforts.", "fields": [{"name": "campaign_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.marketing_campaigns_campaign_id_seq'::regclass)", "description": "A unique identifier for each marketing campaign."}, {"name": "campaign_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the marketing campaign."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the marketing campaign begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the marketing campaign ends."}, {"name": "budget", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total budget allocated for the marketing campaign."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the marketing campaign (e.g., active, completed, paused)."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.customer_segments", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is designed to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement.", "fields": [{"name": "segment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.customer_segments_segment_id_seq'::regclass)", "description": "A unique identifier for each customer segment."}, {"name": "segment_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name assigned to the customer segment for easy reference."}, {"name": "criteria", "data_type": "jsonb", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The specific conditions or characteristics that define the customer segment."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the customer segment was created."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.customer_lifetime_value", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage the calculated lifetime value of customers, which helps businesses understand the long-term value each customer brings based on their purchasing behavior and trends.", "fields": [{"name": "clv_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.customer_lifetime_value_clv_id_seq'::regclass)", "description": "A unique identifier for each customer lifetime value record."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer associated with the lifetime value."}, {"name": "calculated_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date on which the customer lifetime value was calculated."}, {"name": "predicted_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The predicted monetary value that a customer is expected to generate over their lifetime."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that indicates the level of certainty in the predicted value."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.campaign_metrics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics related to marketing campaigns, allowing for analysis of campaign effectiveness over time.", "fields": [{"name": "metric_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.campaign_metrics_metric_id_seq'::regclass)", "description": "A unique identifier for each metric entry."}, {"name": "campaign_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the campaign to which the metric belongs."}, {"name": "metric_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the metric being recorded (e.g., clicks, impressions, conversions)."}, {"name": "metric_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The numerical value of the metric recorded for the campaign."}, {"name": "metric_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the metric was recorded, allowing for time-based analysis."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.churn_prediction", "description": "This table is designed to store information related to customer churn predictions for a business. It helps in analyzing customer behavior and identifying those who are at risk of leaving the service. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level.", "fields": [{"name": "prediction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.churn_prediction_prediction_id_seq'::regclass)", "description": "A unique identifier for each churn prediction record."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer associated with the churn prediction."}, {"name": "prediction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the churn prediction was made."}, {"name": "churn_probability", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The probability that the customer will churn, expressed as a percentage."}, {"name": "risk_level", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A categorical assessment of the customer's risk of churning, such as 'low', 'medium', or 'high'."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.conversion_funnel", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The 'conversion_funnel' table is designed to track the progress of users through various stages of a conversion process, allowing for analysis of user behavior and conversion rates at each stage.", "fields": [{"name": "funnel_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.conversion_funnel_funnel_id_seq'::regclass)", "description": "A unique identifier for each conversion funnel instance."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the user associated with the conversion funnel."}, {"name": "stage", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current stage of the user in the conversion funnel, indicating their progress."}, {"name": "stage_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the user reached the current stage in the funnel."}, {"name": "conversion_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The monetary value associated with the conversion at this stage, if applicable."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.inventory_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is designed to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, allowing for better inventory management and planning.", "fields": [{"name": "forecast_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.inventory_forecast_forecast_id_seq'::regclass)", "description": "A unique identifier for each forecast entry."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product associated with the forecast."}, {"name": "forecast_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date for which the stock forecast is made."}, {"name": "predicted_stock", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The estimated quantity of stock expected to be available on the forecast date."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that indicates the uncertainty of the predicted stock, providing a measure of reliability for the forecast."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.performance_metrics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on the performance of different entities or processes.", "fields": [{"name": "metric_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.performance_metrics_metric_id_seq'::regclass)", "description": "A unique identifier for each performance metric entry."}, {"name": "metric_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the performance metric being recorded."}, {"name": "metric_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The value of the performance metric, representing its measurement."}, {"name": "metric_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the performance metric was recorded."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category under which the performance metric falls, helping to classify the metrics."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.segment_members", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and segments within a marketing or customer relationship management system. It allows for the organization of customers into specific segments for targeted marketing efforts and analysis.", "fields": [{"name": "member_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.segment_members_member_id_seq'::regclass)", "description": "A unique identifier for each member in the segment."}, {"name": "segment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A unique identifier for the segment to which the member belongs."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A unique identifier for the customer associated with the segment."}, {"name": "added_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the member was added to the segment."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.sales_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The purpose of the 'sales_forecast' table is to store and manage sales predictions for various products over specified time periods, allowing businesses to plan inventory and marketing strategies effectively.", "fields": [{"name": "forecast_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.sales_forecast_forecast_id_seq'::regclass)", "description": "A unique identifier for each sales forecast entry."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product associated with the sales forecast."}, {"name": "forecast_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date for which the sales forecast is made."}, {"name": "predicted_sales", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The estimated number of units expected to be sold on the forecast date."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that indicates the reliability of the predicted sales figure."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.search_queries", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within an application. It tracks each search made by users, including details about the user, the specific search terms they used, when the search occurred, and how many results were returned.", "fields": [{"name": "query_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.search_queries_query_id_seq'::regclass)", "description": "A unique identifier for each search query."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the user who performed the search."}, {"name": "query_text", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The actual text of the search query entered by the user."}, {"name": "search_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the search query was executed."}, {"name": "results_count", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The number of results returned for the search query."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "analytics", "table_name": "analytics.product_views", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the views of products by users on an e-commerce platform. It records each instance a user views a product, capturing relevant details such as the user, the product viewed, and the duration of the view.", "fields": [{"name": "view_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.product_views_view_id_seq'::regclass)", "description": "A unique identifier for each view record."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product that was viewed."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the user who viewed the product."}, {"name": "view_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the product was viewed."}, {"name": "view_duration", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The duration of time (in seconds) that the user viewed the product."}], "status": "deactivated", "sample_rows": "{\n  \"view_id\": \"random_view_id_123456\",\n  \"product_id\": \"random_product_id_789012\",\n  \"user_id\": \"random_user_id_345678\",\n  \"view_date\": \"2023-10-01T12:00:00Z\",\n  \"view_duration\": 120\n}"}, {"schema_name": "analytics", "table_name": "analytics.page_views", "description": "This table records the details of each page view on the website. It tracks user interactions with different pages, allowing for analysis of traffic patterns and user engagement. This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The data collected can be used for reporting and improving user experience.", "fields": [{"name": "view_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.page_views_view_id_seq'::regclass)", "description": "A unique identifier for each page view record."}, {"name": "page_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The URL of the page that was viewed."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the user who viewed the page."}, {"name": "view_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the page was viewed."}, {"name": "session_id", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the session during which the page view occurred."}], "status": "deactivated", "sample_rows": "{\n  \"view_id\": \"random_view_id_123456\",\n  \"page_url\": \"https://randompageurl.com\",\n  \"user_id\": \"random_user_id_789012\",\n  \"view_date\": \"2023-10-01T12:00:00Z\",\n  \"session_id\": \"random_session_id_345678\"\n}"}, {"schema_name": "analytics", "table_name": "analytics.user_activity", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time.", "fields": [{"name": "activity_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('analytics.user_activity_activity_id_seq'::regclass)", "description": "A unique identifier for each activity record."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the user who performed the activity."}, {"name": "activity_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of activity performed by the user, such as login, logout, or content creation.", "found_categorical_values": ["login", "logout", "search", "view_product"]}, {"name": "activity_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the activity occurred."}, {"name": "details", "data_type": "jsonb", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "Additional information or context about the activity.", "json_type": "json_field"}], "status": "deactivated", "sample_rows": "{\n  \"activity_id\": \"random_activity_id_123456\",\n  \"user_id\": \"random_user_id_789012\",\n  \"activity_type\": \"random_activity_type\",\n  \"activity_date\": \"2023-10-01T12:00:00Z\",\n  \"details\": \"random_details_info\"\n}"}, {"schema_name": "finance", "table_name": "finance.currency_exchange", "description": "This table contains the following fields: exchange_id, from_currency, to_currency, rate, exchange_date. The purpose of the 'currency_exchange' table is to store information about currency exchange transactions, including the currencies involved, the exchange rate applied, and the date of the transaction.", "fields": [{"name": "exchange_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.currency_exchange_exchange_id_seq'::regclass)", "description": "A unique identifier for each currency exchange transaction."}, {"name": "from_currency", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The currency code of the currency being exchanged from."}, {"name": "to_currency", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The currency code of the currency being exchanged to."}, {"name": "rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The exchange rate applied for the transaction."}, {"name": "exchange_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date on which the currency exchange took place."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.credit_card_transactions", "description": "This table contains the following fields: transaction_id, credit_card_id, amount, merchant, transaction_date. The purpose of this table is to store detailed records of all transactions made using credit cards, allowing for tracking and analysis of spending behavior, merchant activity, and transaction history.", "fields": [{"name": "transaction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.credit_card_transactions_transaction_id_seq'::regclass)", "description": "A unique identifier for each transaction."}, {"name": "credit_card_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the credit card used in the transaction."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money spent in the transaction."}, {"name": "merchant", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the merchant where the transaction took place."}, {"name": "transaction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the transaction occurred."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.investments", "description": "This table contains the following fields: investment_id, investment_type, amount, start_date, end_date, return_rate. The 'investments' table is designed to track various investment records, detailing the specifics of each investment made, including the type, financial details, and duration of the investment.", "fields": [{"name": "investment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.investments_investment_id_seq'::regclass)", "description": "A unique identifier for each investment record."}, {"name": "investment_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category or type of investment (e.g., stocks, bonds, real estate)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total monetary amount invested."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the investment was initiated."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The date when the investment matures or is expected to be liquidated."}, {"name": "return_rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The expected or actual rate of return on the investment expressed as a percentage."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.financial_reports", "description": "This table contains the following fields: report_id, report_type, period, generated_at, content. The 'financial_reports' table is designed to store detailed records of financial reports generated by the organization. Each entry in this table represents a unique financial report, capturing essential information about the type of report, the reporting period, the date it was generated, and the actual content of the report.", "fields": [{"name": "report_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.financial_reports_report_id_seq'::regclass)", "description": "A unique identifier for each financial report."}, {"name": "report_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of financial report, such as income statement, balance sheet, or cash flow statement."}, {"name": "period", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The reporting period for which the financial report is generated, typically expressed as a date range."}, {"name": "generated_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the financial report was generated."}, {"name": "content", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The actual content or data of the financial report, often in a structured format."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.investment_returns", "description": "This table contains the following fields: return_id, investment_id, amount, return_date. The purpose of the 'investment_returns' table is to track the returns generated from various investments over time, allowing for analysis of investment performance and profitability.", "fields": [{"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.investment_returns_return_id_seq'::regclass)", "description": "A unique identifier for each return record."}, {"name": "investment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A foreign key that links to the specific investment associated with the return."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The monetary value of the return generated from the investment."}, {"name": "return_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date on which the return was realized or recorded."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.credit_cards", "description": "This table contains the following fields: credit_card_id, card_number, card_holder, expiry_date, credit_limit, current_balance. The 'credit_cards' table is designed to store information about credit card accounts, including unique identifiers, card details, and financial limits associated with each card.", "fields": [{"name": "credit_card_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.credit_cards_credit_card_id_seq'::regclass)", "description": "A unique identifier for each credit card record."}, {"name": "card_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number assigned to the credit card, used for transactions."}, {"name": "card_holder", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the individual or entity that owns the credit card."}, {"name": "expiry_date", "data_type": "date", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the credit card expires and is no longer valid."}, {"name": "credit_limit", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The maximum amount of credit that can be used on the card."}, {"name": "current_balance", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current outstanding balance on the credit card."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.budgets", "description": "This table contains the following fields: budget_id, category, amount, start_date, end_date. The 'budgets' table is designed to store financial planning information for various categories, allowing users to track their budget allocations over specified time periods.", "fields": [{"name": "budget_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.budgets_budget_id_seq'::regclass)", "description": "A unique identifier for each budget entry."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category of the budget, such as 'Food', 'Utilities', or 'Entertainment'."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount allocated for the budget category."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the budget period begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the budget period ends."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.expenses", "description": "This table contains the following fields: expense_id, category, amount, description, expense_date, payment_method. The 'expenses' table is designed to track and manage financial expenditures made by an individual or organization. It records details about each expense, including its category, amount spent, a brief description, the date of the expense, and the method of payment used.", "fields": [{"name": "expense_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.expenses_expense_id_seq'::regclass)", "description": "A unique identifier for each expense entry."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category to which the expense belongs, such as travel, food, or utilities."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money spent on the expense."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A brief description of the expense, providing additional context."}, {"name": "expense_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the expense was incurred."}, {"name": "payment_method", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The method used to pay for the expense, such as credit card, cash, or bank transfer."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.loan_payments", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is designed to track the payments made towards loans, capturing essential details about each payment transaction. It helps in managing loan repayment schedules and monitoring the status of payments.", "fields": [{"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.loan_payments_payment_id_seq'::regclass)", "description": "A unique identifier for each payment record."}, {"name": "loan_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the loan associated with the payment."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The amount of money paid in the payment transaction."}, {"name": "payment_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date on which the payment was made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the payment, indicating whether it is completed, pending, or failed."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.payments", "description": "This table contains the following fields: payment_id, invoice_id, amount, payment_method, payment_date, status. The 'payments' table is designed to store information about financial transactions made by customers, tracking the details of each payment related to invoices, including the amount paid, the method of payment, and the status of the transaction.", "fields": [{"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.payments_payment_id_seq'::regclass)", "description": "A unique identifier for each payment transaction."}, {"name": "invoice_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the invoice associated with the payment."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money paid in the transaction."}, {"name": "payment_method", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The method used for the payment, such as credit card, cash, or bank transfer."}, {"name": "payment_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date when the payment was made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the payment, indicating whether it is completed, pending, or failed."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.tax_records", "description": "This table contains the following fields: tax_record_id, tax_type, amount, tax_period, due_date, status. The 'tax_records' table is used to store information about various tax obligations for individuals or entities, including details about the type of tax, the amount owed, the period for which the tax is applicable, the due date for payment, and the current status of the tax record.", "fields": [{"name": "tax_record_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.tax_records_tax_record_id_seq'::regclass)", "description": "A unique identifier for each tax record."}, {"name": "tax_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of tax being recorded (e.g., income tax, property tax, sales tax)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of tax owed."}, {"name": "tax_period", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The specific period for which the tax is applicable."}, {"name": "due_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date by which the tax payment must be made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the tax record (e.g., paid, unpaid, overdue)."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.loans", "description": "This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status. The 'loans' table is designed to store information about various loans issued to borrowers, including details about the type of loan, the amount borrowed, the interest rate applied, and the duration of the loan. It also tracks the current status of each loan, allowing for effective management and reporting of loan data.", "fields": [{"name": "loan_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.loans_loan_id_seq'::regclass)", "description": "A unique identifier for each loan record."}, {"name": "loan_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of loan (e.g., personal, mortgage, auto, etc.)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money borrowed in the loan."}, {"name": "interest_rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The percentage of interest charged on the loan amount."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the loan period begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the loan period ends."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the loan (e.g., active, paid off, defaulted, etc.)."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "finance", "table_name": "finance.accounts", "description": "This table contains the following fields: account_id, account_number, account_type, balance, created_at. The 'accounts' table is designed to store information about user accounts in a financial system, including unique identifiers, account details, and balance information.", "fields": [{"name": "account_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.accounts_account_id_seq'::regclass)", "description": "A unique identifier for each account, typically an integer or UUID."}, {"name": "account_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The unique number assigned to the account for identification purposes."}, {"name": "account_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of account, such as savings, checking, or credit.", "found_categorical_values": ["checking", "investment", "savings"]}, {"name": "balance", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current balance of the account, representing the amount of money available."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the account was created."}], "status": "deactivated", "sample_rows": "{\n  \"account_id\": \"random_account_id_123456\",\n  \"account_number\": \"random_account_number_789012\",\n  \"account_type\": \"random_account_type\",\n  \"balance\": \"random_balance_345678\",\n  \"created_at\": \"random_created_at_2023-10-01T12:00:00Z\"\n}"}, {"schema_name": "finance", "table_name": "finance.transactions", "description": "This table contains the following fields: transaction_id, account_id, transaction_type, amount, description, transaction_date. The 'transactions' table is designed to record all financial transactions associated with user accounts, providing a comprehensive log of each transaction's details for tracking and reporting purposes.", "fields": [{"name": "transaction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.transactions_transaction_id_seq'::regclass)", "description": "A unique identifier for each transaction."}, {"name": "account_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the account associated with the transaction."}, {"name": "transaction_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of transaction, such as 'credit' or 'debit'.", "found_categorical_values": ["deposit", "transfer", "withdrawal"]}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The monetary amount involved in the transaction."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A brief description of the transaction."}, {"name": "transaction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the transaction occurred."}], "status": "deactivated", "sample_rows": "{\n  \"transaction_id\": \"random_transaction_id_123456\",\n  \"account_id\": \"random_account_id_654321\",\n  \"transaction_type\": \"random_transaction_type\",\n  \"amount\": \"random_amount_7890\",\n  \"description\": \"random_description_text\",\n  \"transaction_date\": \"random_transaction_date_2023-10-01\"\n}"}, {"schema_name": "finance", "table_name": "finance.invoices", "description": "This table contains the following fields: invoice_id, customer_id, invoice_number, amount, status, due_date, created_at. The 'invoices' table is designed to store billing information for customers, tracking the details of each invoice issued, including the amount due, payment status, and relevant dates.", "fields": [{"name": "invoice_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('finance.invoices_invoice_id_seq'::regclass)", "description": "A unique identifier for each invoice."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A reference to the customer associated with the invoice."}, {"name": "invoice_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique number assigned to the invoice for tracking purposes."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount due on the invoice."}, {"name": "status", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the invoice (e.g., paid, pending, overdue).", "found_categorical_values": ["overdue", "paid", "pending"]}, {"name": "due_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date by which the invoice must be paid."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the invoice was created."}], "status": "deactivated", "sample_rows": "{\n  \"invoice_id\": \"random_invoice_id_123456\",\n  \"customer_id\": \"random_customer_id_654321\",\n  \"invoice_number\": \"INV-2023-0001\",\n  \"amount\": \"1500.00\",\n  \"status\": \"paid\",\n  \"due_date\": \"2023-12-31\",\n  \"created_at\": \"2023-01-15T10:00:00Z\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.product_promotions", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationship between products and their associated promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment.", "fields": [{"name": "product_promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.product_promotions_product_promotion_id_seq'::regclass)", "description": "A unique identifier for each product promotion record."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product that is associated with the promotion."}, {"name": "promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the promotion that is being applied to the product."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.return_items", "description": "This table contains the following fields: return_item_id, return_id, order_item_id, quantity, condition. The purpose of the 'return_items' table is to track individual items that are returned by customers, linking them to specific return transactions and the original order items. It helps manage inventory and customer service processes related to product returns.", "fields": [{"name": "return_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.return_items_return_item_id_seq'::regclass)", "description": "A unique identifier for each return item record."}, {"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A reference to the associated return transaction, linking to the overall return process."}, {"name": "order_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A reference to the original order item that is being returned."}, {"name": "quantity", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units of the item being returned."}, {"name": "condition", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The condition of the returned item, indicating whether it is new, used, damaged, etc."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.product_images", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The purpose of the 'product_images' table is to store and manage images associated with products in an e-commerce database. It allows for the organization of multiple images for each product, including designating a primary image for display purposes.", "fields": [{"name": "image_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.product_images_image_id_seq'::regclass)", "description": "A unique identifier for each image in the table."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that the image is associated with."}, {"name": "image_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The URL where the image is stored, allowing it to be accessed and displayed."}, {"name": "is_primary", "data_type": "boolean", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "false", "description": "A boolean flag indicating whether this image is the primary image for the associated product."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.returns", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers, capturing essential details about each return request, including the associated order and customer information, the reason for the return, the current status of the return process, and the timestamp of when the return was created.", "fields": [{"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.returns_return_id_seq'::regclass)", "description": "A unique identifier for each return record."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the order associated with the return."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the customer who initiated the return."}, {"name": "reason", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The reason provided by the customer for returning the product."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the return process (e.g., pending, completed, rejected)."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the return request was created."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.shipping", "description": "This table contains information related to the shipment of orders. It tracks the details of each shipment, including the carrier used, tracking information, and the current status of the shipment. This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery.", "fields": [{"name": "shipping_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.shipping_shipping_id_seq'::regclass)", "description": "A unique identifier for each shipping record."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the order associated with this shipment."}, {"name": "carrier", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the shipping carrier responsible for delivering the package."}, {"name": "tracking_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The tracking number provided by the carrier to monitor the shipment's progress."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the shipment (e.g., pending, shipped, delivered)."}, {"name": "estimated_delivery", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The estimated date of delivery for the shipment."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.cart", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is designed to store information about shopping carts in an e-commerce application. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp of when the cart was created.", "fields": [{"name": "cart_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.cart_cart_id_seq'::regclass)", "description": "A unique identifier for each shopping cart."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the customer who owns the shopping cart."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the shopping cart was created."}], "status": "deactivated", "sample_rows": "{\n  \"cart_id\": \"cart_123456789\",\n  \"customer_id\": \"customer_987654321\",\n  \"created_at\": \"2023-10-01T12:00:00Z\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.wishlist", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is used to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, facilitating easier access and potential purchase later on.", "fields": [{"name": "wishlist_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.wishlist_wishlist_id_seq'::regclass)", "description": "A unique identifier for each wishlist entry."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the customer who owns the wishlist."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that is added to the wishlist."}, {"name": "added_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the product was added to the wishlist."}], "status": "deactivated", "sample_rows": []}, {"schema_name": "ecommerce", "table_name": "ecommerce.order_items", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is designed to store details about individual items within customer orders. Each record corresponds to a specific item in an order, capturing essential information such as the product, the quantity ordered, and the price per unit.", "fields": [{"name": "order_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.order_items_order_item_id_seq'::regclass)", "description": "A unique identifier for each order item."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the order to which this item belongs."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product being ordered."}, {"name": "quantity", "data_type": "integer", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units of the product ordered.", "found_categorical_values": [1, 2, 3, 4, 5]}, {"name": "unit_price", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The price per unit of the product at the time of the order."}], "status": "deactivated", "sample_rows": "{\n  \"order_item_id\": \"random_order_item_id_123456\",\n  \"order_id\": \"random_order_id_654321\",\n  \"product_id\": \"random_product_id_789012\",\n  \"quantity\": 10,\n  \"unit_price\": 29.99\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.cart_items", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is designed to store individual items that are added to a shopping cart in an e-commerce application. Each entry represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process.", "fields": [{"name": "cart_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.cart_items_cart_item_id_seq'::regclass)", "description": "A unique identifier for each item in the cart."}, {"name": "cart_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the shopping cart to which the item belongs."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that is being added to the cart."}, {"name": "quantity", "data_type": "integer", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units of the product that the user wishes to purchase.", "found_categorical_values": [1, 2, 3, 4, 5]}], "status": "deactivated", "sample_rows": "{\n  \"cart_item_id\": \"random_cart_item_id_123456\",\n  \"cart_id\": \"random_cart_id_654321\",\n  \"product_id\": \"random_product_id_789012\",\n  \"quantity\": \"random_quantity_3\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.categories", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to organize and manage product categories within a database, allowing for hierarchical relationships between categories and providing detailed information about each category.", "fields": [{"name": "category_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.categories_category_id_seq'::regclass)", "description": "A unique identifier for each category, typically an integer that serves as the primary key."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the category, which is used to identify it in user interfaces and reports."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A textual description of the category, providing additional context or details about its purpose."}, {"name": "parent_category_id", "data_type": "integer", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "An optional field that references the category_id of a parent category, allowing for the creation of a hierarchical structure among categories.", "found_categorical_values": [101, 102, 103, 104, 105, 106, 107, 108, 111, 112, 113, 114, 115, 116, 117, 119, 120, 121, 123, 125, 131, 133, 135, 136, 142, 147]}], "status": "deactivated", "sample_rows": "{\n  \"category_id\": \"random_category_id_123\",\n  \"name\": \"random_name_456\",\n  \"description\": \"random_description_789\",\n  \"parent_category_id\": \"random_parent_category_id_101\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.promotions", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is used to store information about various promotional offers available to customers, including details about the promotion's identity, its specifics, and the time frame during which it is valid.", "fields": [{"name": "promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.promotions_promotion_id_seq'::regclass)", "description": "A unique identifier for each promotion."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the promotion."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A detailed description of the promotion."}, {"name": "discount_percentage", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The percentage discount offered by the promotion."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the promotion becomes active."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The date when the promotion expires."}], "status": "deactivated", "sample_rows": "{\n  \"promotion_id\": \"promo_123456\",\n  \"name\": \"Summer Sale\",\n  \"description\": \"Get ready for the summer with our exclusive discounts!\",\n  \"discount_percentage\": \"25\",\n  \"start_date\": \"2023-06-01\",\n  \"end_date\": \"2023-06-30\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.orders", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is designed to store information about customer orders placed in the system, tracking essential details such as the customer who made the order, the date of the order, the total amount charged, the current status of the order, the shipping address for delivery, and the payment details associated with the order.", "fields": [{"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.orders_order_id_seq'::regclass)", "description": "A unique identifier for each order."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer who placed the order."}, {"name": "order_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date when the order was placed."}, {"name": "total_amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total monetary amount for the order."}, {"name": "status", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the order (e.g., pending, shipped, delivered).", "found_categorical_values": ["cancelled", "delivered", "pending", "processing", "shipped"]}, {"name": "shipping_address", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The address where the order will be shipped."}, {"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the payment transaction associated with the order."}], "status": "deactivated", "sample_rows": "{\n  \"order_id\": \"order_123456\",\n  \"customer_id\": \"customer_789012\",\n  \"order_date\": \"2023-10-01T12:00:00Z\",\n  \"total_amount\": 150.75,\n  \"status\": \"shipped\",\n  \"shipping_address\": \"123 Fake Street, Faketown, FK 12345\",\n  \"payment_id\": \"payment_345678\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.products", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the various products available for sale in an inventory system. It includes details such as product identification, pricing, stock levels, and categorization to facilitate product management and sales tracking.", "fields": [{"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.products_product_id_seq'::regclass)", "description": "A unique identifier for each product in the table."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the product."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A detailed description of the product, including features and specifications."}, {"name": "price", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The selling price of the product."}, {"name": "stock_quantity", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current quantity of the product available in stock."}, {"name": "category_id", "data_type": "integer", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A reference identifier linking the product to its category.", "found_categorical_values": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150]}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the product was added to the inventory."}], "status": "deactivated", "sample_rows": "{\n  \"product_id\": \"random_product_id_123456\",\n  \"name\": \"Random Product Name\",\n  \"description\": \"This is a description of a random product.\",\n  \"price\": \"19.99\",\n  \"stock_quantity\": \"100\",\n  \"category_id\": \"random_category_id_789012\",\n  \"created_at\": \"2023-10-01T12:00:00Z\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.customers", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have registered or made purchases from the business. It serves as a central repository for customer data, enabling efficient management of customer relationships and communication.", "fields": [{"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.customers_customer_id_seq'::regclass)", "description": "A unique identifier for each customer, typically an integer or UUID."}, {"name": "first_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The first name of the customer."}, {"name": "last_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The last name of the customer."}, {"name": "email", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The email address of the customer, used for communication and account verification."}, {"name": "phone", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The contact phone number of the customer."}, {"name": "address", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The physical address of the customer, which may include street, city, state, and zip code."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the customer record was created."}], "status": "deactivated", "sample_rows": "{\n  \"customer_id\": \"customer_123456\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+**********\",\n  \"address\": \"123 Fake Street, Faketown, FK 12345\",\n  \"created_at\": \"2023-10-01T12:00:00Z\"\n}"}, {"schema_name": "ecommerce", "table_name": "ecommerce.reviews", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to gather insights on product performance and customer satisfaction. Each entry in this table represents an individual review submitted by a customer, including their rating and comments about the product.", "fields": [{"name": "review_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": "nextval('ecommerce.reviews_review_id_seq'::regclass)", "description": "A unique identifier for each review."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product being reviewed."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the customer who submitted the review."}, {"name": "rating", "data_type": "integer", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The rating given by the customer, typically on a scale from 1 to 5.", "found_categorical_values": [1, 2, 3, 4, 5]}, {"name": "comment", "data_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The textual feedback provided by the customer regarding the product."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the review was created."}], "status": "deactivated", "sample_rows": "{\n  \"review_id\": \"random_review_id_123456\",\n  \"product_id\": \"random_product_id_654321\",\n  \"customer_id\": \"random_customer_id_789012\",\n  \"rating\": \"5\",\n  \"comment\": \"This is a great product!\",\n  \"created_at\": \"2023-10-01T12:00:00Z\"\n}"}]}