{"report": {"total_databases": 3, "analyzed_databases": [{"database_name": "finance", "tables_count": 15, "successful_tables": 15, "failed_tables": 0, "error": null}, {"database_name": "ecommerce", "tables_count": 14, "successful_tables": 14, "failed_tables": 0, "error": null}, {"database_name": "analytics", "tables_count": 14, "successful_tables": 14, "failed_tables": 0, "error": null}], "total_tables_found": 43, "successfully_analyzed": 43, "failed_analysis": 0, "failed_tables": [], "execution_time": 14.93}, "tables": [{"database_name": "finance", "table_name": "finance.investment_returns", "description": "This table contains the following fields: return_id, investment_id, amount, return_date. The purpose of the 'investment_returns' table is to track the returns generated from various investments over time, allowing for analysis of investment performance and profitability.", "fields": [{"name": "return_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each return record."}, {"name": "investment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the investment associated with the return."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The monetary amount of the return generated from the investment."}, {"name": "return_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date on which the return was realized."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.currency_exchange", "description": "This table contains the following fields: exchange_id, from_currency, to_currency, rate, exchange_date. The purpose of the 'currency_exchange' table is to store information about currency exchange transactions, including the currencies involved, the exchange rate applied, and the date of the transaction.", "fields": [{"name": "exchange_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each currency exchange transaction."}, {"name": "from_currency", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(3)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The currency code of the currency being exchanged from."}, {"name": "to_currency", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(3)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The currency code of the currency being exchanged to."}, {"name": "rate", "data_type": "decimal", "column_type": "decimal(10,4)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The exchange rate applied for the currency conversion."}, {"name": "exchange_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date on which the currency exchange transaction took place."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.budgets", "description": "This table contains the following fields: budget_id, category, amount, start_date, end_date. The 'budgets' table is designed to store information about financial budgets, allowing users to track their planned expenditures across different categories over specified time periods.", "fields": [{"name": "budget_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each budget entry."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category of the budget, such as 'Food', 'Utilities', or 'Entertainment'."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount allocated for the budget category."}, {"name": "start_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the budget period begins."}, {"name": "end_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the budget period ends."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.financial_reports", "description": "This table contains the following fields: report_id, report_type, period, generated_at, content. The purpose of the 'financial_reports' table is to store detailed information about financial reports generated by the organization, including their types, reporting periods, and the actual content of the reports.", "fields": [{"name": "report_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each financial report."}, {"name": "report_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of financial report, such as income statement, balance sheet, or cash flow statement."}, {"name": "period", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The reporting period for which the financial report is generated, typically in the format of a date range."}, {"name": "generated_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the financial report was generated."}, {"name": "content", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The actual content or data of the financial report, which may include figures, tables, and narrative descriptions."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.credit_card_transactions", "description": "This table contains the following fields: transaction_id, credit_card_id, amount, merchant, transaction_date. The purpose of the 'credit_card_transactions' table is to store detailed records of all transactions made using credit cards, allowing for tracking and analysis of spending patterns, merchant interactions, and transaction history.", "fields": [{"name": "transaction_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each transaction."}, {"name": "credit_card_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the credit card used in the transaction."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money spent in the transaction."}, {"name": "merchant", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the merchant where the transaction took place."}, {"name": "transaction_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the transaction occurred."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.credit_cards", "description": "This table contains information about credit cards issued to customers. It is used to manage and track credit card details, including the cardholder's information and financial limits. This table contains the following fields: credit_card_id, card_number, card_holder, expiry_date, credit_limit, current_balance.", "fields": [{"name": "credit_card_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each credit card record."}, {"name": "card_number", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number associated with the credit card, typically 16 digits."}, {"name": "card_holder", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the individual to whom the credit card is issued."}, {"name": "expiry_date", "data_type": "date", "column_type": "date", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the credit card expires, after which it is no longer valid."}, {"name": "credit_limit", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The maximum amount of credit that can be used on the card."}, {"name": "current_balance", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current outstanding balance on the credit card."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.expenses", "description": "This table contains the following fields: expense_id, category, amount, description, expense_date, payment_method. The 'expenses' table is designed to track and manage financial expenditures made by individuals or organizations. It records essential details about each expense to facilitate budgeting, reporting, and financial analysis.", "fields": [{"name": "expense_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each expense entry."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category under which the expense falls, such as travel, food, or utilities."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The monetary value of the expense."}, {"name": "description", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A brief description providing additional details about the expense."}, {"name": "expense_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the expense was incurred."}, {"name": "payment_method", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The method used to make the payment, such as credit card, cash, or bank transfer."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.investments", "description": "This table contains the following fields: investment_id, investment_type, amount, start_date, end_date, return_rate. The 'investments' table is designed to track various investment records, detailing the specifics of each investment made by individuals or entities, including the type of investment, the amount invested, the duration of the investment, and the expected return rate.", "fields": [{"name": "investment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each investment record."}, {"name": "investment_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of investment, such as stocks, bonds, real estate, etc."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money invested."}, {"name": "start_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the investment begins."}, {"name": "end_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": null, "description": "The date when the investment matures or is expected to end."}, {"name": "return_rate", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The expected rate of return on the investment, usually expressed as a percentage."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.loan_payments", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is designed to track the payments made towards loans, capturing essential details about each transaction to ensure accurate financial records and reporting.", "fields": [{"name": "payment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each payment record."}, {"name": "loan_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the loan associated with the payment."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The amount of money paid in this transaction."}, {"name": "payment_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the payment was made."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the payment (e.g., completed, pending, failed)."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.payments", "description": "This table contains the following fields: payment_id, invoice_id, amount, payment_method, payment_date, status. The 'payments' table is designed to store information related to financial transactions made by customers for their invoices. It tracks the details of each payment, including the amount paid, the method of payment, and the status of the transaction.", "fields": [{"name": "payment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each payment transaction."}, {"name": "invoice_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the invoice associated with the payment."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money that was paid."}, {"name": "payment_method", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The method used to make the payment (e.g., credit card, cash, bank transfer)."}, {"name": "payment_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date when the payment was made."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the payment (e.g., completed, pending, failed)."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.tax_records", "description": "This table contains the following fields: tax_record_id, tax_type, amount, tax_period, due_date, status. The 'tax_records' table is designed to store information related to various tax obligations for individuals or entities. It tracks the type of tax, the amount owed, the period for which the tax is applicable, the due date for payment, and the current status of the tax record.", "fields": [{"name": "tax_record_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each tax record."}, {"name": "tax_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of tax being recorded (e.g., income tax, property tax, sales tax)."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of tax owed."}, {"name": "tax_period", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The specific period for which the tax is applicable."}, {"name": "due_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date by which the tax payment must be made."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the tax record (e.g., paid, unpaid, overdue)."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.loans", "description": "This table contains information about various loans issued to borrowers. It tracks the details of each loan, including its type, amount, interest rate, duration, and current status. This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status.", "fields": [{"name": "loan_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each loan."}, {"name": "loan_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category of the loan (e.g., personal, mortgage, auto, etc.)."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount of money borrowed."}, {"name": "interest_rate", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The percentage of interest charged on the loan amount."}, {"name": "start_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the loan period begins."}, {"name": "end_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the loan is scheduled to be fully paid off."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the loan (e.g., active, paid off, defaulted, etc.)."}], "status": "deactivated", "sample_rows": []}, {"database_name": "finance", "table_name": "finance.accounts", "description": "This table contains the following fields: account_id, account_number, account_type, balance, created_at. The 'accounts' table is designed to store information related to user accounts within a financial system. It tracks essential details such as account identification, type, balance, and creation date, facilitating account management and financial transactions.", "fields": [{"name": "account_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each account, typically an auto-incrementing integer."}, {"name": "account_number", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique number assigned to the account for identification in transactions."}, {"name": "account_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of account, such as savings, checking, or credit.", "found_categorical_values": ["checking", "investment", "savings"]}, {"name": "balance", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current balance of the account, representing the amount of money available."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the account was created."}], "status": "deactivated", "sample_rows": "{\n  \"account_id\": 2,\n  \"account_number\": \"ACC-123456-7890\",\n  \"account_type\": \"savings\",\n  \"balance\": 5678.45,\n  \"created_at\": \"2025-04-15T14:20:30\"\n}"}, {"database_name": "finance", "table_name": "finance.invoices", "description": "This table contains the following fields: invoice_id, customer_id, invoice_number, amount, status, due_date, created_at. The 'invoices' table is used to store information about customer invoices, including details such as the unique identifier for each invoice, the customer associated with the invoice, the invoice number for reference, the total amount due, the current status of the invoice, the due date for payment, and the date the invoice was created.", "fields": [{"name": "invoice_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each invoice."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer associated with the invoice."}, {"name": "invoice_number", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A reference number assigned to the invoice for tracking purposes."}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total amount due on the invoice."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the invoice (e.g., paid, unpaid, overdue).", "found_categorical_values": ["overdue", "paid", "pending"]}, {"name": "due_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date by which the invoice must be paid."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the invoice was created."}], "status": "deactivated", "sample_rows": "{\n  \"invoice_id\": 2,\n  \"customer_id\": 839,\n  \"invoice_number\": \"INV-********-0000\",\n  \"amount\": 2450.75,\n  \"status\": \"paid\",\n  \"due_date\": \"2025-05-20T14:30:12\",\n  \"created_at\": \"2025-02-15T09:45:30\"\n}"}, {"database_name": "finance", "table_name": "finance.transactions", "description": "This table contains the following fields: transaction_id, account_id, transaction_type, amount, description, transaction_date. The 'transactions' table is designed to record all financial transactions associated with user accounts, capturing essential details about each transaction for tracking and reporting purposes.", "fields": [{"name": "transaction_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each transaction."}, {"name": "account_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the account associated with the transaction."}, {"name": "transaction_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of transaction, such as 'credit' or 'debit'.", "found_categorical_values": ["deposit", "transfer", "withdrawal"]}, {"name": "amount", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The monetary amount involved in the transaction."}, {"name": "description", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A brief description of the transaction."}, {"name": "transaction_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the transaction occurred."}], "status": "deactivated", "sample_rows": "{\n  \"transaction_id\": 2,\n  \"account_id\": 572,\n  \"transaction_type\": \"withdrawal\",\n  \"amount\": 432.15,\n  \"description\": \"Strategy how platform engage.\",\n  \"transaction_date\": \"2025-03-15T10:45:30\"\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.product_promotions", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationships between products and their associated promotions, allowing for efficient tracking and application of promotional offers to specific products.", "fields": [{"name": "product_promotion_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each product promotion entry."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product that is associated with the promotion."}, {"name": "promotion_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the promotion that is linked to the product."}], "status": "deactivated", "sample_rows": []}, {"database_name": "ecommerce", "table_name": "ecommerce.product_images", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The 'product_images' table is designed to store images associated with products in an e-commerce database. It allows for the management of multiple images per product, including the designation of a primary image for display purposes.", "fields": [{"name": "image_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each image in the table."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product to which the image belongs."}, {"name": "image_url", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The URL link to the image file stored in the database."}, {"name": "is_primary", "data_type": "tinyint", "column_type": "tinyint(1)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": "0", "description": "A boolean flag indicating whether this image is the primary image for the associated product."}], "status": "deactivated", "sample_rows": []}, {"database_name": "ecommerce", "table_name": "ecommerce.returns", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers. It records essential information about each return, including the associated order, the customer making the return, the reason for the return, the current status of the return process, and the date the return was created.", "fields": [{"name": "return_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each return record."}, {"name": "order_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the order associated with the return."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the customer who initiated the return."}, {"name": "reason", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The reason provided by the customer for returning the product."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the return (e.g., pending, processed, completed)."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the return record was created."}], "status": "deactivated", "sample_rows": []}, {"database_name": "ecommerce", "table_name": "ecommerce.cart", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is used to store information about shopping carts created by customers. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp when the cart was created.", "fields": [{"name": "cart_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each shopping cart."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the customer who owns the shopping cart."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the shopping cart was created."}], "status": "deactivated", "sample_rows": "{\n  \"cart_id\": 2,\n  \"customer_id\": 912,\n  \"created_at\": \"2025-02-15T14:30:45\"\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.promotions", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is used to store information about promotional offers available to customers, including details about the promotion's identity, its characteristics, and the duration for which it is valid.", "fields": [{"name": "promotion_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each promotion."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the promotion."}, {"name": "description", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A detailed description of the promotion."}, {"name": "discount_percentage", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The percentage discount offered by the promotion."}, {"name": "start_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the promotion becomes active."}, {"name": "end_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the promotion expires."}], "status": "deactivated", "sample_rows": "{\n  \"promotion_id\": 2,\n  \"name\": \"Dynamic adaptive algorithm\",\n  \"description\": \"Explore both engagement unique location any. Complete expert insight. Approach wide participant.\\nStyle include however nearly deliver. Our greater response diverse ok.\",\n  \"discount_percentage\": 15.75,\n  \"start_date\": \"2025-02-10T12:45:00\",\n  \"end_date\": \"2025-02-25T12:45:00\"\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.order_items", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is used to store details about individual items within a customer order. Each record represents a specific product included in an order, along with its quantity and price, allowing for accurate tracking and management of sales transactions.", "fields": [{"name": "order_item_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each order item."}, {"name": "order_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the order to which this item belongs."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product being ordered."}, {"name": "quantity", "data_type": "int", "column_type": "int", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units of the product ordered.", "found_categorical_values": [1, 2, 3, 4, 5]}, {"name": "unit_price", "data_type": "decimal", "column_type": "decimal(10,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The price per unit of the product at the time of the order."}], "status": "deactivated", "sample_rows": "{\n  \"order_item_id\": 2,\n  \"order_id\": 5,\n  \"product_id\": 456,\n  \"quantity\": 3,\n  \"unit_price\": 89.99\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.categories", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to organize and manage product categories within a database, allowing for hierarchical relationships between categories and facilitating efficient categorization of products.", "fields": [{"name": "category_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each category, typically an integer that serves as the primary key."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the category, which is used to identify it in user interfaces and reports."}, {"name": "description", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A textual description of the category, providing additional details about its purpose or contents."}, {"name": "parent_category_id", "data_type": "int", "column_type": "int", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "An optional field that references the category_id of a parent category, allowing for the creation of a hierarchical structure among categories.", "found_categorical_values": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 16, 18, 19, 22, 24, 25, 26, 27, 33, 40, 43]}], "status": "deactivated", "sample_rows": "{\n  \"category_id\": 42,\n  \"name\": \"Premium\",\n  \"description\": \"Mountain lake adventure journey.\",\n  \"parent_category_id\": null\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.shipping", "description": "This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery. The 'shipping' table is used to store information related to the shipment of orders, including details about the carrier, tracking information, and the current status of the shipment.", "fields": [{"name": "shipping_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each shipping record."}, {"name": "order_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the order associated with this shipment."}, {"name": "carrier", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the shipping carrier responsible for delivering the package."}, {"name": "tracking_number", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The tracking number provided by the carrier to monitor the shipment's progress."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the shipment (e.g., pending, shipped, delivered)."}, {"name": "estimated_delivery", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": null, "description": "The estimated date and time when the shipment is expected to be delivered."}], "status": "deactivated", "sample_rows": []}, {"database_name": "ecommerce", "table_name": "ecommerce.orders", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is used to store information about customer orders placed in the system, tracking details such as the customer who placed the order, the date of the order, the total amount charged, the current status of the order, the shipping address for delivery, and the associated payment details.", "fields": [{"name": "order_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each order."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer who placed the order."}, {"name": "order_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date when the order was placed."}, {"name": "total_amount", "data_type": "decimal", "column_type": "decimal(10,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total monetary amount for the order."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the order (e.g., pending, shipped, delivered, canceled).", "found_categorical_values": ["cancelled", "delivered", "pending", "processing", "shipped"]}, {"name": "shipping_address", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The address where the order will be shipped."}, {"name": "payment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the payment transaction associated with the order."}], "status": "deactivated", "sample_rows": "{\n  \"order_id\": 2,\n  \"customer_id\": 512,\n  \"order_date\": \"2025-02-15T12:45:00\",\n  \"total_amount\": 289.99,\n  \"status\": \"processing\",\n  \"shipping_address\": \"4821 Maple Avenue\\nNorth Newtown, CA 98765\",\n  \"payment_id\": null\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.cart_items", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is designed to store information about the individual items that are added to a shopping cart in an e-commerce application. Each entry in this table represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process.", "fields": [{"name": "cart_item_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each item in the cart."}, {"name": "cart_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the shopping cart to which the item belongs."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that is added to the cart."}, {"name": "quantity", "data_type": "int", "column_type": "int", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units of the product that the user intends to purchase.", "found_categorical_values": [1, 2, 3, 4, 5]}], "status": "deactivated", "sample_rows": "{\n  \"cart_item_id\": 123,\n  \"cart_id\": 456,\n  \"product_id\": 789,\n  \"quantity\": 2\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.wishlist", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is designed to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, making it easier for them to find and buy these products later.", "fields": [{"name": "wishlist_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each wishlist entry."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the customer who owns the wishlist."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that is added to the wishlist."}, {"name": "added_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the product was added to the wishlist."}], "status": "deactivated", "sample_rows": []}, {"database_name": "ecommerce", "table_name": "ecommerce.products", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the items available for sale in an inventory system. It includes details such as product identification, pricing, stock levels, and categorization to facilitate product management and sales tracking.", "fields": [{"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each product in the table."}, {"name": "name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the product."}, {"name": "description", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A detailed description of the product."}, {"name": "price", "data_type": "decimal", "column_type": "decimal(10,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The selling price of the product."}, {"name": "stock_quantity", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The number of units available in stock for the product."}, {"name": "category_id", "data_type": "int", "column_type": "int", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A reference to the category to which the product belongs.", "found_categorical_values": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the product was added to the inventory."}], "status": "deactivated", "sample_rows": "{\n  \"product_id\": 2,\n  \"name\": \"Dynamic adaptive system\",\n  \"description\": \"Ensure sufficient protection exist. Region device surprise artificial mouth essence might. Demand only activity social tone all fluid.\",\n  \"price\": 523.89,\n  \"stock_quantity\": 312,\n  \"category_id\": 7,\n  \"created_at\": \"2025-09-15T12:45:30\"\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.customers", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have made purchases or engaged with the business, allowing for effective customer management and communication.", "fields": [{"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each customer, typically an auto-incrementing integer."}, {"name": "first_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The first name of the customer."}, {"name": "last_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The last name of the customer."}, {"name": "email", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The email address of the customer, used for communication and account verification."}, {"name": "phone", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The contact phone number of the customer."}, {"name": "address", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The physical address of the customer, including street, city, state, and zip code."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the customer record was created in the database."}], "status": "deactivated", "sample_rows": "{\n  \"customer_id\": 2,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+************\",\n  \"address\": \"123 Maple Street Apt. 456\\nSpringfield, ID 12345\",\n  \"created_at\": \"2025-06-15T12:34:56\"\n}"}, {"database_name": "ecommerce", "table_name": "ecommerce.reviews", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to gather insights on customer satisfaction and product performance. Each entry represents an individual review submitted by a customer, including their rating and comments about the product.", "fields": [{"name": "review_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each review."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product that the review is associated with."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the customer who submitted the review."}, {"name": "rating", "data_type": "int", "column_type": "int", "is_categorical": true, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The rating given by the customer, typically on a scale from 1 to 5.", "found_categorical_values": [1, 2, 3, 4, 5]}, {"name": "comment", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The textual feedback provided by the customer regarding the product."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the review was created."}], "status": "deactivated", "sample_rows": "{\n  \"review_id\": 2,\n  \"product_id\": 912,\n  \"customer_id\": 738,\n  \"rating\": 4,\n  \"comment\": \"Local between industry expert create. Feedback operate over fifty team find evaluate many. Essential low junior both.\",\n  \"created_at\": \"2025-03-15T12:45:30\"\n}"}, {"database_name": "analytics", "table_name": "analytics.customer_segments", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The purpose of the 'customer_segments' table is to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement.", "fields": [{"name": "segment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each customer segment."}, {"name": "segment_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name assigned to the customer segment, representing its characteristics."}, {"name": "criteria", "data_type": "json", "column_type": "json", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The specific conditions or attributes that define the segment."}, {"name": "created_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the customer segment was created."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.campaign_metrics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics associated with marketing campaigns, allowing for analysis and reporting on the effectiveness of various campaigns over time.", "fields": [{"name": "metric_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each metric entry."}, {"name": "campaign_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the campaign to which the metric belongs."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the metric being recorded (e.g., clicks, impressions, conversions)."}, {"name": "metric_value", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The numerical value of the metric for the specified date."}, {"name": "metric_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the metric was recorded."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.performance_metrics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on the performance of different categories within an organization.", "fields": [{"name": "metric_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each performance metric entry."}, {"name": "metric_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the performance metric being tracked."}, {"name": "metric_value", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The numerical value of the performance metric."}, {"name": "metric_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the performance metric was recorded."}, {"name": "category", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The category under which the performance metric falls, helping to organize metrics by type."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.conversion_funnel", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The purpose of the 'conversion_funnel' table is to track the progress of users through various stages of a conversion process, capturing key data points that help analyze user behavior and conversion rates.", "fields": [{"name": "funnel_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each conversion funnel entry."}, {"name": "user_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the user associated with the conversion funnel entry."}, {"name": "stage", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current stage of the user in the conversion funnel (e.g., awareness, consideration, decision)."}, {"name": "stage_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the user reached the current stage in the funnel."}, {"name": "conversion_value", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The monetary value associated with the conversion at this stage, if applicable."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.inventory_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is designed to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, enabling better inventory management and planning.", "fields": [{"name": "forecast_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each forecast entry."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product associated with the forecast."}, {"name": "forecast_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date for which the inventory forecast is made."}, {"name": "predicted_stock", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The estimated amount of stock expected to be available on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that expresses the degree of uncertainty around the predicted stock."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.marketing_campaigns", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is designed to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses to facilitate tracking and analysis of marketing efforts.", "fields": [{"name": "campaign_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each marketing campaign."}, {"name": "campaign_name", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The name of the marketing campaign."}, {"name": "start_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the marketing campaign begins."}, {"name": "end_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the marketing campaign ends."}, {"name": "budget", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The total budget allocated for the marketing campaign."}, {"name": "status", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The current status of the marketing campaign (e.g., active, completed, paused)."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.customer_lifetime_value", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage the calculated lifetime value of customers, which helps businesses understand the long-term value of their customer relationships and make informed decisions regarding marketing and customer retention strategies.", "fields": [{"name": "clv_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each customer lifetime value record."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer associated with the lifetime value."}, {"name": "calculated_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date on which the customer lifetime value was calculated."}, {"name": "predicted_value", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The predicted monetary value that a customer is expected to generate over their lifetime."}, {"name": "confidence_interval", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that indicates the level of certainty around the predicted value."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.churn_prediction", "description": "This table is designed to store and analyze customer churn predictions for a business. It helps in identifying customers who are likely to leave, allowing for proactive retention strategies. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level.", "fields": [{"name": "prediction_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each churn prediction record."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier for the customer associated with the churn prediction."}, {"name": "prediction_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date when the churn prediction was made."}, {"name": "churn_probability", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The estimated probability that the customer will churn, expressed as a percentage."}, {"name": "risk_level", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(20)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A categorical assessment of the customer's risk of churning, such as 'low', 'medium', or 'high'."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.sales_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The 'sales_forecast' table is designed to store and manage sales predictions for various products over specific time periods. It allows businesses to analyze expected sales trends and make informed decisions based on forecasted data.", "fields": [{"name": "forecast_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each sales forecast entry."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier for the product associated with the sales forecast."}, {"name": "forecast_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": false, "column_default": null, "description": "The date for which the sales forecast is made."}, {"name": "predicted_sales", "data_type": "decimal", "column_type": "decimal(15,2)", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The estimated number of units expected to be sold on the forecast date."}, {"name": "confidence_interval", "data_type": "decimal", "column_type": "decimal(5,2)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A statistical range that indicates the reliability of the predicted sales figure."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.search_queries", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within an application. It tracks each search made by users, including who made the search, what was searched for, when it occurred, and how many results were returned.", "fields": [{"name": "query_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each search query."}, {"name": "user_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the user who performed the search."}, {"name": "query_text", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The actual text of the search query entered by the user."}, {"name": "search_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the search was performed."}, {"name": "results_count", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The number of results returned for the search query."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.segment_members", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association of customers with specific segments in a marketing or analytics context. It allows businesses to manage and analyze customer segmentation effectively by linking customers to various segments based on their behaviors or characteristics.", "fields": [{"name": "member_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each member in the segment."}, {"name": "segment_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A unique identifier for the segment to which the member belongs."}, {"name": "customer_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A unique identifier for the customer associated with the segment."}, {"name": "added_at", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The timestamp indicating when the customer was added to the segment."}], "status": "deactivated", "sample_rows": []}, {"database_name": "analytics", "table_name": "analytics.product_views", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the interactions of users with products on an e-commerce platform. It records each instance a user views a product, capturing relevant details such as the user who viewed it, the product being viewed, and the duration of the view.", "fields": [{"name": "view_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each view record."}, {"name": "product_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the product that was viewed."}, {"name": "user_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the user who viewed the product."}, {"name": "view_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the product was viewed."}, {"name": "view_duration", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The duration of time (in seconds) that the product was viewed."}], "status": "deactivated", "sample_rows": "{\n  \"view_id\": 123,\n  \"product_id\": 456,\n  \"user_id\": 789,\n  \"view_date\": \"2025-03-01T12:00:00\",\n  \"view_duration\": 1500\n}"}, {"database_name": "analytics", "table_name": "analytics.page_views", "description": "This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The 'page_views' table is designed to track the views of web pages by users. It records each instance a user views a page, capturing essential details such as the specific page viewed, the user who viewed it, and the time of the view, which can be used for analytics and reporting purposes.", "fields": [{"name": "view_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each page view record."}, {"name": "page_url", "data_type": "text", "column_type": "text", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The URL of the page that was viewed."}, {"name": "user_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The unique identifier of the user who viewed the page."}, {"name": "view_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the page was viewed."}, {"name": "session_id", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(100)", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "A unique identifier for the session during which the page view occurred."}], "status": "deactivated", "sample_rows": "{\n  \"view_id\": 2,\n  \"page_url\": \"http://www.example.com/\",\n  \"user_id\": 789,\n  \"view_date\": \"2025-06-15T14:22:10\",\n  \"session_id\": \"f1a2b3c4-d5e6-7f8g-9h0i-j1k2l3m4n5o6\"\n}"}, {"database_name": "analytics", "table_name": "analytics.user_activity", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time.", "fields": [{"name": "activity_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "A unique identifier for each activity record."}, {"name": "user_id", "data_type": "int", "column_type": "int", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "The identifier of the user who performed the activity."}, {"name": "activity_type", "data_type": "<PERSON><PERSON><PERSON>", "column_type": "<PERSON><PERSON><PERSON>(50)", "is_categorical": true, "is_datetime": false, "is_nullable": false, "column_default": null, "description": "The type of activity performed by the user, such as 'login', 'purchase', or 'comment'.", "found_categorical_values": ["login", "logout", "search", "view_product"]}, {"name": "activity_date", "data_type": "timestamp", "column_type": "timestamp", "is_categorical": false, "is_datetime": true, "is_nullable": true, "column_default": "CURRENT_TIMESTAMP", "description": "The date and time when the activity occurred."}, {"name": "details", "data_type": "json", "column_type": "json", "is_categorical": false, "is_datetime": false, "is_nullable": true, "column_default": null, "description": "Additional information or context about the activity.", "json_type": "other_json"}], "status": "deactivated", "sample_rows": "{\n  \"activity_id\": 2,\n  \"user_id\": 987,\n  \"activity_type\": \"add_to_cart\",\n  \"activity_date\": \"2025-02-25T14:30:12\",\n  \"details\": \"{\\\"ip\\\": \\\"************\\\", \\\"browser\\\": \\\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36\\\"}\"\n}"}]}