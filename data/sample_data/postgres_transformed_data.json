{"provider": "snowflake", "schemas": [{"schema_id": "24341679-cce4-4e4c-9954-3de7fc0b0e00", "schema_name": "analytics", "description": "Snowflake schema containing 14 table(s)", "table_count": 14, "tables_overview": [{"table_name": "analytics.marketing_campaigns", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is designed to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, enabling effective tracking and management of marketing efforts."}, {"table_name": "analytics.customer_segments", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is designed to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement."}, {"table_name": "analytics.customer_lifetime_value", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage the calculated lifetime value of customers, which helps businesses understand the long-term value each customer brings based on their purchasing behavior and trends."}, {"table_name": "analytics.campaign_metrics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics related to marketing campaigns, allowing for analysis of campaign effectiveness over time."}, {"table_name": "analytics.churn_prediction", "description": "This table is designed to store information related to customer churn predictions for a business. It helps in analyzing customer behavior and identifying those who are at risk of leaving the service. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level."}, {"table_name": "analytics.conversion_funnel", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The 'conversion_funnel' table is designed to track the progress of users through various stages of a conversion process, allowing for analysis of user behavior and conversion rates at each stage."}, {"table_name": "analytics.inventory_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is designed to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, allowing for better inventory management and planning."}, {"table_name": "analytics.performance_metrics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on the performance of different entities or processes."}, {"table_name": "analytics.segment_members", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and segments within a marketing or customer relationship management system. It allows for the organization of customers into specific segments for targeted marketing efforts and analysis."}, {"table_name": "analytics.sales_forecast", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The purpose of the 'sales_forecast' table is to store and manage sales predictions for various products over specified time periods, allowing businesses to plan inventory and marketing strategies effectively."}, {"table_name": "analytics.search_queries", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within an application. It tracks each search made by users, including details about the user, the specific search terms they used, when the search occurred, and how many results were returned."}, {"table_name": "analytics.product_views", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the views of products by users on an e-commerce platform. It records each instance a user views a product, capturing relevant details such as the user, the product viewed, and the duration of the view."}, {"table_name": "analytics.page_views", "description": "This table records the details of each page view on the website. It tracks user interactions with different pages, allowing for analysis of traffic patterns and user engagement. This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The data collected can be used for reporting and improving user experience."}, {"table_name": "analytics.user_activity", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time."}]}, {"schema_id": "de0f0a40-6a34-4410-99d3-a92c12f65f0f", "schema_name": "finance", "description": "Snowflake schema containing 15 table(s)", "table_count": 15, "tables_overview": [{"table_name": "finance.currency_exchange", "description": "This table contains the following fields: exchange_id, from_currency, to_currency, rate, exchange_date. The purpose of the 'currency_exchange' table is to store information about currency exchange transactions, including the currencies involved, the exchange rate applied, and the date of the transaction."}, {"table_name": "finance.credit_card_transactions", "description": "This table contains the following fields: transaction_id, credit_card_id, amount, merchant, transaction_date. The purpose of this table is to store detailed records of all transactions made using credit cards, allowing for tracking and analysis of spending behavior, merchant activity, and transaction history."}, {"table_name": "finance.investments", "description": "This table contains the following fields: investment_id, investment_type, amount, start_date, end_date, return_rate. The 'investments' table is designed to track various investment records, detailing the specifics of each investment made, including the type, financial details, and duration of the investment."}, {"table_name": "finance.financial_reports", "description": "This table contains the following fields: report_id, report_type, period, generated_at, content. The 'financial_reports' table is designed to store detailed records of financial reports generated by the organization. Each entry in this table represents a unique financial report, capturing essential information about the type of report, the reporting period, the date it was generated, and the actual content of the report."}, {"table_name": "finance.investment_returns", "description": "This table contains the following fields: return_id, investment_id, amount, return_date. The purpose of the 'investment_returns' table is to track the returns generated from various investments over time, allowing for analysis of investment performance and profitability."}, {"table_name": "finance.credit_cards", "description": "This table contains the following fields: credit_card_id, card_number, card_holder, expiry_date, credit_limit, current_balance. The 'credit_cards' table is designed to store information about credit card accounts, including unique identifiers, card details, and financial limits associated with each card."}, {"table_name": "finance.budgets", "description": "This table contains the following fields: budget_id, category, amount, start_date, end_date. The 'budgets' table is designed to store financial planning information for various categories, allowing users to track their budget allocations over specified time periods."}, {"table_name": "finance.expenses", "description": "This table contains the following fields: expense_id, category, amount, description, expense_date, payment_method. The 'expenses' table is designed to track and manage financial expenditures made by an individual or organization. It records details about each expense, including its category, amount spent, a brief description, the date of the expense, and the method of payment used."}, {"table_name": "finance.loan_payments", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is designed to track the payments made towards loans, capturing essential details about each payment transaction. It helps in managing loan repayment schedules and monitoring the status of payments."}, {"table_name": "finance.payments", "description": "This table contains the following fields: payment_id, invoice_id, amount, payment_method, payment_date, status. The 'payments' table is designed to store information about financial transactions made by customers, tracking the details of each payment related to invoices, including the amount paid, the method of payment, and the status of the transaction."}, {"table_name": "finance.tax_records", "description": "This table contains the following fields: tax_record_id, tax_type, amount, tax_period, due_date, status. The 'tax_records' table is used to store information about various tax obligations for individuals or entities, including details about the type of tax, the amount owed, the period for which the tax is applicable, the due date for payment, and the current status of the tax record."}, {"table_name": "finance.loans", "description": "This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status. The 'loans' table is designed to store information about various loans issued to borrowers, including details about the type of loan, the amount borrowed, the interest rate applied, and the duration of the loan. It also tracks the current status of each loan, allowing for effective management and reporting of loan data."}, {"table_name": "finance.accounts", "description": "This table contains the following fields: account_id, account_number, account_type, balance, created_at. The 'accounts' table is designed to store information about user accounts in a financial system, including unique identifiers, account details, and balance information."}, {"table_name": "finance.transactions", "description": "This table contains the following fields: transaction_id, account_id, transaction_type, amount, description, transaction_date. The 'transactions' table is designed to record all financial transactions associated with user accounts, providing a comprehensive log of each transaction's details for tracking and reporting purposes."}, {"table_name": "finance.invoices", "description": "This table contains the following fields: invoice_id, customer_id, invoice_number, amount, status, due_date, created_at. The 'invoices' table is designed to store billing information for customers, tracking the details of each invoice issued, including the amount due, payment status, and relevant dates."}]}, {"schema_id": "9552d07f-efd2-4c9c-93c7-c77ae9e1807b", "schema_name": "ecommerce", "description": "Snowflake schema containing 15 table(s)", "table_count": 15, "tables_overview": [{"table_name": "ecommerce.product_promotions", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationship between products and their associated promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment."}, {"table_name": "ecommerce.return_items", "description": "This table contains the following fields: return_item_id, return_id, order_item_id, quantity, condition. The purpose of the 'return_items' table is to track individual items that are returned by customers, linking them to specific return transactions and the original order items. It helps manage inventory and customer service processes related to product returns."}, {"table_name": "ecommerce.product_images", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The purpose of the 'product_images' table is to store and manage images associated with products in an e-commerce database. It allows for the organization of multiple images for each product, including designating a primary image for display purposes."}, {"table_name": "ecommerce.returns", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers, capturing essential details about each return request, including the associated order and customer information, the reason for the return, the current status of the return process, and the timestamp of when the return was created."}, {"table_name": "ecommerce.shipping", "description": "This table contains information related to the shipment of orders. It tracks the details of each shipment, including the carrier used, tracking information, and the current status of the shipment. This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery."}, {"table_name": "ecommerce.cart", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is designed to store information about shopping carts in an e-commerce application. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp of when the cart was created."}, {"table_name": "ecommerce.wishlist", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is used to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, facilitating easier access and potential purchase later on."}, {"table_name": "ecommerce.order_items", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is designed to store details about individual items within customer orders. Each record corresponds to a specific item in an order, capturing essential information such as the product, the quantity ordered, and the price per unit."}, {"table_name": "ecommerce.cart_items", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is designed to store individual items that are added to a shopping cart in an e-commerce application. Each entry represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process."}, {"table_name": "ecommerce.categories", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to organize and manage product categories within a database, allowing for hierarchical relationships between categories and providing detailed information about each category."}, {"table_name": "ecommerce.promotions", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is used to store information about various promotional offers available to customers, including details about the promotion's identity, its specifics, and the time frame during which it is valid."}, {"table_name": "ecommerce.orders", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is designed to store information about customer orders placed in the system, tracking essential details such as the customer who made the order, the date of the order, the total amount charged, the current status of the order, the shipping address for delivery, and the payment details associated with the order."}, {"table_name": "ecommerce.products", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the various products available for sale in an inventory system. It includes details such as product identification, pricing, stock levels, and categorization to facilitate product management and sales tracking."}, {"table_name": "ecommerce.customers", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have registered or made purchases from the business. It serves as a central repository for customer data, enabling efficient management of customer relationships and communication."}, {"table_name": "ecommerce.reviews", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to gather insights on product performance and customer satisfaction. Each entry in this table represents an individual review submitted by a customer, including their rating and comments about the product."}]}], "tables": [{"table_id": "c6fba6f3-c88b-4b87-9f75-985e142e0875", "table_name": "analytics.marketing_campaigns", "schema_id": "analytics", "description": "This table contains the following fields: campaign_id, campaign_name, start_date, end_date, budget, status. The 'marketing_campaigns' table is designed to store information about various marketing campaigns, including their identifiers, names, timelines, financial allocations, and current statuses, enabling effective tracking and management of marketing efforts.", "field_count": 6, "fields": [{"name": "campaign_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each marketing campaign."}, {"name": "campaign_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the marketing campaign."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the marketing campaign begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the marketing campaign ends."}, {"name": "budget", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total budget allocated for the marketing campaign."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the marketing campaign (e.g., active, completed, paused)."}]}, {"table_id": "3b274481-975e-4a86-a959-2fcccb2aec99", "table_name": "analytics.customer_segments", "schema_id": "analytics", "description": "This table contains the following fields: segment_id, segment_name, criteria, created_at. The 'customer_segments' table is designed to categorize customers into distinct segments based on specific criteria, allowing businesses to tailor their marketing strategies and improve customer engagement.", "field_count": 4, "fields": [{"name": "segment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer segment."}, {"name": "segment_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name assigned to the customer segment for easy reference."}, {"name": "criteria", "data_type": "jsonb", "is_categorical": false, "is_datetime": false, "description": "The specific conditions or characteristics that define the customer segment."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the customer segment was created."}]}, {"table_id": "e95d4d7e-0fc1-4d5f-bc5a-e21b25b1318a", "table_name": "analytics.customer_lifetime_value", "schema_id": "analytics", "description": "This table contains the following fields: clv_id, customer_id, calculated_date, predicted_value, confidence_interval. The purpose of this table is to store and manage the calculated lifetime value of customers, which helps businesses understand the long-term value each customer brings based on their purchasing behavior and trends.", "field_count": 5, "fields": [{"name": "clv_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer lifetime value record."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer associated with the lifetime value."}, {"name": "calculated_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date on which the customer lifetime value was calculated."}, {"name": "predicted_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The predicted monetary value that a customer is expected to generate over their lifetime."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the level of certainty in the predicted value."}]}, {"table_id": "e2c4394c-7410-45bb-b74a-57b9fec2cd93", "table_name": "analytics.campaign_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, campaign_id, metric_name, metric_value, metric_date. The purpose of the 'campaign_metrics' table is to store performance metrics related to marketing campaigns, allowing for analysis of campaign effectiveness over time.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each metric entry."}, {"name": "campaign_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the campaign to which the metric belongs."}, {"name": "metric_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the metric being recorded (e.g., clicks, impressions, conversions)."}, {"name": "metric_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The numerical value of the metric recorded for the campaign."}, {"name": "metric_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the metric was recorded, allowing for time-based analysis."}]}, {"table_id": "afa4a753-58ee-4917-a016-aff69213c206", "table_name": "analytics.churn_prediction", "schema_id": "analytics", "description": "This table is designed to store information related to customer churn predictions for a business. It helps in analyzing customer behavior and identifying those who are at risk of leaving the service. This table contains the following fields: prediction_id, customer_id, prediction_date, churn_probability, risk_level.", "field_count": 5, "fields": [{"name": "prediction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each churn prediction record."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer associated with the churn prediction."}, {"name": "prediction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the churn prediction was made."}, {"name": "churn_probability", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The probability that the customer will churn, expressed as a percentage."}, {"name": "risk_level", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "A categorical assessment of the customer's risk of churning, such as 'low', 'medium', or 'high'."}]}, {"table_id": "066b55ce-91c0-4aa7-b1f2-0f48ff779d78", "table_name": "analytics.conversion_funnel", "schema_id": "analytics", "description": "This table contains the following fields: funnel_id, user_id, stage, stage_date, conversion_value. The 'conversion_funnel' table is designed to track the progress of users through various stages of a conversion process, allowing for analysis of user behavior and conversion rates at each stage.", "field_count": 5, "fields": [{"name": "funnel_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each conversion funnel instance."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the user associated with the conversion funnel."}, {"name": "stage", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current stage of the user in the conversion funnel, indicating their progress."}, {"name": "stage_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the user reached the current stage in the funnel."}, {"name": "conversion_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The monetary value associated with the conversion at this stage, if applicable."}]}, {"table_id": "2aca52b6-e3e4-4fc6-93ec-b106acde952a", "table_name": "analytics.inventory_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_stock, confidence_interval. The 'inventory_forecast' table is designed to store and manage forecasts related to inventory levels for various products. It helps businesses predict future stock requirements based on historical data and trends, allowing for better inventory management and planning.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each forecast entry."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the forecast."}, {"name": "forecast_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date for which the stock forecast is made."}, {"name": "predicted_stock", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The estimated quantity of stock expected to be available on the forecast date."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the uncertainty of the predicted stock, providing a measure of reliability for the forecast."}]}, {"table_id": "ebf0deca-57a8-4383-8cb5-d0992b836de1", "table_name": "analytics.performance_metrics", "schema_id": "analytics", "description": "This table contains the following fields: metric_id, metric_name, metric_value, metric_date, category. The purpose of the 'performance_metrics' table is to store and track various performance metrics over time, allowing for analysis and reporting on the performance of different entities or processes.", "field_count": 5, "fields": [{"name": "metric_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each performance metric entry."}, {"name": "metric_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the performance metric being recorded."}, {"name": "metric_value", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The value of the performance metric, representing its measurement."}, {"name": "metric_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the performance metric was recorded."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The category under which the performance metric falls, helping to classify the metrics."}]}, {"table_id": "f4e72eda-3f88-465d-880e-1d613ce0f1a0", "table_name": "analytics.segment_members", "schema_id": "analytics", "description": "This table contains the following fields: member_id, segment_id, customer_id, added_at. The 'segment_members' table is used to track the association between customers and segments within a marketing or customer relationship management system. It allows for the organization of customers into specific segments for targeted marketing efforts and analysis.", "field_count": 4, "fields": [{"name": "member_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each member in the segment."}, {"name": "segment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the segment to which the member belongs."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for the customer associated with the segment."}, {"name": "added_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the member was added to the segment."}]}, {"table_id": "d8f7c169-7063-490e-bdd3-b734f00d808f", "table_name": "analytics.sales_forecast", "schema_id": "analytics", "description": "This table contains the following fields: forecast_id, product_id, forecast_date, predicted_sales, confidence_interval. The purpose of the 'sales_forecast' table is to store and manage sales predictions for various products over specified time periods, allowing businesses to plan inventory and marketing strategies effectively.", "field_count": 5, "fields": [{"name": "forecast_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each sales forecast entry."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product associated with the sales forecast."}, {"name": "forecast_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date for which the sales forecast is made."}, {"name": "predicted_sales", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The estimated number of units expected to be sold on the forecast date."}, {"name": "confidence_interval", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "A statistical range that indicates the reliability of the predicted sales figure."}]}, {"table_id": "25230d56-db0a-435c-9b31-5aea99c1667b", "table_name": "analytics.search_queries", "schema_id": "analytics", "description": "This table contains the following fields: query_id, user_id, query_text, search_date, results_count. The 'search_queries' table is designed to store information about user search queries within an application. It tracks each search made by users, including details about the user, the specific search terms they used, when the search occurred, and how many results were returned.", "field_count": 5, "fields": [{"name": "query_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each search query."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who performed the search."}, {"name": "query_text", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual text of the search query entered by the user."}, {"name": "search_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the search query was executed."}, {"name": "results_count", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The number of results returned for the search query."}]}, {"table_id": "541d627d-c23d-4545-b847-773c9caed50d", "table_name": "analytics.product_views", "schema_id": "analytics", "description": "This table contains the following fields: view_id, product_id, user_id, view_date, view_duration. The 'product_views' table is designed to track the views of products by users on an e-commerce platform. It records each instance a user views a product, capturing relevant details such as the user, the product viewed, and the duration of the view.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each view record."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the product that was viewed."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who viewed the product."}, {"name": "view_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the product was viewed."}, {"name": "view_duration", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The duration of time (in seconds) that the user viewed the product."}]}, {"table_id": "95ff6d53-c938-40f1-884a-116d9c9d34f7", "table_name": "analytics.page_views", "schema_id": "analytics", "description": "This table records the details of each page view on the website. It tracks user interactions with different pages, allowing for analysis of traffic patterns and user engagement. This table contains the following fields: view_id, page_url, user_id, view_date, session_id. The data collected can be used for reporting and improving user experience.", "field_count": 5, "fields": [{"name": "view_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each page view record."}, {"name": "page_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL of the page that was viewed."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the user who viewed the page."}, {"name": "view_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the page was viewed."}, {"name": "session_id", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The identifier for the session during which the page view occurred."}]}, {"table_id": "33dfef17-3510-4fac-83cc-5841aa8364eb", "table_name": "analytics.user_activity", "schema_id": "analytics", "description": "This table contains the following fields: activity_id, user_id, activity_type, activity_date, details. The 'user_activity' table is designed to track and log various activities performed by users within the application. It serves as a historical record of user interactions, enabling analysis of user behavior and engagement over time.", "field_count": 5, "fields": [{"name": "activity_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each activity record."}, {"name": "user_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the user who performed the activity."}, {"name": "activity_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "description": "The type of activity performed by the user, such as login, logout, or content creation.", "categorical_values": ["login", "logout", "search", "view_product"]}, {"name": "activity_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the activity occurred."}, {"name": "details", "data_type": "jsonb", "is_categorical": false, "is_datetime": false, "description": "Additional information or context about the activity."}]}, {"table_id": "cf673027-efb2-47fb-8b55-4001a948dfef", "table_name": "finance.currency_exchange", "schema_id": "finance", "description": "This table contains the following fields: exchange_id, from_currency, to_currency, rate, exchange_date. The purpose of the 'currency_exchange' table is to store information about currency exchange transactions, including the currencies involved, the exchange rate applied, and the date of the transaction.", "field_count": 5, "fields": [{"name": "exchange_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each currency exchange transaction."}, {"name": "from_currency", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The currency code of the currency being exchanged from."}, {"name": "to_currency", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The currency code of the currency being exchanged to."}, {"name": "rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The exchange rate applied for the transaction."}, {"name": "exchange_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date on which the currency exchange took place."}]}, {"table_id": "6b746f71-10ea-462c-83ed-64758075e12e", "table_name": "finance.credit_card_transactions", "schema_id": "finance", "description": "This table contains the following fields: transaction_id, credit_card_id, amount, merchant, transaction_date. The purpose of this table is to store detailed records of all transactions made using credit cards, allowing for tracking and analysis of spending behavior, merchant activity, and transaction history.", "field_count": 5, "fields": [{"name": "transaction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction."}, {"name": "credit_card_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the credit card used in the transaction."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount of money spent in the transaction."}, {"name": "merchant", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the merchant where the transaction took place."}, {"name": "transaction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the transaction occurred."}]}, {"table_id": "af748eed-1852-41ac-8b3a-c22c33126401", "table_name": "finance.investments", "schema_id": "finance", "description": "This table contains the following fields: investment_id, investment_type, amount, start_date, end_date, return_rate. The 'investments' table is designed to track various investment records, detailing the specifics of each investment made, including the type, financial details, and duration of the investment.", "field_count": 6, "fields": [{"name": "investment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each investment record."}, {"name": "investment_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The category or type of investment (e.g., stocks, bonds, real estate)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total monetary amount invested."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the investment was initiated."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the investment matures or is expected to be liquidated."}, {"name": "return_rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The expected or actual rate of return on the investment expressed as a percentage."}]}, {"table_id": "a8668dad-d9de-48a6-973c-34b130be2167", "table_name": "finance.financial_reports", "schema_id": "finance", "description": "This table contains the following fields: report_id, report_type, period, generated_at, content. The 'financial_reports' table is designed to store detailed records of financial reports generated by the organization. Each entry in this table represents a unique financial report, capturing essential information about the type of report, the reporting period, the date it was generated, and the actual content of the report.", "field_count": 5, "fields": [{"name": "report_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each financial report."}, {"name": "report_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The type of financial report, such as income statement, balance sheet, or cash flow statement."}, {"name": "period", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The reporting period for which the financial report is generated, typically expressed as a date range."}, {"name": "generated_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the financial report was generated."}, {"name": "content", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The actual content or data of the financial report, often in a structured format."}]}, {"table_id": "48980172-4648-4035-84dc-da3a618dc45e", "table_name": "finance.investment_returns", "schema_id": "finance", "description": "This table contains the following fields: return_id, investment_id, amount, return_date. The purpose of the 'investment_returns' table is to track the returns generated from various investments over time, allowing for analysis of investment performance and profitability.", "field_count": 4, "fields": [{"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each return record."}, {"name": "investment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A foreign key that links to the specific investment associated with the return."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The monetary value of the return generated from the investment."}, {"name": "return_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date on which the return was realized or recorded."}]}, {"table_id": "b7c926be-0025-41d0-be24-08b5c063bd6c", "table_name": "finance.credit_cards", "schema_id": "finance", "description": "This table contains the following fields: credit_card_id, card_number, card_holder, expiry_date, credit_limit, current_balance. The 'credit_cards' table is designed to store information about credit card accounts, including unique identifiers, card details, and financial limits associated with each card.", "field_count": 6, "fields": [{"name": "credit_card_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each credit card record."}, {"name": "card_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The number assigned to the credit card, used for transactions."}, {"name": "card_holder", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the individual or entity that owns the credit card."}, {"name": "expiry_date", "data_type": "date", "is_categorical": false, "is_datetime": true, "description": "The date when the credit card expires and is no longer valid."}, {"name": "credit_limit", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The maximum amount of credit that can be used on the card."}, {"name": "current_balance", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The current outstanding balance on the credit card."}]}, {"table_id": "0b46f17c-8bbe-4d49-8931-98b6ddac8e29", "table_name": "finance.budgets", "schema_id": "finance", "description": "This table contains the following fields: budget_id, category, amount, start_date, end_date. The 'budgets' table is designed to store financial planning information for various categories, allowing users to track their budget allocations over specified time periods.", "field_count": 5, "fields": [{"name": "budget_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each budget entry."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The category of the budget, such as 'Food', 'Utilities', or 'Entertainment'."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount allocated for the budget category."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the budget period begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the budget period ends."}]}, {"table_id": "875e9344-52a1-484a-b74d-6d0e1e50b21e", "table_name": "finance.expenses", "schema_id": "finance", "description": "This table contains the following fields: expense_id, category, amount, description, expense_date, payment_method. The 'expenses' table is designed to track and manage financial expenditures made by an individual or organization. It records details about each expense, including its category, amount spent, a brief description, the date of the expense, and the method of payment used.", "field_count": 6, "fields": [{"name": "expense_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each expense entry."}, {"name": "category", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The category to which the expense belongs, such as travel, food, or utilities."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount of money spent on the expense."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the expense, providing additional context."}, {"name": "expense_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the expense was incurred."}, {"name": "payment_method", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The method used to pay for the expense, such as credit card, cash, or bank transfer."}]}, {"table_id": "********-2914-41af-9f24-f205cee45ea0", "table_name": "finance.loan_payments", "schema_id": "finance", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is designed to track the payments made towards loans, capturing essential details about each payment transaction. It helps in managing loan repayment schedules and monitoring the status of payments.", "field_count": 5, "fields": [{"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each payment record."}, {"name": "loan_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the loan associated with the payment."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The amount of money paid in the payment transaction."}, {"name": "payment_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date on which the payment was made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the payment, indicating whether it is completed, pending, or failed."}]}, {"table_id": "d2d48688-3a62-4e7e-96e1-da941fbfc877", "table_name": "finance.payments", "schema_id": "finance", "description": "This table contains the following fields: payment_id, invoice_id, amount, payment_method, payment_date, status. The 'payments' table is designed to store information about financial transactions made by customers, tracking the details of each payment related to invoices, including the amount paid, the method of payment, and the status of the transaction.", "field_count": 6, "fields": [{"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each payment transaction."}, {"name": "invoice_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the invoice associated with the payment."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount of money paid in the transaction."}, {"name": "payment_method", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The method used for the payment, such as credit card, cash, or bank transfer."}, {"name": "payment_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the payment was made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the payment, indicating whether it is completed, pending, or failed."}]}, {"table_id": "a1f9b1c1-ad3c-4679-9176-70ca1c2e9aa2", "table_name": "finance.tax_records", "schema_id": "finance", "description": "This table contains the following fields: tax_record_id, tax_type, amount, tax_period, due_date, status. The 'tax_records' table is used to store information about various tax obligations for individuals or entities, including details about the type of tax, the amount owed, the period for which the tax is applicable, the due date for payment, and the current status of the tax record.", "field_count": 6, "fields": [{"name": "tax_record_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each tax record."}, {"name": "tax_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The type of tax being recorded (e.g., income tax, property tax, sales tax)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount of tax owed."}, {"name": "tax_period", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The specific period for which the tax is applicable."}, {"name": "due_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date by which the tax payment must be made."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the tax record (e.g., paid, unpaid, overdue)."}]}, {"table_id": "e7a8aecd-82ee-49cf-ba73-659b9e466e93", "table_name": "finance.loans", "schema_id": "finance", "description": "This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status. The 'loans' table is designed to store information about various loans issued to borrowers, including details about the type of loan, the amount borrowed, the interest rate applied, and the duration of the loan. It also tracks the current status of each loan, allowing for effective management and reporting of loan data.", "field_count": 7, "fields": [{"name": "loan_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each loan record."}, {"name": "loan_type", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The type of loan (e.g., personal, mortgage, auto, etc.)."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount of money borrowed in the loan."}, {"name": "interest_rate", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The percentage of interest charged on the loan amount."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the loan period begins."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the loan period ends."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the loan (e.g., active, paid off, defaulted, etc.)."}]}, {"table_id": "a8119040-01ee-4552-91b4-27392b6603d1", "table_name": "finance.accounts", "schema_id": "finance", "description": "This table contains the following fields: account_id, account_number, account_type, balance, created_at. The 'accounts' table is designed to store information about user accounts in a financial system, including unique identifiers, account details, and balance information.", "field_count": 5, "fields": [{"name": "account_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each account, typically an integer or UUID."}, {"name": "account_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The unique number assigned to the account for identification purposes."}, {"name": "account_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "description": "The type of account, such as savings, checking, or credit.", "categorical_values": ["checking", "investment", "savings"]}, {"name": "balance", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The current balance of the account, representing the amount of money available."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the account was created."}]}, {"table_id": "f09df766-ec77-4b62-9764-e1201a266b56", "table_name": "finance.transactions", "schema_id": "finance", "description": "This table contains the following fields: transaction_id, account_id, transaction_type, amount, description, transaction_date. The 'transactions' table is designed to record all financial transactions associated with user accounts, providing a comprehensive log of each transaction's details for tracking and reporting purposes.", "field_count": 6, "fields": [{"name": "transaction_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each transaction."}, {"name": "account_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the account associated with the transaction."}, {"name": "transaction_type", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "description": "The type of transaction, such as 'credit' or 'debit'.", "categorical_values": ["deposit", "transfer", "withdrawal"]}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The monetary amount involved in the transaction."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A brief description of the transaction."}, {"name": "transaction_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the transaction occurred."}]}, {"table_id": "28a39878-dcec-4c46-9db0-5dac43a9744e", "table_name": "finance.invoices", "schema_id": "finance", "description": "This table contains the following fields: invoice_id, customer_id, invoice_number, amount, status, due_date, created_at. The 'invoices' table is designed to store billing information for customers, tracking the details of each invoice issued, including the amount due, payment status, and relevant dates.", "field_count": 7, "fields": [{"name": "invoice_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each invoice."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A reference to the customer associated with the invoice."}, {"name": "invoice_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "A unique number assigned to the invoice for tracking purposes."}, {"name": "amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total amount due on the invoice."}, {"name": "status", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "description": "The current status of the invoice (e.g., paid, pending, overdue).", "categorical_values": ["overdue", "paid", "pending"]}, {"name": "due_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date by which the invoice must be paid."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the invoice was created."}]}, {"table_id": "c65c8896-6255-4b51-bef3-c53190cdcc03", "table_name": "ecommerce.product_promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: product_promotion_id, product_id, promotion_id. The purpose of the 'product_promotions' table is to manage the relationship between products and their associated promotions, allowing for effective tracking and application of promotional offers to specific products in a retail or e-commerce environment.", "field_count": 3, "fields": [{"name": "product_promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product promotion record."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the product that is associated with the promotion."}, {"name": "promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the promotion that is being applied to the product."}]}, {"table_id": "78ec8ca6-769f-47a0-938e-4b794aca7ba2", "table_name": "ecommerce.return_items", "schema_id": "ecommerce", "description": "This table contains the following fields: return_item_id, return_id, order_item_id, quantity, condition. The purpose of the 'return_items' table is to track individual items that are returned by customers, linking them to specific return transactions and the original order items. It helps manage inventory and customer service processes related to product returns.", "field_count": 5, "fields": [{"name": "return_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each return item record."}, {"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A reference to the associated return transaction, linking to the overall return process."}, {"name": "order_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A reference to the original order item that is being returned."}, {"name": "quantity", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The number of units of the item being returned."}, {"name": "condition", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The condition of the returned item, indicating whether it is new, used, damaged, etc."}]}, {"table_id": "51fa69e6-c510-4e47-9d77-dcc24b2dc8f4", "table_name": "ecommerce.product_images", "schema_id": "ecommerce", "description": "This table contains the following fields: image_id, product_id, image_url, is_primary. The purpose of the 'product_images' table is to store and manage images associated with products in an e-commerce database. It allows for the organization of multiple images for each product, including designating a primary image for display purposes.", "field_count": 4, "fields": [{"name": "image_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each image in the table."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that the image is associated with."}, {"name": "image_url", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The URL where the image is stored, allowing it to be accessed and displayed."}, {"name": "is_primary", "data_type": "boolean", "is_categorical": false, "is_datetime": false, "description": "A boolean flag indicating whether this image is the primary image for the associated product."}]}, {"table_id": "314df172-ddc1-4fcf-9aff-3b0f8c5e6654", "table_name": "ecommerce.returns", "schema_id": "ecommerce", "description": "This table contains the following fields: return_id, order_id, customer_id, reason, status, created_at. The 'returns' table is used to track product returns initiated by customers, capturing essential details about each return request, including the associated order and customer information, the reason for the return, the current status of the return process, and the timestamp of when the return was created.", "field_count": 6, "fields": [{"name": "return_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each return record."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the order associated with the return."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the customer who initiated the return."}, {"name": "reason", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The reason provided by the customer for returning the product."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the return process (e.g., pending, completed, rejected)."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the return request was created."}]}, {"table_id": "6156a1fd-6022-407c-8d1b-f76d51592ddf", "table_name": "ecommerce.shipping", "schema_id": "ecommerce", "description": "This table contains information related to the shipment of orders. It tracks the details of each shipment, including the carrier used, tracking information, and the current status of the shipment. This table contains the following fields: shipping_id, order_id, carrier, tracking_number, status, estimated_delivery.", "field_count": 6, "fields": [{"name": "shipping_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shipping record."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order associated with this shipment."}, {"name": "carrier", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the shipping carrier responsible for delivering the package."}, {"name": "tracking_number", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The tracking number provided by the carrier to monitor the shipment's progress."}, {"name": "status", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The current status of the shipment (e.g., pending, shipped, delivered)."}, {"name": "estimated_delivery", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The estimated date of delivery for the shipment."}]}, {"table_id": "b2e8457e-9bfb-4dc2-a8a8-ab34b209325c", "table_name": "ecommerce.cart", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_id, customer_id, created_at. The 'cart' table is designed to store information about shopping carts in an e-commerce application. It tracks the unique identifier for each cart, the customer associated with the cart, and the timestamp of when the cart was created.", "field_count": 3, "fields": [{"name": "cart_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each shopping cart."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who owns the shopping cart."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date and time when the shopping cart was created."}]}, {"table_id": "8ef271c9-52ab-4a77-b10a-4334c3ae994a", "table_name": "ecommerce.wishlist", "schema_id": "ecommerce", "description": "This table contains the following fields: wishlist_id, customer_id, product_id, added_at. The 'wishlist' table is used to store information about products that customers wish to purchase in the future. It allows customers to save items they are interested in, facilitating easier access and potential purchase later on.", "field_count": 4, "fields": [{"name": "wishlist_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each wishlist entry."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the customer who owns the wishlist."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that is added to the wishlist."}, {"name": "added_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the product was added to the wishlist."}]}, {"table_id": "b9f97a48-a8d6-457d-a39f-4bd08c10b8db", "table_name": "ecommerce.order_items", "schema_id": "ecommerce", "description": "This table contains the following fields: order_item_id, order_id, product_id, quantity, unit_price. The 'order_items' table is designed to store details about individual items within customer orders. Each record corresponds to a specific item in an order, capturing essential information such as the product, the quantity ordered, and the price per unit.", "field_count": 5, "fields": [{"name": "order_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order item."}, {"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the order to which this item belongs."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product being ordered."}, {"name": "quantity", "data_type": "integer", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product ordered.", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "unit_price", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The price per unit of the product at the time of the order."}]}, {"table_id": "14197b5d-88ca-4358-a75b-62ce1f9c8b49", "table_name": "ecommerce.cart_items", "schema_id": "ecommerce", "description": "This table contains the following fields: cart_item_id, cart_id, product_id, quantity. The 'cart_items' table is designed to store individual items that are added to a shopping cart in an e-commerce application. Each entry represents a specific product along with its quantity in a user's cart, allowing for the management and retrieval of cart contents during the checkout process.", "field_count": 4, "fields": [{"name": "cart_item_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each item in the cart."}, {"name": "cart_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the shopping cart to which the item belongs."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier for the product that is being added to the cart."}, {"name": "quantity", "data_type": "integer", "is_categorical": true, "is_datetime": false, "description": "The number of units of the product that the user wishes to purchase.", "categorical_values": ["1", "2", "3", "4", "5"]}]}, {"table_id": "ca14b6e2-6d3f-47c4-bb5f-fe930509fee6", "table_name": "ecommerce.categories", "schema_id": "ecommerce", "description": "This table contains the following fields: category_id, name, description, parent_category_id. The 'categories' table is used to organize and manage product categories within a database, allowing for hierarchical relationships between categories and providing detailed information about each category.", "field_count": 4, "fields": [{"name": "category_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each category, typically an integer that serves as the primary key."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the category, which is used to identify it in user interfaces and reports."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A textual description of the category, providing additional context or details about its purpose."}, {"name": "parent_category_id", "data_type": "integer", "is_categorical": true, "is_datetime": false, "description": "An optional field that references the category_id of a parent category, allowing for the creation of a hierarchical structure among categories.", "categorical_values": ["101", "102", "103", "104", "105", "106", "107", "108", "111", "112", "113", "114", "115", "116", "117", "119", "120", "121", "123", "125", "131", "133", "135", "136", "142", "147"]}]}, {"table_id": "224db599-5867-49fa-aa3e-ef79116f17d9", "table_name": "ecommerce.promotions", "schema_id": "ecommerce", "description": "This table contains the following fields: promotion_id, name, description, discount_percentage, start_date, end_date. The 'promotions' table is used to store information about various promotional offers available to customers, including details about the promotion's identity, its specifics, and the time frame during which it is valid.", "field_count": 6, "fields": [{"name": "promotion_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each promotion."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the promotion."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the promotion."}, {"name": "discount_percentage", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The percentage discount offered by the promotion."}, {"name": "start_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the promotion becomes active."}, {"name": "end_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the promotion expires."}]}, {"table_id": "6d4719e7-6a0d-4bc0-a46a-fb22c3dff6e2", "table_name": "ecommerce.orders", "schema_id": "ecommerce", "description": "This table contains the following fields: order_id, customer_id, order_date, total_amount, status, shipping_address, payment_id. The 'orders' table is designed to store information about customer orders placed in the system, tracking essential details such as the customer who made the order, the date of the order, the total amount charged, the current status of the order, the shipping address for delivery, and the payment details associated with the order.", "field_count": 7, "fields": [{"name": "order_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each order."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the customer who placed the order."}, {"name": "order_date", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The date when the order was placed."}, {"name": "total_amount", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The total monetary amount for the order."}, {"name": "status", "data_type": "character varying", "is_categorical": true, "is_datetime": false, "description": "The current status of the order (e.g., pending, shipped, delivered).", "categorical_values": ["cancelled", "delivered", "pending", "processing", "shipped"]}, {"name": "shipping_address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The address where the order will be shipped."}, {"name": "payment_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The unique identifier for the payment transaction associated with the order."}]}, {"table_id": "6de19cdb-2437-404e-99a6-aea69cc217fa", "table_name": "ecommerce.products", "schema_id": "ecommerce", "description": "This table contains the following fields: product_id, name, description, price, stock_quantity, category_id, created_at. The 'products' table is designed to store information about the various products available for sale in an inventory system. It includes details such as product identification, pricing, stock levels, and categorization to facilitate product management and sales tracking.", "field_count": 7, "fields": [{"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each product in the table."}, {"name": "name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The name of the product."}, {"name": "description", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "A detailed description of the product, including features and specifications."}, {"name": "price", "data_type": "numeric", "is_categorical": false, "is_datetime": false, "description": "The selling price of the product."}, {"name": "stock_quantity", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The current quantity of the product available in stock."}, {"name": "category_id", "data_type": "integer", "is_categorical": true, "is_datetime": false, "description": "A reference identifier linking the product to its category.", "categorical_values": ["101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150"]}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the product was added to the inventory."}]}, {"table_id": "fd990851-85c7-4bde-8b6b-8b992c04667f", "table_name": "ecommerce.customers", "schema_id": "ecommerce", "description": "This table contains the following fields: customer_id, first_name, last_name, email, phone, address, created_at. The 'customers' table is designed to store information about individuals who have registered or made purchases from the business. It serves as a central repository for customer data, enabling efficient management of customer relationships and communication.", "field_count": 7, "fields": [{"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each customer, typically an integer or UUID."}, {"name": "first_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The first name of the customer."}, {"name": "last_name", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The last name of the customer."}, {"name": "email", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The email address of the customer, used for communication and account verification."}, {"name": "phone", "data_type": "character varying", "is_categorical": false, "is_datetime": false, "description": "The contact phone number of the customer."}, {"name": "address", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The physical address of the customer, which may include street, city, state, and zip code."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the customer record was created."}]}, {"table_id": "9638bdfa-14b1-4183-a76c-de41f0a7d19d", "table_name": "ecommerce.reviews", "schema_id": "ecommerce", "description": "This table contains the following fields: review_id, product_id, customer_id, rating, comment, created_at. The 'reviews' table is designed to store customer feedback on products, allowing businesses to gather insights on product performance and customer satisfaction. Each entry in this table represents an individual review submitted by a customer, including their rating and comments about the product.", "field_count": 6, "fields": [{"name": "review_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "A unique identifier for each review."}, {"name": "product_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the product being reviewed."}, {"name": "customer_id", "data_type": "integer", "is_categorical": false, "is_datetime": false, "description": "The identifier of the customer who submitted the review."}, {"name": "rating", "data_type": "integer", "is_categorical": true, "is_datetime": false, "description": "The rating given by the customer, typically on a scale from 1 to 5.", "categorical_values": ["1", "2", "3", "4", "5"]}, {"name": "comment", "data_type": "text", "is_categorical": false, "is_datetime": false, "description": "The textual feedback provided by the customer regarding the product."}, {"name": "created_at", "data_type": "timestamp without time zone", "is_categorical": false, "is_datetime": false, "description": "The timestamp indicating when the review was created."}]}]}