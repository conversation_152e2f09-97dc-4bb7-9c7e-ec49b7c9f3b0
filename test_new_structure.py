#!/usr/bin/env python3
"""
Test script to verify the new tool structure works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_copilot.services.ai.agent_tools import schema_generator, query_generator, executor, plot_generator

def test_tools():
    """Test that all tools can be imported and have the correct signatures."""
    
    print("Testing tool imports...")
    
    # Test schema_generator
    print("✓ schema_generator imported successfully")
    print(f"  - Function signature: {schema_generator.__name__}")
    
    # Test query_generator
    print("✓ query_generator imported successfully")
    print(f"  - Function signature: {query_generator.__name__}")
    
    # Test executor
    print("✓ executor imported successfully")
    print(f"  - Function signature: {executor.__name__}")
    
    # Test plot_generator
    print("✓ plot_generator imported successfully")
    print(f"  - Function signature: {plot_generator.__name__}")
    
    print("\nAll tools imported successfully!")
    print("\nTool structure:")
    print("- schema_generator: Fetches database schema for filtered tables")
    print("- query_generator: Generates SQL queries (no file mode)")
    print("- executor: Executes queries (supports direct/file mode)")
    print("- plot_generator: Creates visualizations from results")

if __name__ == "__main__":
    test_tools()
 