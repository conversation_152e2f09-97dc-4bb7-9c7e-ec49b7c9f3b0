import requests
from dotenv import load_dotenv
import os
import re
from slack_sdk import Web<PERSON>lient
import os
import json
import asyncio
import logging
from typing import List, Dict
import snowflake.connector
import os
from dotenv import load_dotenv
import datetime

import json

import requests
from dotenv import load_dotenv
import os
import re
from slack_sdk import <PERSON><PERSON>lient
from config import Config
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Load Slack tokens

SLACK_BOT_TOKEN = Config.SLACK_BOT_TOKEN
SLACK_APP_TOKEN = Config.SLACK_APP_TOKEN




client = WebClient(token=SLACK_BOT_TOKEN)


# Global bot user ID (will be overridden by team-specific tokens)
BOT_USER_ID = None


def get_user_name(user_id, access_token=None):
    """
    Fetch user name from Slack API
    Returns display name if available, otherwise real name, otherwise user ID
    """
    try:
        # Use provided access token or fall back to global config
        token_to_use = access_token or SLACK_BOT_TOKEN
        
        # Create a temporary client with the provided token
        temp_client = WebClient(token=token_to_use)
        
        # Use the WebClient to get user info
        user_info = temp_client.users_info(user=user_id)
        if user_info["ok"]:
            user = user_info["user"]
            # Try to get display name first, then real name, then fallback to user ID
            display_name = user.get("profile", {}).get("display_name", "")
            real_name = user.get("real_name", "")
            
            if display_name:
                return display_name
            elif real_name:
                return real_name
            else:
                return user_id
        else:
            logger.warning(f"Failed to get user info for {user_id}: {user_info.get('error')}")
            return "NOT PROVIDED"
    except Exception as e:
        logger.error(f"Error fetching user name for {user_id}: {e}")
        return "NOT PROVIDED"


def fetch_user_questions(channel, ts, access_token=None, k=10):
    """
    Fetches the conversation history from a Slack thread, including both user messages and bot responses.
    If a bot message contains an SQL query in its blocks, the query is appended to the bot's response.
    Now includes actual user names for better context.
    """
    # Use provided access token or fall back to global config
    token_to_use = access_token or SLACK_BOT_TOKEN
    
    # Create a temporary client with the provided token to get bot info
    temp_client = WebClient(token=token_to_use)
    
    try:
        bot_info = temp_client.auth_test()
        bot_user_id = bot_info["user_id"]
        logger.info(f"Bot user ID from token: {bot_user_id}")
    except Exception as e:
        logger.error(f"Error getting bot info with token: {e}")
        raise ValueError("Invalid access token or bot user ID not found. Cannot fetch messages.")

    # Fetch thread messages
    url = "https://slack.com/api/conversations.replies"
    headers = {"Authorization": f"Bearer {token_to_use}", "Content-Type": "application/x-www-form-urlencoded"}
    params = {"channel": channel, "ts": ts}
    response = requests.get(url, headers=headers, params=params)

    if response.status_code != 200:
        raise Exception(f"Slack API request failed: {response.status_code}")

    data = response.json()

    print(f"CONVERSATION DATA: {data}")
    if not data.get("ok"):
        raise Exception(f"Error fetching messages: {data.get('error')}")


    message_history = []
    previous_message = None

    # SQL query pattern
    sql_pattern = re.compile(r"\b(SELECT|INSERT|UPDATE|DELETE|FROM|WHERE|JOIN)\b", re.IGNORECASE)

    for msg in data.get("messages", []):
        user_id = msg.get("user", "Unknown")
        role = "assistant" if user_id == bot_user_id else "user"

        print(f"MESSAGE: {msg}")

        text = msg.get("text", "").strip()
        sql_queries = []

        # Only search for SQL queries inside blocks if the message is from the bot
        if role == "assistant" and "blocks" in msg:
            for block in msg["blocks"]:
                if block.get("type") == "section" and "text" in block:
                    block_text = block["text"]["text"]
                    if sql_pattern.search(block_text):
                        sql_queries.append(block_text)

        if role == "user" and f"<@{bot_user_id}>" in text:
            clean_text = re.sub(f"<@{bot_user_id}>", "", text).strip()
            user_name = get_user_name(user_id, token_to_use)

            clean_text += f"\n\n(From (NAME OF USER): {user_name})"
            print(f"USER NAME: {user_name}")
            message_history.append({
                "role": role, 
                
                "message": clean_text
            })
        elif role == "assistant":
            new_message = {
                "role": role,   # You could also get the bot's name if needed
                "message": text
            }

            # Append any found SQL queries
            for sql_query in sql_queries:
                new_message["message"] += f"\n\n```sql\n{sql_query}\n```"

            message_history.append(new_message)
            previous_message = new_message  # Track last bot message

    print(f"convo history:{message_history}")
    return {"platform": "SLACK", "message_history": message_history[-k:]}