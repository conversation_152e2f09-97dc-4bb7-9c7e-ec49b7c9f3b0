import os
import json
import asyncio
import logging
from typing import List, Dict
import snowflake.connector
import os
from dotenv import load_dotenv
import datetime


import os
import json
import asyncio
import logging
from typing import List, Dict
import snowflake.connector
import os
from dotenv import load_dotenv
import datetime



logger = logging.getLogger(__name__)



async def save_feedback_and_execute_sql(
    question: str,
    sql_query: str,
    feedback: bool,
    comment: str,
    schema=os.getenv('SNOWFLAKE_SCHEMA')
):
    """
    Save feedback, question, and SQL query to the database.
    """
    
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
        session_parameters={'QUERY_TAG': 'py-copy-into'},
    )
    
    # Save feedback to the BOT_FEEDBACK table
    feedback_query = f"""
    INSERT INTO AFRIEX.DIRTY.BOT_FEEDBACK (QUESTION, SQL_QUERY, FEEDBACK, COMMENT)
    VALUES (%s, %s, %s, %s);
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(feedback_query, (question, sql_query, feedback, comment))
        connection.commit()
        
        return cursor.fetchall(), None
        
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error saving feedback: {error_message}")
        return None, error_message
        
    finally:
        cursor.close()
        connection.close()


async def get_feedback_stats():
    """
    Query all feedback from the database and calculate weekly statistics.
    Returns a dictionary with total correct, total wrong, overall totals, and all feedback data.
    """
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    )
    
    try:
        cursor = connection.cursor()
        
        # Query to get all feedback
        cursor.execute("SELECT QUESTION, FEEDBACK, CREATED_AT, SQL_QUERY, COMMENT FROM AFRIEX.DIRTY.BOT_FEEDBACK")
        feedback_records = cursor.fetchall()
        
        # Initialize counters
        total_correct = 0
        total_wrong = 0
        weekly_correct = 0
        weekly_wrong = 0
        
        # Get the current week number
        current_week = datetime.datetime.now().isocalendar()[1]
        
        all_feedback = []
        
        for question, feedback, created_at, sql_query, comment in feedback_records:
            # Check if feedback is a boolean
            if feedback is None:
                logger.warning(f"Feedback for question '{question}' is None, skipping.")
                continue
            
            # Create a dictionary for each feedback record
            feedback_entry = {
                "question": question,
                "feedback": feedback,
                "time_stamp": created_at,
                "sql_query": sql_query,
                "comment":comment
            }
            all_feedback.append(feedback_entry)

            # Assuming feedback is a boolean where True = correct and False = wrong
            if feedback:
                total_correct += 1
                if created_at.isocalendar()[1] == current_week:
                    weekly_correct += 1
            else:
                total_wrong += 1
                if created_at.isocalendar()[1] == current_week:
                    weekly_wrong += 1
        
        return {
            "total_correct": total_correct,
            "total_wrong": total_wrong,
            "weekly_correct": weekly_correct,
            "weekly_wrong": weekly_wrong,
            "all_feedback": all_feedback  # Include all feedback data as list of dictionaries
        }
        
    except Exception as error:
        logger.error(f"Error fetching feedback stats: {error}")
        return None
        
    finally:
        cursor.close()
        connection.close()