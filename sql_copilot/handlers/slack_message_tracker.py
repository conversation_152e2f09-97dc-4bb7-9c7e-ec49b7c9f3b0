import os
import json
import asyncio
import logging
from typing import List, Dict, Optional
import snowflake.connector
from dotenv import load_dotenv
import datetime



logger = logging.getLogger(__name__)


async def create_slack_message_entry(
    thread_id: str,
    channel_id: str,
    message_id: str,
    status: str = "pending"
):
    """
    Create a new entry in the slackMessageTracker table.
    """
    
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
        session_parameters={'QUERY_TAG': 'slack-tracker-create'},
    )
    
    # Insert query
    insert_query = """
    INSERT INTO AFRIEX.DIRTY.slackMessageTracker (thread_id, channel_id, message_id, status)
    VALUES (%s, %s, %s, %s);
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(insert_query, (thread_id, channel_id, message_id, status))
        connection.commit()
        
        logger.info(f"Created slack message entry for message_id: {message_id}")
        return True, None
        
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error creating slack message entry: {error_message}")
        return False, error_message
        
    finally:
        cursor.close()
        connection.close()


async def update_slack_message_status(message_id: str, new_status: str):
    """
    Update the status of a slack message by message_id.
    """
    
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
        session_parameters={'QUERY_TAG': 'slack-tracker-update'},
    )
    
    # Update query
    update_query = """
    UPDATE AFRIEX.DIRTY.slackMessageTracker 
    SET status = %s, updated_at = CURRENT_TIMESTAMP 
    WHERE message_id = %s;
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(update_query, (new_status, message_id))
        connection.commit()
        
        rows_affected = cursor.rowcount
        if rows_affected > 0:
            logger.info(f"Updated status to '{new_status}' for message_id: {message_id}")
            return True, None
        else:
            logger.warning(f"No record found with message_id: {message_id}")
            return False, "No record found"
        
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error updating slack message status: {error_message}")
        return False, error_message
        
    finally:
        cursor.close()
        connection.close()


async def get_slack_message_by_id(message_id: str):
    """
    Get a slack message record by message_id.
    Returns the record as a dictionary or None if not found.
    """
    
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
        session_parameters={'QUERY_TAG': 'slack-tracker-get'},
    )
    
    # Select query
    select_query = """
    SELECT thread_id, channel_id, message_id, status, created_at, updated_at
    FROM AFRIEX.DIRTY.slackMessageTracker 
    WHERE message_id = %s;
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(select_query, (message_id,))
        result = cursor.fetchone()
        
        if result:
            thread_id, channel_id, msg_id, status, created_at, updated_at = result
            return {
                "thread_id": thread_id,
                "channel_id": channel_id,
                "message_id": msg_id,
                "status": status,
                "created_at": created_at,
                "updated_at": updated_at
            }, None
        else:
            logger.info(f"No record found with message_id: {message_id}")
            return None, "No record found"
        
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error fetching slack message: {error_message}")
        return None, error_message
        
    finally:
        cursor.close()
        connection.close()


async def delete_slack_message_by_id(message_id: str):
    """
    Delete a slack message record by message_id.
    """
    
    # Connect to Snowflake
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
        session_parameters={'QUERY_TAG': 'slack-tracker-delete'},
    )
    
    # Delete query
    delete_query = """
    DELETE FROM AFRIEX.DIRTY.slackMessageTracker 
    WHERE message_id = %s;
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(delete_query, (message_id,))
        connection.commit()
        
        rows_affected = cursor.rowcount
        if rows_affected > 0:
            logger.info(f"Deleted slack message entry for message_id: {message_id}")
            return True, None
        else:
            logger.warning(f"No record found to delete with message_id: {message_id}")
            return False, "No record found"
        
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error deleting slack message: {error_message}")
        return False, error_message
        
    finally:
        cursor.close()
        connection.close()


# Example usage functions
async def example_usage():
    """
    Example of how to use the slack message tracker functions.
    """
    
    # Create a new entry
    success, error = await create_slack_message_entry(
        thread_id="1234567890.123456",
        channel_id="C1234567890",
        message_id="1234567890.123457",
        status="pending"
    )
    
    if success:
        print("Entry created successfully")
    else:
        print(f"Error creating entry: {error}")
    
    # Get the entry
    record, error = await get_slack_message_by_id("1234567890.123457")
    if record:
        print(f"Found record: {record}")
    else:
        print(f"Error fetching record: {error}")
    
    # Update the status
    success, error = await update_slack_message_status("1234567890.123457", "responded")
    if success:
        print("Status updated successfully")
    else:
        print(f"Error updating status: {error}")
    
    # Delete the entry
    '''success, error = await delete_slack_message_by_id("1234567890.123457")
    if success:
        print("Entry deleted successfully")
    else:
        print(f"Error deleting entry: {error}")'''


if __name__ == "__main__":
    # Run the example
    asyncio.run(example_usage())