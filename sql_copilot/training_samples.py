import os
import json
import asyncio
import logging
from typing import List, Dict, Optional, Tuple
import snowflake.connector
from dotenv import load_dotenv
import datetime
#oad_dotenv()

logger = logging.getLogger(__name__)

async def create_training_example(
    question: str,
    sql_query: str,
    schema=os.getenv('SNOWFLAKE_SCHEMA')
) -> Optional[Tuple[List, str]]:
    """
    Create a new training example in the database.
    """
    return await execute_training_example_query(
        "INSERT INTO AFRIEX.DIRTY.BOT_EXAMPLES (QUESTION, SQL_QUERY) VALUES (%s, %s);",
        (question, sql_query)
    )

async def update_training_example(
    example_id: int,
    question: str,
    sql_query: str,
    schema=os.getenv('SNOWFLAKE_SCHEMA')
) -> Optional[Tuple[List, str]]:
    """
    Update an existing training example in the database.
    """
    return await execute_training_example_query(
        "UPDATE AFRIEX.DIRTY.BOT_EXAMPLES SET QUESTION = %s, SQL_QUERY = %s WHERE ID = %s;",
        (question, sql_query, example_id)
    )

async def delete_training_example(
    example_id: int,
    schema=os.getenv('SNOWFLAKE_SCHEMA')
) -> Optional[Tuple[List, str]]:
    """
    Delete a training example from the database.
    """
    return await execute_training_example_query(
        "DELETE FROM AFRIEX.DIRTY.BOT_EXAMPLES WHERE ID = %s;",
        (example_id,)
    )

async def get_all_training_examples(
    schema=os.getenv('SNOWFLAKE_SCHEMA')
) -> Optional[Tuple[List, str]]:
    """
    Retrieve all training examples from the database.
    """
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    )
    
    try:
        cursor = connection.cursor()
        cursor.execute("SELECT * FROM AFRIEX.DIRTY.BOT_EXAMPLES;")
        return cursor.fetchall(), None
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error retrieving training examples: {error_message}")
        return None, error_message
    finally:
        cursor.close()
        connection.close()

async def execute_training_example_query(query: str, params: Tuple) -> Optional[Tuple[List, str]]:
    """
    Execute a training example query with the provided parameters.
    """
    connection = snowflake.connector.connect(
        user=os.getenv('SNOWFLAKE_USER'),
        password=os.getenv('SNOWFLAKE_PASSWORD'),
        account=os.getenv('SNOWFLAKE_ACCOUNT'),
        role=os.getenv('ROLE'),
        database=os.getenv('DATABASE'),
        schema="DIRTY",
        warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    )
    
    try:
        cursor = connection.cursor()
        cursor.execute(query, params)
        connection.commit()
        return cursor.fetchall(), None
    except Exception as error:
        error_message = str(error)
        logger.error(f"Error executing training example query: {error_message}")
        return None, error_message
    finally:
        cursor.close()
        connection.close()
# Example of how to call this function with asyncio
async def get_all_samples():
    examples, error = await get_all_training_examples()
    if error:
        None
    else:
        # Convert examples to a list of dictionaries for better readability
        return [{"question": ex[1], "sql_query": ex[2]} for ex in examples]

if __name__ == "__main__":
    examples_dict = asyncio.run(get_all_samples())
    print(examples_dict)  # Now you can use examples_dict elsewhere