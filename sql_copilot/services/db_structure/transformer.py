import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from sql_copilot.services.db_structure.providers import DatabaseProvider

class BaseSchemaTransformer(ABC):
    """Abstract base class for schema transformers"""
    
    def __init__(self, provider: DatabaseProvider):
        self.provider = provider
        self.provider_name = provider.value
    
    @abstractmethod
    def transform_to_json_db(self, raw_schema_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Transform provider-specific schema data to standardized JSON format"""
        pass
    
    @abstractmethod
    def validate_schema_data(self, raw_schema_data: List[Dict[str, Any]]) -> bool:
        """Validate that the input data matches expected provider format"""
        pass
    
    def save_to_file(self, db_structure: Dict[str, Any], filename: str = None) -> str:
        """Save the database structure to a JSON file"""
        if filename is None:
            filename = f"{self.provider_name}_schema.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(db_structure, f, indent=2, ensure_ascii=False)
        
        print(f"Schema saved to {filename}")
        return filename
    
    def load_from_file(self, filename: str) -> Dict[str, Any]:
        """Load schema structure from JSON file"""
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_tables_by_schema(self, db_structure: Dict[str, Any], schema_id: str) -> List[Dict[str, Any]]:
        """Get all tables belonging to a specific schema"""
        return [table for table in db_structure.get("tables", []) if table["schema_id"] == schema_id]
    
    def get_categorical_fields_summary(self, db_structure: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Get summary of all categorical fields by schema"""
        summary = {}
        for table in db_structure.get("tables", []):
            schema_id = table["schema_id"]
            if schema_id not in summary:
                summary[schema_id] = []
            
            for field in table.get("fields", []):
                if field.get("is_categorical") and field.get("categorical_values"):
                    summary[schema_id].append({
                        "table_name": table["table_name"],
                        "field_name": field["name"],
                        "values": field["categorical_values"]
                    })
        return summary

class SchemaTransformerFactory:
    """Factory class to create appropriate schema transformers"""
    
    _transformers = {}
    
    @classmethod
    def register_transformer(cls, provider: DatabaseProvider, transformer_class):
        """Register a transformer class for a provider"""
        cls._transformers[provider] = transformer_class
    
    @classmethod
    def create_transformer(cls, provider: DatabaseProvider) -> BaseSchemaTransformer:
        """Create a transformer for the specified provider"""
        if provider not in cls._transformers:
            raise ValueError(f"Unsupported database provider: {provider}")
        
        return cls._transformers[provider]()
    
    @classmethod
    def get_supported_providers(cls) -> List[DatabaseProvider]:
        """Get list of supported database providers"""
        return list(cls._transformers.keys())

# Import transformers after class definitions to avoid circular imports
from sql_copilot.services.db_structure.providers_handler.snowflake import SnowflakeSchemaTransformer
from sql_copilot.services.db_structure.providers_handler.mongodb import MongoDBSchemaTransformer
from sql_copilot.services.db_structure.providers_handler.postgres import PostgresSchemaTransformer
from sql_copilot.services.db_structure.providers_handler.mysql import MysqlSchemaTransformer

# Register the transformers
SchemaTransformerFactory.register_transformer(DatabaseProvider.SNOWFLAKE, SnowflakeSchemaTransformer)
SchemaTransformerFactory.register_transformer(DatabaseProvider.MONGODB, MongoDBSchemaTransformer)
SchemaTransformerFactory.register_transformer(DatabaseProvider.POSTGRES, PostgresSchemaTransformer)
SchemaTransformerFactory.register_transformer(DatabaseProvider.MYSQL, MysqlSchemaTransformer)

# Example usage
if __name__ == "__main__":
    # Snowflake example
    snowflake_data = [
        {
            "schema_name": "TRANSFORMED",
            "table_name": "TRANSFORMED.MEDICI_BALANCES", 
            "description": "This table tracks financial balances for users in different currencies.",
            "fields": [
                {
                    "name": "id",
                    "data_type": "TEXT",
                    "is_categorical": False,
                    "is_datetime": False,
                    "description": "Unique identifier for each balance record."
                },
                {
                    "name": "book", 
                    "data_type": "TEXT",
                    "is_categorical": True,
                    "is_datetime": False,
                    "description": "Financial book identifier.",
                    "found_categorical_values": ["AfriexBook", "MainBook", "TestBook"]
                },
                {
                    "name": "currency",
                    "data_type": "TEXT",
                    "is_categorical": True,
                    "is_datetime": False,
                    "description": "Currency code.",
                    "found_categorical_values": ["USD", "EUR", "NGN"]
                }
            ]
        }
    ]
    
    # MongoDB example (your format)
    mongodb_data = {
        "collections": [
            {
                "database_name": "dev",
                "collection_name": "contacts",
                "description": "This collection stores information about individual contacts, including their unique identifiers and associated data.",
                "fields": [
                    {
                        "name": "_id",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "A unique identifier for each contact, automatically generated by MongoDB."
                    },
                    {
                        "name": "hash",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "A hashed value representing the contact's information for quick lookup and verification."
                    },
                    {
                        "name": "contacts",
                        "data_type": "array",
                        "is_categorical": False,
                        "description": "An array of objects containing detailed information about each contact, such as name, phone number, and email address."
                    }
                ],
                "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"hash\": \"f1e2d3c4b5a6978f0e1d2c3b4a5f6e7d8c9b0a1e2f3d4c5b6a7e8f9g0h1i2j3k\",\n  \"contacts\": [\n    \"1234567890abcdef12345678\",\n    \"abcdef1234567890abcdef12\",\n    \"fedcba0987654321fedcba09\"\n  ]\n}",
                "status": "active"
            },
            {
                "database_name": "dev",
                "collection_name": "feature-flags",
                "description": "This collection stores feature flags that control the availability of features in an application.",
                "fields": [
                    {
                        "name": "_id",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "Unique identifier for the feature flag."
                    },
                    {
                        "name": "flag_name",
                        "data_type": "string",
                        "is_categorical": True,
                        "description": "Name of the feature flag.",
                        "categorical_values": ["dark_mode", "new_ui", "beta_features"]
                    }
                ],
                "sample_documents": "{\n  \"_id\": \"flag123\",\n  \"flag_name\": \"dark_mode\",\n  \"enabled\": true\n}",
                "status": "active"
            },
            {
                "database_name": "dev",
                "collection_name": "waitlists",
                "description": "",
                "fields": [],
                "sample_documents": [],
                "status": "deactivated"
            }
        ]
    }
    
    print("=== SUPPORTED PROVIDERS ===")
    for provider in SchemaTransformerFactory.get_supported_providers():
        print(f"- {provider.value}")
    
    print("\n=== SNOWFLAKE TRANSFORMATION ===")
    snowflake_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.SNOWFLAKE)
    snowflake_result = snowflake_transformer.transform_to_json_db(snowflake_data)
    print(json.dumps(snowflake_result, indent=2))
    
    print("\n=== MONGODB TRANSFORMATION ===")
    mongodb_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.MONGODB)
    mongodb_result = mongodb_transformer.transform_to_json_db(mongodb_data)
    print(json.dumps(mongodb_result, indent=2))
    
    print("\n=== MONGODB SCHEMA OVERVIEW (Node 1) ===")
    for schema in mongodb_result["schemas"]:
        print(f"Database: {schema['schema_name']}")
        print(f"Total Collections: {schema['total_collections']}")
        print(f"Active Collections: {schema['active_collections']}")
        print("Collections Overview:")
        for collection in schema['collections_overview']:
            print(f"  - {collection['collection_name']} ({collection['status']})")
            print(f"    Description: {collection['description']}")
            print(f"    Fields: {collection['field_count']}")
            if collection['categorical_examples']:
                print("    Categorical Fields:")
                for cat in collection['categorical_examples']:
                    print(f"      • {cat['field_name']}: {', '.join(cat['sample_values'])}")
        print()
    
    print("=== MONGODB ORIGINAL DATA (Node 2) ===")
    for table in mongodb_result["tables"]:
        if table["status"] == "active":  # Only show active collections
            print(f"Collection: {table['collection_name']}")
            print(f"Database: {table['database_name']}")
            print(f"Description: {table['description']}")
            print(f"Fields: {len(table['fields'])}")
            print(f"Has Sample Documents: {bool(table['sample_documents'])}")
            print("---")