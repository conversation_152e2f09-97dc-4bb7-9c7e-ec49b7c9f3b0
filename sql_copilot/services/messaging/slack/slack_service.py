"""
Slack Service Module
Contains all Slack-related utility functions and services
"""

import os
import re
import tempfile
import logging
import json
from typing import Optional, Dict, Any, List
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
import pandas as pd
from pathlib import Path

from app.models import MessagingConnection
from app.core.crypto_utils import decrypt_data
from sqlalchemy.orm import Session
from app.databse import get_db
from sql_copilot.services.ai.query_handler import Query<PERSON><PERSON><PERSON>, QueryResponse

logger = logging.getLogger(__name__)

# Constants
LOADING_MESSAGES = [
    {
        "type": "section",
        "text": {
            "type": "mrkdwn",
            "text": "🤔 Thinking..."
        }
    }
]


class SlackService:
    """Service class for handling Slack operations"""
    
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.client = WebClient(token=access_token)
        self.bot_user_id = self._get_bot_user_id()
    
    def _get_bot_user_id(self) -> Optional[str]:
        """Get bot user ID from access token"""
        try:
            bot_info = self.client.auth_test()
            bot_user_id = bot_info["user_id"]
            logger.info(f"Bot user ID from token: {bot_user_id}")
            return bot_user_id
        except Exception as e:
            logger.error(f"Error getting bot user ID with token: {e}")
            return None
    
    def send_loading_message(self, channel: str, thread_ts: Optional[str] = None) -> Optional[str]:
        """Send a loading message and return its timestamp"""
        try:
            response = self.client.chat_postMessage(
                channel=channel,
                thread_ts=thread_ts,
                blocks=LOADING_MESSAGES,
                text="Processing your request..."  # Fallback text
            )
            return response["ts"]
        except Exception as e:
            logger.error(f"Error sending loading message: {e}")
            return None
    
    def update_message_with_response(self, channel: str, loading_message_ts: str, blocks: List[Dict], text: str) -> bool:
        """Update the loading message with the actual response"""
        try:
            self.client.chat_update(
                channel=channel,
                ts=loading_message_ts,
                blocks=blocks,
                text=text
            )
            return True
        except Exception as e:
            logger.error(f"Error updating message: {e}")
            return False
    
    def send_message(self, channel: str, thread_ts: Optional[str], blocks: List[Dict], text: str) -> bool:
        """Send a new message"""
        try:
            self.client.chat_postMessage(
                channel=channel,
                thread_ts=thread_ts,
                blocks=blocks,
                text=text
            )
            return True
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return False
    
    def delete_message(self, channel: str, ts: str) -> bool:
        """Delete a message"""
        try:
            self.client.chat_delete(channel=channel, ts=ts)
            return True
        except Exception as e:
            logger.error(f"Error deleting message: {e}")
            return False
    
    def upload_file(self, file_path: str, title: str, channel: str, thread_ts: Optional[str], initial_comment: Optional[str] = None) -> Optional[Dict]:
        """Upload a file to Slack"""
        try:
            response = self.client.files_upload_v2(
                file=file_path,
                title=title,
                channel=channel,
                thread_ts=thread_ts,
                initial_comment=initial_comment
            )
            logger.info("File uploaded successfully:", response['file']['url_private'])
            if os.path.exists(file_path):
                os.remove(file_path)
            return response
        except SlackApiError as e:
            logger.error(f"Error uploading file: {e.response['error']}")
            return None
    
    def extract_question_from_message(self, text: str) -> str:
        """Extract the actual question from a message that might contain a bot mention"""
        if self.bot_user_id:
            text = re.sub(f'<@{self.bot_user_id}>', '', text)
        return text.strip()
    
    def should_process_message(self, event: Dict) -> tuple[bool, str]:
        """Enhanced message filtering with detailed reasons"""
        # Basic bot filtering
        if any([
            "bot_id" in event,
            "bot_profile" in event,
            event.get("user") == self.bot_user_id,
            event.get("subtype") == "bot_message",
            event.get("subtype") == "message_changed",
            event.get("subtype") == "message_deleted"
        ]):
            return False, "bot_message"
        
        # Check for required fields
        if not event.get("user"):
            return False, "no_user"
        
        if not event.get("text", "").strip():
            return False, "empty_text"
        
        # Check if it's a direct message or mentions the bot
        text = event.get("text", "")
        is_dm = event.get("channel_type") == "im"
        is_mention = f"<@{self.bot_user_id}>" in text if self.bot_user_id else False
        
        if not (is_dm or is_mention):
            return False, "not_addressed_to_bot"
        
        return True, "should_process"


def create_composite_message_id(event: Dict) -> str:
    """Create a composite ID that's more reliable than client_msg_id alone"""
    client_msg_id = event.get("client_msg_id", "")
    channel = event.get("channel", "")
    ts = event.get("ts", "")
    user = event.get("user", "")
    text_hash = str(hash(event.get("text", "")))[:8]  # Short hash of text
    
    # Use multiple identifiers to create a unique composite ID
    return f"{client_msg_id}_{channel}_{ts}_{user}_{text_hash}"


async def query_sql_copilot(question: str, team_id: str, conversation_history: Optional[List] = None) -> QueryResponse:
    """Send question to SQL Copilot API and get response"""
    try:
        logger.info(f"Processing query for team_id: {team_id}")
        
        # Get database session
        db: Session = next(get_db())
        
        # Find the MessagingConnection by team_id
        messaging_connection = db.query(MessagingConnection).filter_by(
            team_id=team_id, 
            type="slack", 
            status="connected"
        ).first()
        
        logger.info(f"Found messaging connection: {messaging_connection is not None}")
        
        if not messaging_connection:
            return QueryResponse(
                response="❌ Slack is not connected to any database. Please ask your admin to connect Slack to a database on the dashboard.",
                has_file=False,
                file_name=None,
                query_code="",
                question_interpretation=""
            )
        
        # Get the database connection ID
        db_connection_id = messaging_connection.db_connection_id
        logger.info(f"Database connection ID: {db_connection_id}")
        
        if not db_connection_id:
            logger.warning(f"No database connection configured for team_id: {team_id}")
            return QueryResponse(
                response="❌ No database connection configured for this Slack workspace. Please ask your admin to configure a database connection on the dashboard.",
                has_file=False,
                file_name=None,
                query_code="",
                question_interpretation=""
            )
        
        # Get the access token from the messaging connection config
        config = json.loads(decrypt_data(messaging_connection.config))
        access_token = config.get("access_token")
        
        if not access_token:
            logger.error(f"No access token found for team_id: {team_id}")
            return QueryResponse(
                response="❌ Slack access token not found. Please ask your admin to reconnect Slack on the dashboard.",
                has_file=False,
                file_name=None,
                query_code="",
                question_interpretation=""
            )
        
        # Create QueryHandler with the connection_id
        handler = QueryHandler(
            query=question,
            history=conversation_history,
            connection_id=db_connection_id
        )
        
        # Process the query and return response
        response = await handler.process_query()
        
        return response
        
    except Exception as e:
        logger.error(f"Error querying SQL Copilot: {e}")
        return QueryResponse(
            response="❌ Sorry, I encountered an error processing your request. Please try again later.",
            has_file=False,
            file_name=None,
            query_code="",
            error_detail=str(e)
        )


def create_slack_blocks(api_response: Dict, thread_ts: str, channel: str, slack_service: SlackService) -> List[Dict]:
    """Create Slack blocks from API response"""
    blocks = []
    
    # Add question interpretation if present (as a context block)
    question_interpretation = api_response.get('question_interpretation')
    if question_interpretation and question_interpretation.strip():
        blocks.append({
            "type": "context",
            "elements": [
                {
                    "type": "mrkdwn",
                    "text": f"_I understood your question as:_ {question_interpretation}"
                }
            ]
        })
    
    # 1. Add response text (always included if present)
    response_text = api_response.get('response', '')
    if response_text.strip():  # Only add if there's actual content
        blocks.append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": response_text
            }
        })
        
        # Add a divider if we have more content coming
        if any([
            api_response.get('query_code'),
            api_response.get('code_data'),
            api_response.get('has_file')
        ]):
            blocks.append({"type": "divider"})

    # 2. Add SQL query if present
    sql_query = api_response.get('query_code')
    if sql_query:
        blocks.append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "*SQL Query:*\n```sql\n" + sql_query.strip() + "\n```"
            }
        })
        
        blocks.append({"type": "divider"})

    # 3. Add table data (only in analytics mode)
    if api_response.get('analytics_mode'):
        code_data = api_response.get('code_data', [])
        if code_data:
            try:
                code_df = pd.DataFrame(code_data)
                
                # Save dataframe to a temporary CSV file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".csv") as temp_file:
                    file_path = temp_file.name
                    code_df.to_csv(file_path, index=False)

                # Only attempt upload if file exists
                if os.path.exists(file_path):
                    upload_response = slack_service.upload_file(
                        file_path=file_path,
                        title="Data",
                        channel=channel,
                        thread_ts=thread_ts,
                        initial_comment="Data"
                    )
                    if not upload_response:
                        raise Exception("File upload failed")
                    os.remove(file_path)

                else:
                    raise FileNotFoundError(f"File not found: {file_path}")

            except Exception as table_error:
                logger.error(f"Error formatting table: {table_error}")
                pass

    # 4. Handle file uploads/downloads
    if api_response.get('has_file') and api_response.get('file_name'):
        if api_response.get("analytics_mode"):
            # Upload file for analytics mode
            try:
                file_name = api_response['file_name']
                file_path = os.path.abspath(os.path.join("static", "plots", file_name))
                
                # Only attempt upload if file exists
                if os.path.exists(file_path):
                    upload_response = slack_service.upload_file(
                        file_path=file_path,
                        title="Generated Graph",
                        channel=channel,
                        thread_ts=thread_ts,
                        initial_comment="📊 Generated visualization"
                    )
                    if not upload_response:
                        raise Exception("File upload failed")
                else:
                    raise FileNotFoundError(f"File not found: {file_path}")
                    
            except Exception as upload_error:
                logger.error(f"Error uploading file: {upload_error}")
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "❌ *Error uploading the visualization. Please try again.*"
                    }
                })
        else:
            # Add download button for non-analytics mode
            file_name = api_response['file_name']
            download_url = f"/files/download/{file_name}"
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "📥 *Download the generated file:*"
                }
            })
            
            blocks.append({
                "type": "actions",
                "elements": [{
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "Download File",
                        "emoji": True
                    },
                    "url": download_url,
                    "action_id": "download_file"
                }]
            })
    
    if api_response.get('query_code'):
        # feedback section at the end
        blocks.append({"type": "divider"})
        
        blocks.append({
            "type": "section",
            "block_id":"added_feedback_message",
            "text": {
                "type": "mrkdwn",
                "text": "Was this response helful? Please provide feedback, Your feedback helps us better increase the accuracy of the the bot"
            }
        })
        
        blocks.append({
            "type": "actions",
            "block_id": "feedback_buttons",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👍 Yes",
                        "emoji": True
                    },
                    "style": "primary",
                    "value": "yes",
                    "action_id": "feedback_yes"
                },
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "👎 No",
                        "emoji": True
                    },
                    "style": "danger",
                    "value": "no",
                    "action_id": "feedback_no"
                }
            ]
        })

    return blocks 