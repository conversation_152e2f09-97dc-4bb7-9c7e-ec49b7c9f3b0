"""
Redis Message Processor Module
Handles message deduplication and processing state using Redis
"""

import time
import logging
import async<PERSON>
from typing import Op<PERSON>, Tuple
from app.core.redis import get_redis

logger = logging.getLogger(__name__)


class RedisMessageProcessor:
    """Handles message deduplication and processing state using Redis"""
    
    def __init__(self):
        self.redis = None
        self.processing_key = "slack:processing_messages"
        self.processed_key = "slack:processed_messages"
        self.timestamps_key = "slack:message_timestamps"
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
        # Don't initialize Redis here - wait until first use
        
    def _init_redis(self):
        """Initialize Redis client with retry logic"""
        if self.redis is not None:
            return True
            
        max_retries = 3
        retry_delay = 1  # seconds
        
        for attempt in range(max_retries):
            try:
                self.redis = get_redis()
                if self.redis is not None:
                    logger.info("Successfully initialized Redis client")
                    return True
                logger.warning(f"Redis client not initialized, attempt {attempt + 1}/{max_retries}")
                time.sleep(retry_delay)
            except Exception as e:
                logger.error(f"Error initializing Redis client: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
        
        logger.error("Failed to initialize Redis client after all retries")
        return False
        
    async def _cleanup_old_entries(self):
        """Remove old entries to prevent memory leaks"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
            
        # Remove entries older than 1 hour
        cutoff_time = current_time - 3600
        
        try:
            # Get all timestamps
            timestamps = await self.redis.hgetall(self.timestamps_key)
            old_keys = [k for k, v in timestamps.items() if float(v) < cutoff_time]
            
            if old_keys:
                # Remove from all sets
                await self.redis.srem(self.processing_key, *old_keys)
                await self.redis.srem(self.processed_key, *old_keys)
                await self.redis.hdel(self.timestamps_key, *old_keys)
                
            self._last_cleanup = current_time
        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            self.redis = None  # Reset Redis client on error
    
    async def is_processing_or_processed(self, message_id: str) -> Tuple[bool, str]:
        """Check if message is being processed or already processed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return False, "redis_not_initialized"
            
        await self._cleanup_old_entries()
        
        try:
            # Use Redis pipeline for atomic operations
            async with self.redis.pipeline() as pipe:
                pipe.sismember(self.processing_key, message_id)
                pipe.sismember(self.processed_key, message_id)
                results = await pipe.execute()
                is_processing, is_processed = results
                
                if is_processing:
                    return True, "processing"
                if is_processed:
                    return True, "processed"
                return False, "new"
        except Exception as e:
            logger.error(f"Error checking message status: {e}")
            self.redis = None  # Reset Redis client on error
            return False, "error"
    
    async def mark_processing(self, message_id: str) -> bool:
        """Mark message as processing. Returns False if already processing/processed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return False
            
        try:
            # Check if already processing or processed first
            is_processing = await self.redis.sismember(self.processing_key, message_id)
            is_processed = await self.redis.sismember(self.processed_key, message_id)
            
            if is_processing or is_processed:
                return False
            
            # Use Redis transaction for atomicity
            async with self.redis.pipeline() as pipe:
                await pipe.watch(self.processing_key, self.processed_key)
                
                # Double-check after watching
                is_processing = await self.redis.sismember(self.processing_key, message_id)
                is_processed = await self.redis.sismember(self.processed_key, message_id)
                
                if is_processing or is_processed:
                    return False
                
                # Add to processing set and update timestamp
                pipe.multi()
                pipe.sadd(self.processing_key, message_id)
                pipe.hset(self.timestamps_key, message_id, time.time())
                await pipe.execute()
                return True
            
        except Exception as e:
            logger.error(f"Error marking message as processing: {e}")
            self.redis = None  # Reset Redis client on error
            return False
    
    async def mark_completed(self, message_id: str):
        """Mark message as completed"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        try:
            async with self.redis.pipeline() as pipe:
                pipe.srem(self.processing_key, message_id)
                pipe.sadd(self.processed_key, message_id)
                pipe.hset(self.timestamps_key, message_id, time.time())
                await pipe.execute()
        except Exception as e:
            logger.error(f"Error marking message as completed: {e}")
            self.redis = None  # Reset Redis client on error
    
    async def mark_failed(self, message_id: str):
        """Mark message as failed (remove from processing)"""
        if not self.redis:
            self._init_redis()  # Try to reinitialize Redis
            if not self.redis:
                logger.error("Redis client not initialized")
                return
            
        try:
            async with self.redis.pipeline() as pipe:
                pipe.srem(self.processing_key, message_id)
                pipe.hset(self.timestamps_key, message_id, time.time())
                await pipe.execute()
        except Exception as e:
            logger.error(f"Error marking message as failed: {e}")
            self.redis = None  # Reset Redis client on error


# Global processor instance
message_processor = RedisMessageProcessor() 