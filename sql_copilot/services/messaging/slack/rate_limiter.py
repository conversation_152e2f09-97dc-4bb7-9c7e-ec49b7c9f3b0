"""
Rate Limiter Service Module
Handles rate limiting for API calls
"""

import time
import threading
import logging
from typing import Dict, List

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter for controlling API call frequency"""
    
    def __init__(self, max_calls: int = 10, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls: Dict[str, List[float]] = {}
        self.lock = threading.Lock()
    
    def is_allowed(self, key: str) -> bool:
        """Check if a call is allowed for the given key"""
        with self.lock:
            now = time.time()
            
            # Clean old entries
            if key in self.calls:
                self.calls[key] = [call_time for call_time in self.calls[key] 
                                  if now - call_time < self.time_window]
            else:
                self.calls[key] = []
            
            # Check if under limit
            if len(self.calls[key]) < self.max_calls:
                self.calls[key].append(now)
                return True
            
            return False
    
    def get_remaining_calls(self, key: str) -> int:
        """Get remaining calls for the given key"""
        with self.lock:
            now = time.time()
            
            if key in self.calls:
                self.calls[key] = [call_time for call_time in self.calls[key] 
                                  if now - call_time < self.time_window]
                return max(0, self.max_calls - len(self.calls[key]))
            
            return self.max_calls
    
    def reset(self, key: str):
        """Reset rate limit for the given key"""
        with self.lock:
            if key in self.calls:
                del self.calls[key]
    
    def reset_all(self):
        """Reset all rate limits"""
        with self.lock:
            self.calls.clear()


# Global rate limiter instance
rate_limiter = RateLimiter() 