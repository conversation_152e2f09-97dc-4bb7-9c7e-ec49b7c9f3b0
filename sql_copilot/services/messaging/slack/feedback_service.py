"""
Feedback Service Module
Handles feedback data storage and retrieval
"""

import os
import json
import glob
import logging
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# Directory to store feedback data
FEEDBACK_DIR = Path("./temp_feedback")

# Ensure feedback directory exists
os.makedirs(FEEDBACK_DIR, exist_ok=True)


class FeedbackService:
    """Service class for handling feedback operations"""
    
    @staticmethod
    async def store_feedback_data(feedback_id: str, feedback_data: Dict[str, Any]) -> bool:
        """Store feedback data in a JSON file"""
        try:
            file_path = FEEDBACK_DIR / f"{feedback_id}.json"
            with open(file_path, 'w') as f:
                json.dump(feedback_data, f)
            return True
        except Exception as e:
            logger.error(f"Error storing feedback data: {e}")
            return False

    @staticmethod
    async def get_feedback_data(feedback_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve feedback data from a JSON file"""
        try:
            file_path = FEEDBACK_DIR / f"{feedback_id}.json"
            if not file_path.exists():
                return None
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error retrieving feedback data: {e}")
            return None

    @staticmethod
    async def delete_all_feedback_data() -> bool:
        """Delete all feedback data files"""
        try:
            files = glob.glob(str(FEEDBACK_DIR / "*.json"))  # Get all JSON files
            for file in files:
                os.remove(file)  # Delete each file
            return True
        except Exception as e:
            logger.error(f"Error deleting all feedback data: {e}")
            return False

    @staticmethod
    async def cleanup_feedback_data():
        """Cleanup feedback data after processing"""
        await FeedbackService.delete_all_feedback_data() 