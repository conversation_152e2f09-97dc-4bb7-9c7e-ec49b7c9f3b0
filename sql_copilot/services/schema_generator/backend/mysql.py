import concurrent.futures
from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
import mysql.connector
from mysql.connector import Error
from langchain_core.prompts import <PERSON>t<PERSON>rompt<PERSON>emplate
from utils import logger
from ..row_randomizer import generate_sample
from ..schema_analyzer import BaseSchemaAnalyzer
from config import Config
from ..categorical_detector import detect_categorical_fields
from ...schema_generator.schema_analyzer import BaseSchemaAnalyzer, SchemaAnalyzerRegistry, SchemaAnalyzer

class Field(BaseModel):
    name: str
    description: str

class TableDescription(BaseModel):
    table_name: str
    description: str
    fields: List[Field]

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        return super(CustomJSONEncoder, self).default(obj)


class MySQLSchemaAnalyzer(BaseSchemaAnalyzer):
    """
    A class to analyze and generate schema documentation for MySQL databases.
    
    This class provides functionality to:
    - Connect to a MySQL database
    - Analyze table structures and column properties
    - Generate AI-powered descriptions for tables and columns
    - Export the analysis results in JSON format
    """
    
    def __init__(self, connection_params: Dict[str, str], openai_api_key: Optional[str] = None, progress_callback=None):
        """
        Initialize the analyzer with connection parameters.
        
        Args:
            connection_params: Dict containing MySQL connection parameters:
                - host: MySQL host
                - user: MySQL username
                - password: MySQL password
                - port: MySQL port (default: 3306)
            openai_api_key: Optional OpenAI API key for generating descriptions
            progress_callback: Optional callback function to send progress messages
        """
        self.connections = connection_params.copy()
        # Set default port if not provided
        if 'port' not in self.connections:
            self.connections['port'] = 3306
        
        self.openai_api_key = Config.OPENAI_API_KEY
        if not self.openai_api_key:
            raise ValueError("OpenAI API key must be provided either in constructor or as environment variable")
        self.progress_callback = progress_callback
        logger.info("Initialized MySQL Schema Analyzer")
    
    def test_connection(self) -> Tuple[bool, Optional[str]]:
        """
        Test if the connection parameters are valid by attempting to connect to MySQL.
        
        Returns:
            Tuple containing:
            - Boolean indicating if connection was successful
            - Optional error message (None if connection successful)
        """
        try:
            # Attempt to establish a connection
            connection = mysql.connector.connect(**self.connections)
            
            # Try executing a simple query to verify connection
            cursor = connection.cursor()
            cursor.execute("SELECT NOW()")
            cursor.fetchone()
            
            # Close the connection
            cursor.close()
            connection.close()
            
            logger.info("Successfully tested connection to MySQL")
            return True, None
            
        except Error as e:
            error_msg = f"Failed to connect to MySQL: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def _execute_sql(self, query: str, database: Optional[str] = None) -> Tuple[Optional[List[Any]], Optional[str]]:
        """Execute SQL query and return results."""
        connection = None
        cursor = None
        try:
            # Create connection params with specific database if provided
            conn_params = self.connections.copy()
            if database:
                conn_params['database'] = database
            
            connection = mysql.connector.connect(**conn_params)
            cursor = connection.cursor(dictionary=True)
            
            logger.debug(f"Executing query: {query}")
            cursor.execute(query)
            result = cursor.fetchall()
            
            logger.debug(f"Query returned {len(result) if result else 0} rows")
            return result, None
            
        except Error as error:
            error_msg = f"MySQL execution error: {str(error)}"
            logger.error(f"{error_msg} for query: {query}")
            if database:
                logger.error(f"Error occurred while using database: {database}")
            return None, error_msg
        finally:
            if cursor:
                cursor.close()
            if connection and connection.is_connected():
                connection.close()
   
    def _generate_table_description(self, table_name: str, fields: List[str]) -> Dict:
        """Generate AI-powered descriptions for tables and fields."""
        try:
            model = ChatOpenAI(
                model="gpt-4o-mini",
                api_key=self.openai_api_key,
                temperature=0.2,
                max_tokens=4096
            )
            
            prompt = ChatPromptTemplate.from_messages([
                ("system", "You are a database expert who provides clear, concise descriptions of database tables and fields."),
                ("user", f"""For the database table '{table_name}', generate:
                    1. A brief description of the table's purpose and function. IMPORTANT: In this table description,describe the table explicitly list all the fields contained in this table by saying "This table contains the following fields: [list all field names]". Then proceed with the table's purpose description.
                    2. Short, clear descriptions for each field: {', '.join(fields)}
                """)
            ])
            
            chain = prompt | model.with_structured_output(TableDescription)
            response = chain.invoke({"messages": []})
            return {"table_name": response.table_name, "description": response.description, "fields": response.fields}
        
        except Exception as e:
            logger.error(f"Error generating AI descriptions: {e}")
            # Return fallback descriptions
            return {
                "table_name": table_name,
                "description": f"Table {table_name} contains the following fields: {', '.join(fields)}",
                "fields": [{"name": field, "description": f"Field {field}"} for field in fields]
            }

    def analyze_schema(self, max_workers: int = 10, **kwargs) -> Tuple[List[Dict], Dict, Optional[str]]:
        """
        Analyze one or more databases and generate comprehensive documentation.
        
        Args:
            max_workers: Maximum number of parallel workers for analysis
            **kwargs: Additional parameters including:
                - databases: Optional list of database names to analyze
            
        Returns:
            Tuple containing:
            - List of dictionaries containing table information and analysis
            - Dictionary containing analysis report
            - Optional error string (None if no errors)
        """
        try:
            start_time = time.time()
            if self.progress_callback:
                self.progress_callback("Starting MySQL schema analysis...")
            
            # Initialize report statistics
            report = {
                "total_databases": 0,
                "analyzed_databases": [],
                "total_tables_found": 0,
                "successfully_analyzed": 0,
                "failed_analysis": 0,
                "failed_tables": [],
                "execution_time": 0
            }
            
            # Get databases to analyze from kwargs
            databases = kwargs.get("databases", None)
            
            if databases is None:
                if self.progress_callback:
                    self.progress_callback("Fetching database list from MySQL server...")
                # Get all databases except system databases
                databases_query = """
                SELECT SCHEMA_NAME as database_name
                FROM INFORMATION_SCHEMA.SCHEMATA
                WHERE SCHEMA_NAME NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
                ORDER BY SCHEMA_NAME
                """
                databases_result, error = self._execute_sql(query=databases_query)
                if error:
                    logger.error(f"Error fetching databases: {error}")
                    if self.progress_callback:
                        self.progress_callback(f"Error fetching databases: {error}")
                    return [], report, f"Failed to fetch databases: {error}"
                
                databases = [db['database_name'] for db in databases_result]
            
            report["total_databases"] = len(databases)
            if self.progress_callback:
                self.progress_callback(f"Found {len(databases)} databases. Beginning analysis...")
            
            all_results = []
            
            def process_database(database: str) -> Tuple[List[Dict], Dict]:
                """Process a single database and return its results and stats"""
                if self.progress_callback:
                    self.progress_callback(f"Analyzing database: {database}")
                
                database_stats = {
                    "database_name": database,
                    "tables_count": 0,
                    "successful_tables": 0,
                    "failed_tables": 0,
                    "error": None
                }
                
                try:
                    # Get table names
                    if self.progress_callback:
                        self.progress_callback(f"Fetching tables and columns for database: {database}")
                    tables_query = """
                    SELECT TABLE_NAME as table_name
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME
                    """
                    
                    # Execute with parameter binding for MySQL
                    connection = mysql.connector.connect(**self.connections)
                    cursor = connection.cursor(dictionary=True)
                    cursor.execute(tables_query, (database,))
                    tables_result = cursor.fetchall()
                    cursor.close()
                    connection.close()
                    
                    if not tables_result:
                        logger.warning(f"No tables found in database {database}")
                        return [], database_stats
                    
                    # Extract table names
                    actual_table_names = [row["table_name"] for row in tables_result]
                    logger.info(f"Found tables in database {database}: {actual_table_names}")
                    
                    # Now get columns for each table
                    tables = {}
                    for actual_table_name in actual_table_names:
                        columns_query = """
                        SELECT 
                            COLUMN_NAME as column_name,
                            DATA_TYPE as data_type,
                            ORDINAL_POSITION as ordinal_position,
                            IS_NULLABLE as is_nullable,
                            COLUMN_DEFAULT as column_default,
                            COLUMN_TYPE as column_type
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = %s
                        AND TABLE_NAME = %s
                        ORDER BY ORDINAL_POSITION
                        """
                        
                        connection = mysql.connector.connect(**self.connections)
                        cursor = connection.cursor(dictionary=True)
                        cursor.execute(columns_query, (database, actual_table_name))
                        columns_result = cursor.fetchall()
                        cursor.close()
                        connection.close()
                        
                        if columns_result:
                            tables[actual_table_name] = []
                            for column in columns_result:
                                tables[actual_table_name].append({
                                    "name": column["column_name"],
                                    "data_type": column["data_type"],
                                    "column_type": column["column_type"],
                                    "position": column["ordinal_position"],
                                    "is_nullable": column["is_nullable"],
                                    "column_default": column["column_default"],
                                    "is_categorical": False,
                                    "description": ""
                                })
                            logger.info(f"Found {len(tables[actual_table_name])} columns for table {actual_table_name}")
                        else:
                            logger.warning(f"Could not get columns for table {database}.{actual_table_name}")
                    
                    database_stats["tables_count"] = len(tables)
                    database_results = []
                    
                    if not tables:
                        logger.warning(f"No tables with columns found in database {database}")
                        return [], database_stats
                    
                    # Process tables within this database concurrently
                    with concurrent.futures.ThreadPoolExecutor(max_workers=min(max_workers, len(tables))) as executor:
                        future_to_table = {
                            executor.submit(self._analyze_table, actual_table_name, columns, database): actual_table_name 
                            for actual_table_name, columns in tables.items()
                        }
                        
                        for future in concurrent.futures.as_completed(future_to_table):
                            actual_table_name = future_to_table[future]
                            try:
                                table_info = future.result()
                                table_info["database_name"] = database
                                database_results.append(table_info)
                                database_stats["successful_tables"] += 1
                                logger.info(f"Completed analysis for table: {database}.{actual_table_name}")
                                if self.progress_callback:
                                    self.progress_callback(f"Completed analysis for table: {database}.{actual_table_name}")
                            except Exception as exc:
                                error_msg = f"Error processing table {database}.{actual_table_name}: {exc}"
                                logger.error(error_msg)
                                database_stats["failed_tables"] += 1
                                database_results.append({
                                    "database_name": database,
                                    "table_name": f"{database}.{actual_table_name}",
                                    "description": "",
                                    "fields": [],
                                    "sample_rows": [],
                                    "error": str(exc)
                                })
                                if self.progress_callback:
                                    self.progress_callback(f"Error processing table {database}.{actual_table_name}: {exc}")
                    
                    return database_results, database_stats
                
                except Exception as e:
                    error_msg = f"Error in process_database for {database}: {str(e)}"
                    logger.error(error_msg)
                    database_stats["error"] = error_msg
                    return [], database_stats
            
            # Process databases in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(max_workers, len(databases))) as database_executor:
                future_to_database = {
                    database_executor.submit(process_database, database): database 
                    for database in databases
                }
                
                for future in concurrent.futures.as_completed(future_to_database):
                    database = future_to_database[future]
                    try:
                        database_results, database_stats = future.result()
                        all_results.extend(database_results)
                        report["analyzed_databases"].append(database_stats)
                        report["total_tables_found"] += database_stats["tables_count"]
                        report["successfully_analyzed"] += database_stats["successful_tables"]
                        report["failed_analysis"] += database_stats["failed_tables"]
                    except Exception as exc:
                        logger.error(f"Error processing database {database}: {exc}")
                        report["analyzed_databases"].append({
                            "database_name": database,
                            "tables_count": 0,
                            "successful_tables": 0,
                            "failed_tables": 0,
                            "error": str(exc)
                        })
            
            elapsed_time = time.time() - start_time
            report["execution_time"] = round(elapsed_time, 2)
            
            logger.info("\nAnalysis Report:")
            logger.info(f"Total databases analyzed: {report['total_databases']}")
            logger.info(f"Total tables found: {report['total_tables_found']}")
            logger.info(f"Successfully analyzed: {report['successfully_analyzed']}")
            logger.info(f"Failed analysis: {report['failed_analysis']}")
            logger.info(f"Execution time: {report['execution_time']} seconds")

            if self.progress_callback:
                self.progress_callback("MySQL schema analysis complete.")
            return all_results, report, None

        except Exception as e:
            error_msg = f"Error during schema analysis: {str(e)}"
            logger.error(error_msg)
            if self.progress_callback:
                self.progress_callback(error_msg)
            return [], report, error_msg

    def _analyze_table(self, actual_table_name: str, columns: List[Dict], database: str) -> Dict:
        """Analyze a single table and return its metadata in the specified format"""
        logger.info(f"Starting analysis of table: {database}.{actual_table_name}")
        if self.progress_callback:
            self.progress_callback(f"Analyzing table: {database}.{actual_table_name}")
        
        # Initialize the table structure with the ACTUAL table name
        table_info = {
            "database_name": database,
            "table_name": f"{database}.{actual_table_name}",
            "description": "",
            "fields": [],
            "status": "deactivated",
            "sample_rows": []
        }
        
        try:
            # Get field names for AI description generation
            field_names = [col["name"] for col in columns]
            
            # Generate AI descriptions for table and fields
            if self.progress_callback:
                self.progress_callback(f"Generating AI descriptions for table: {database}.{actual_table_name}")
            try:
                ai_descriptions = self._generate_table_description(actual_table_name, field_names)
                table_info["description"] = ai_descriptions["description"]
                
                # Create a mapping of field names to their descriptions
                field_descriptions = {field["name"] if isinstance(field, dict) else field.name: 
                                    field["description"] if isinstance(field, dict) else field.description 
                                    for field in ai_descriptions["fields"]}
            except Exception as e:
                logger.error(f"Error generating AI descriptions for {actual_table_name}: {e}")
                field_descriptions = {}
            
            # Get column names for the table (needed for sample rows)
            column_names = [col["name"] for col in columns]
            
            # Check if table has any data first
            count_query = f'SELECT COUNT(*) as count FROM `{database}`.`{actual_table_name}`'
            count_result, count_error = self._execute_sql(query=count_query, database=database)
            
            if count_error:
                logger.warning(f"Could not get row count for {database}.{actual_table_name}: {count_error}")
                row_count = 0
            else:
                row_count = count_result[0]['count'] if count_result and count_result[0] else 0
            
            logger.info(f"Table {database}.{actual_table_name} has {row_count} rows")
            
            # Only try to get sample rows if table has data
            json_sample_rows = []
            if row_count > 0:
                # Simple query to get sample rows
                sample_rows_query = f"""
                SELECT {', '.join(f'`{col["name"]}`' for col in columns)}
                FROM `{database}`.`{actual_table_name}`
                LIMIT 3
                """
                
                logger.debug(f"Executing sample rows query for {database}.{actual_table_name}")
                
                sample_rows_result, sample_rows_error = self._execute_sql(
                    query=sample_rows_query,
                    database=database
                )
                
                # Format sample rows as JSON objects
                if not sample_rows_error and sample_rows_result:
                    for row in sample_rows_result:
                        row_dict = {}
                        for column_name in column_names:
                            if column_name in row:
                                row_dict[column_name] = self._convert_to_json_serializable(row[column_name])
                        if row_dict:  # Only append if we have data
                            json_sample_rows.append(row_dict)
                else:
                    logger.warning(f"Could not get sample rows for {database}.{actual_table_name}: {sample_rows_error}")
            
            if json_sample_rows:
                if self.progress_callback:
                    self.progress_callback(f"Sample rows found for table: {database}.{actual_table_name}")
                try:
                    sample_row = generate_sample(sample=json_sample_rows[0])
                    table_info["sample_rows"] = sample_row
                except Exception as e:
                    logger.warning(f"Error generating sample for {database}.{actual_table_name}: {e}")
                    table_info["sample_rows"] = json_sample_rows[0] if json_sample_rows else {}
            else:
                if self.progress_callback:
                    self.progress_callback(f"No sample rows found for table {database}.{actual_table_name}")
                logger.warning(f"No sample rows found for table {database}.{actual_table_name}")
            
            # Process fields with categorical analysis
            analyzed_fields = []
            for column in columns:
                if self.progress_callback:
                    self.progress_callback(f"Analyzing field: {column['name']} in table: {database}.{actual_table_name}")
                # Check if it's a datetime
                is_datetime = column["data_type"].upper() in ("DATE", "DATETIME", "TIMESTAMP", "TIME", "YEAR")
                
                field_info = {
                    "name": column["name"],  # Use the actual column name from the database
                    "data_type": column["data_type"],  # Use the actual data type from the database
                    "column_type": column.get("column_type", ""),  # MySQL specific column type
                    "is_categorical": False,
                    "is_datetime": is_datetime,
                    "is_nullable": column.get("is_nullable", "YES") == "YES",
                    "column_default": column.get("column_default"),
                    "description": field_descriptions.get(column["name"], "")
                }
                
                # Handle different column types - only if table has data
                if row_count > 0:
                    if column["data_type"].upper() == "JSON":
                        field_info = self._analyze_json_column(f"`{database}`.`{actual_table_name}`", column["name"], field_info)
                    elif not is_datetime:  # Only analyze non-datetime fields for categorical values
                        field_info = self._analyze_regular_column(f"`{database}`.`{actual_table_name}`", column["name"], column["data_type"], field_info)
                
                analyzed_fields.append(field_info)
            
            table_info["fields"] = analyzed_fields
            
            if self.progress_callback:
                self.progress_callback(f"Completed analysis for table: {database}.{actual_table_name}")
            
        except Exception as e:
            logger.error(f"Error analyzing table {actual_table_name}: {str(e)}")
            if self.progress_callback:
                self.progress_callback(f"Error analyzing table {database}.{actual_table_name}: {str(e)}")
            table_info["error"] = str(e)
        
        return table_info
    
    def _analyze_json_column(self, table_name: str, column_name: str, column_info: Dict) -> Dict:
        """Analyze a JSON column to determine its structure"""
        column_info["json_type"] = "json_field"

        # Get sample JSON data to analyze structure
        sample_query = f"""
        SELECT `{column_name}`
        FROM {table_name}
        WHERE `{column_name}` IS NOT NULL
        LIMIT 1
        """

        sample_result, sample_error = self._execute_sql(query=sample_query)

        if not sample_error and sample_result and len(sample_result) > 0:
            try:
                sample_json = sample_result[0][column_name]
                if isinstance(sample_json, dict):
                    column_info["json_type"] = "json_object"
                    column_info["json_keys"] = list(sample_json.keys())
                elif isinstance(sample_json, list):
                    column_info["json_type"] = "json_array"
                else:
                    column_info["json_type"] = "other_json"
            except Exception as e:
                logger.debug(f"Error analyzing JSON structure for {column_name}: {e}")

        return column_info

    def _analyze_regular_column(self, table_name: str, column_name: str, data_type: str, column_info: Dict) -> Dict:
        """Analyze a regular column for categorical values"""
        
        # Skip floating point numbers and datetime types
        if data_type.upper() in ("FLOAT", "DOUBLE", "DECIMAL", "NUMERIC", "DATE", "DATETIME", "TIMESTAMP", "TIME", "YEAR"):
            return column_info
        
        try:
            # Get unique value counts
            stats_query = f"""
            SELECT 
                COUNT(DISTINCT `{column_name}`) as unique_count,
                COUNT(*) as total_count
            FROM {table_name}
            WHERE `{column_name}` IS NOT NULL
            """
            
            stats_result, stats_error = self._execute_sql(query=stats_query)
            if not stats_error and stats_result and len(stats_result) > 0:
                unique_count = stats_result[0]['unique_count']
                total_count = stats_result[0]['total_count']
                
                # Get sample values for AI-based categorical detection
                if total_count > 0:
                    values_query = f"""
                    SELECT DISTINCT `{column_name}` as value
                    FROM {table_name}
                    WHERE `{column_name}` IS NOT NULL
                    ORDER BY value
                    LIMIT 10
                    """
                    
                    values_result, values_error = self._execute_sql(query=values_query)
                  
                    if not values_error and values_result:
                        # Convert values to JSON serializable format
                        serializable_values = [
                            self._convert_to_json_serializable(row['value']) 
                            for row in values_result
                        ]
                        
                        # Prepare sample for AI detection
                        sample = {
                            "name": column_name,
                            "data_type": data_type,
                            "description": column_info.get("description", ""),
                            "unique_count": unique_count,
                            "total_count": total_count,
                            "unique_ratio": unique_count / total_count if total_count > 0 else 0,
                            "sample_values": serializable_values
                        }
                        
                        # Use AI to detect if field is categorical
                        try:
                            ai_result = detect_categorical_fields(sample)
                            is_categorical = ai_result.strip().upper() == "YES"
                        except Exception as e:
                            logger.warning(f"Error in categorical detection for {column_name}: {e}")
                            is_categorical = False

                        logger.debug(f"Table: {table_name}, Column: {column_name}, Is Categorical: {is_categorical}")
                        
                        column_info["is_categorical"] = is_categorical
                        
                        # If categorical, get the distinct values
                        if is_categorical:
                            # Get all values if we need more than the sample
                            if unique_count > 10:
                                full_values_query = f"""
                                SELECT DISTINCT `{column_name}` as value
                                FROM {table_name}
                                WHERE `{column_name}` IS NOT NULL
                                ORDER BY value
                                LIMIT 50
                                """
                                
                                full_values_result, full_values_error = self._execute_sql(query=full_values_query)
                                if not full_values_error and full_values_result:
                                    # Convert all values to JSON serializable format
                                    found_categorical_values = [
                                        self._convert_to_json_serializable(row['value']) 
                                        for row in full_values_result
                                    ]
                                    column_info["found_categorical_values"] = found_categorical_values
                            else:
                                # Use the sample values we already have
                                column_info["found_categorical_values"] = serializable_values
        
        except Exception as e:
            logger.error(f"Error analyzing column {column_name} for categorical values: {str(e)}")
            # Don't re-raise the exception, just log it and continue
        
        return column_info

    def _convert_to_json_serializable(self, obj):
        """Convert an object to a JSON-serializable type"""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        else:
            return obj
    

    def export_to_json(self, result_list: List[Dict], report: Dict, output_file: str = 'schema_analysis.json'):
        """
        Export the analysis results and report to a JSON file.
        
        Args:
            result_list: Analysis results to export
            report: Analysis report to include
            output_file: Path to the output JSON file
        """
        output = {
            "report": report,
            "tables": result_list
        }
        
        with open(output_file, 'w') as json_file:
            json.dump(output, json_file, indent=2, cls=CustomJSONEncoder)

    
SchemaAnalyzerRegistry.register('mysql', MySQLSchemaAnalyzer)