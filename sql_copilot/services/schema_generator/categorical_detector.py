import json
import os
from openai import OpenAI
from typing import Dict, List, Any, Optional
from config import Config

def detect_categorical_fields(sample: Dict, field_values: Optional[List[Any]] = None, unique_count: Optional[int] = None, total_count: Optional[int] = None) -> str:
    """
    Detect if a field is categorical based on its name, description, datatype, and sample values.
    
    Args:
        sample: Dictionary containing field information (name, description, datatype, etc.)
        field_values: Optional list of sample values from the field
        unique_count: Optional count of unique values in the field
        total_count: Optional total count of records
    
    Returns:
        "YES" if categorical, "NO" if not
    """
    client = OpenAI(api_key=Config.OPENAI_API_KEY)

    # Build context information
    context_info = []
    
    # Add basic field info
    sample_json = json.dumps(sample, indent=2)
    context_info.append(f"Field Information:\n{sample_json}")
    
    # Add statistical information if available
    if unique_count is not None and total_count is not None:
        uniqueness_ratio = unique_count / total_count if total_count > 0 else 0
        context_info.append(f"\nStatistical Information:")
        context_info.append(f"- Unique values: {unique_count}")
        context_info.append(f"- Total records: {total_count}")
        context_info.append(f"- Uniqueness ratio: {uniqueness_ratio:.3f}")
    
    # Add sample values if available
    if field_values:
        # Limit to first 10 values to avoid token limits
        sample_values = field_values[:10]
        context_info.append(f"\nSample Values: {sample_values}")
        if len(field_values) > 10:
            context_info.append(f"... and {len(field_values) - 10} more values")

    context = "\n".join(context_info)

    messages = [
        {
            "role": "system",
            "content": """You are an expert database schema analyzer specializing in categorical field detection.

CATEGORICAL FIELDS typically have these characteristics:
✓ LIMITED distinct values (usually < 50-100 unique values)
✓ DISCRETE categories or classes (status, type, category, grade, rating)
✓ ENUMERATED values (active/inactive, male/female, small/medium/large)
✓ CLASSIFICATION labels (department, role, country, state, priority)
✓ BOOLEAN or binary flags (is_active, has_discount, is_premium)
✓ CODED values (error_code, status_code, category_id with limited range)
✓ Low uniqueness ratio (unique_values/total_records < 0.1 typically)

NON-CATEGORICAL FIELDS typically have:
✗ HIGH uniqueness (names, descriptions, comments, addresses)
✗ CONTINUOUS measurements (price, weight, temperature, distance)
✗ TIMESTAMPS or dates (created_at, updated_at, birth_date)
✗ UNIQUE identifiers (user_id, transaction_id, email, phone)
✗ LARGE text content (descriptions, comments, notes, articles)
✗ NUMERICAL measurements with wide ranges (salary, revenue, quantity)
✗ High uniqueness ratio (> 0.5 typically indicates non-categorical)

SPECIAL CONSIDERATIONS:
- Field names ending with '_id' are usually NOT categorical unless they represent a small set of categories
- Numeric fields can be categorical if they represent discrete categories (ratings 1-5, priority levels)
- String fields with high uniqueness are usually NOT categorical
- Consider the business context: "country" is categorical even if it has 200+ values

Analyze ALL provided information including field name, datatype, sample values, and statistical metrics.

Respond with ONLY "YES" if the field is categorical or "NO" if it is not categorical. No explanation."""
        },
        {
            "role": "user",
            "content": f"""Analyze this field and determine if it's categorical:

{context}"""
        }
    ]
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0.0,
            max_tokens=10
        )
        
        result = response.choices[0].message.content.strip().upper()
        return "YES" if result == "YES" else "NO"
        
    except Exception as e:
        print(f"Error in categorical detection: {e}")
        # 

