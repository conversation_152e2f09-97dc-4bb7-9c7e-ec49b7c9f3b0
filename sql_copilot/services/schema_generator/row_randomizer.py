import json
import os
from openai import OpenAI
from typing import Dict
from config import Config
def generate_sample(sample: Dict) -> Dict:
    """Generate a dummy version of a data sample in JSON format for security purposes."""
    client = OpenAI(
        api_key=Config.OPENAI_API_KEY
    )

    sample_json = json.dumps(sample, indent=2)
    messages = [
        {
            "role": "system",
            "content": """You are a data anonymization assistant. Your task is to take a real data sample and create a dummy version of it in JSON format for security purposes. 
            Ensure that sensitive information is replaced with randomized values while maintaining the EXACT structure of the data. 
            
            Critical rules:
            1. Do not alter the format or structure of the data
            2. Keep null values as "null" (as string if input is string)
            3. Keep data types exactly the same as input
            4. For JSON strings, maintain the same JSON structure inside the string
            5. Do not add or remove any fields
            
            Return only the JSON response of the sample data, nothing before or after."""
        },
        {
            "role": "user",
            "content": f"""For the following data sample: 

{sample_json}

Please generate a similar data sample with randomized values while keeping the EXACT same structure.

Here's an example of good input and output:

Input:
{{
  "_id": "67d7e0cb27505ccd9f53e4ab",
  "tolerance": 0.001,
  "admin": "{{\\"id\\": \\"5c6ead695f70e3001430e6a3\\", \\"name\\": \\"Tope\\", \\"role\\": \\"engineering.manager\\"}}",
  "arbitrageInfo": "null",
  "ratesLink": "rates-2025-03-17T08:43:52.279Z.csv",
  "updatedAt": "2025-03-17T08:43:55.788000",
  "createdAt": "2025-03-17T08:43:55.788000"
}}

Good output:
{{
  "_id": "random_id883873",
  "tolerance": 0.001,
  "admin": "{{\\"id\\": \\"random_id123456\\", \\"name\\": \\"JohnDoe\\", \\"role\\": \\"engineering.manager\\"}}",
  "arbitrageInfo": "null",
  "ratesLink": "rates-2025-03-18T10:15:22.279Z.csv",
  "updatedAt": "2025-03-18T10:15:25.788000",
  "createdAt": "2025-03-18T10:15:25.788000"
}}

Notice how:
- The structure is exactly the same
- "null" remains "null" as a string
- JSON strings maintain their structure
- Data types are preserved
- No fields are added or removed

Now generate a similar anonymized version for the input data while following these rules exactly.
NOTE: TRY AS MUCH AS POSSIBLE TO MAKE SURE THE SAMPLE IS 100% FAKE 
"""
        }
    ]
    
    response = client.chat.completions.create(
        model="gpt-4o-mini",  # or your preferred model
        messages=messages,
        temperature=0.0,
        max_tokens=4096
    )
    
    return response.choices[0].message.content


