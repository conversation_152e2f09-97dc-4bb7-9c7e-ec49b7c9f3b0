from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
import json
def get_db_schema(provider,connection_id):
        try:
            '''from config import TEST_SNOQFLAKE_PATH, TEST_MONGO_DB_PATH
            with open(TEST_SNOQFLAKE_PATH, "r") as f:
                sample_snowflake  = json.load(f)["tables"]

                schemas = ["INGEST","TRANSFORMED","GROWTH_ACCOUNTING","FRESHWORKS"]

                sample_snowflake = [table for table in sample_snowflake if table["schema_name"] in schemas]

            with open(TEST_MONGO_DB_PATH, "r") as f:
                sample_mongo  = json.load(f)
                print(f"Mongodb Sample DB: {sample_mongo.keys()}")
            #pick the provider that match
            if provider == "mongodb":
                provider = DatabaseProvider.MONGODB
                db = sample_mongo
            elif provider == "snowflake":
                provider = DatabaseProvider.SNOWFLAKE
                db = sample_snowflake

            trans = SchemaTransformerFactory.create_transformer(provider)
            database_structure = trans.transform_to_json_db(db)'''


            from config import TEST_SNOWFLAEK_TRANSFORMED_PATH,TEST_POSTGRES_TRANSFORMED_PATH,TEST_MYSQL_TRANSFORMED_PATH
            with open(TEST_SNOWFLAEK_TRANSFORMED_PATH, "r") as f:
                database_structure  = json.load(f)


            # with open(TEST_MYSQL_TRANSFORMED_PATH, "r") as f:
            #     database_structure  = json.load(f)

            # with open(TEST_POSTGRES_TRANSFORMED_PATH, "r") as f:
            #     database_structure  = json.load(f)



            

            return database_structure

        
        except Exception as e:
            print(f"Error loading database structure: {e}")
            return None
        

