import requests
from config import Config

# Your Mailgun credentials (get these from mailgun.com)
API_KEY = Config.MAILGUN_API_KEY
DOMAIN = Config.MAILGUN_API_DOMAIN

def send_email(to_email, subject, message):
    """Send a simple email using Mailgun"""
    
    # Mailgun API endpoint
    url = f"https://api.mailgun.net/v3/{DOMAIN}/messages"
    
    # Email data
    data = {
        "from": f"Your Name <noreply@{DOMAIN}>",
        "to": to_email,
        "subject": subject,
        "text": message
    }
    
    # Send the email
    response = requests.post(
        url,
        auth=("api", API_KEY),
        data=data
    )
    
    # Check if it worked
    if response.status_code == 200:
        print(f"✅ Email sent successfully to {to_email}")
        return True
    else:
        print(f"❌ Failed to send email: {response.text}")
        return False

# Usage - just call this function
if __name__ == "__main__":
    send_simple_email(
        to_email="<EMAIL>",
        subject="Hello!",
        message="Hi there! This is a test email from <PERSON>."
    )