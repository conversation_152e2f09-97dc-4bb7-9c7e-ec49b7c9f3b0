from dotenv import load_dotenv
import os
import logging
import json
from .agent import SqlCopilot
from pydantic import BaseModel,Field
import traceback
import matplotlib
matplotlib.use('Agg')  # Set the backend to Agg before importing pyplot
import matplotlib.pyplot as plt
from scripts.plot import generate_plot
import pandas as pd
import uuid
import logging
import json
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from typing import List, Dict, Optional, TypedDict
from langgraph.graph import StateGraph
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage
from ..schema_generator.get_db_schema import get_db_schema
import time
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryResponse(BaseModel):
    response: str
    file_name: Optional[str] = None
    has_file: bool = False
    query_code: str = Field(default="")
    error_detail: Optional[str] = None  # Add this field
    analytics_mode: bool = False  # Corrected from a tuple to a boolean
    plot_code: str = Field(default="")
    code_data: list = []
    question_interpretation:str= ""


class QueryHandler:
    def __init__(self,connection_params:dict,query:str=None,training_examples: str=None,context:str=None,history=None,db_type="sql",company_name=None,provider=None,connection_id=None,database_structure=None):

        self.sql_llm = SqlCopilot(
            training_examples=training_examples,
            database_structure=database_structure,
            context=context,
            query=query,
            connection_params=connection_params,
            history=history,
            db_type=db_type,
            company_name=company_name,
            provider=provider,
            connection_id=connection_id
        )

    async def process_query(self) -> QueryResponse:
        try:
           
            db = get_db_schema(self.sql_llm.provider,self.sql_llm.connection_id)
            if db is None:
                return QueryResponse(
                    response="Sorry i encountered an error , try again",
                    has_file=False,
                    file_name=None,
                    query_code="",
                    error_detail="Error loading database structure"
                )
            
            self.sql_llm.database_structure = db

            response = self.sql_llm.invoke()

            if response["generate_query"]:
                # Check if execution_error is None (no error) or has a value (error occurred)
                if response["execution_error"] is None:
                    if response["mode"] == "file":
                        df = pd.DataFrame(response["data"], columns=response["filemodecols"])
                        if response["download_name"]:
                            file_name = f"{response['download_name']}_{int(time.time())}.csv"
                        else:
                            file_name = f"query_results_{int(time.time())}.csv"

                        temp_path = os.path.join("temp_files", file_name)
                        os.makedirs("temp_files", exist_ok=True)
                        df.to_csv(temp_path, index=False)

                        return QueryResponse(
                            response=response["final_response"],
                            file_name=file_name,
                            has_file=True,
                            query_code=response["executed_query"],
                            question_interpretation = response["interpreted_question"]
                        )

                    elif response["mode"] == "analytics":
                        df = pd.DataFrame(response["data"], columns=response["filemodecols"])
                        data_dict = df.to_dict(orient='records')
                        code_text = response["plot_code"]
                        print(code_text)
                        print(">>>>>>>>>>>>>>>")
                        print(df.head())

                        filename = None
                        try:
                            # Create a new figure for each plot
                            plt.clf()  # Clear any existing plots
                            plot_image_path = generate_plot(code_text, df)
                            plt.close('all')  # Close all figures to free memory
                            filename = os.path.basename(plot_image_path)
                            filename = os.path.splitext(filename)[0] + ".png"
                        except Exception as plot_error:
                            logger.error(f"Error generating plot: {str(plot_error)}")
                            filename = None

                        print(f"Image path {filename}")
                        return QueryResponse(
                            response=response["final_response"],
                            file_name=filename,  # Update to return the image path
                            has_file=True,
                            analytics_mode=True,
                            plot_code=response["plot_code"],
                            code_data=data_dict,
                            query_code=response["executed_query"],
                            question_interpretation = response["interpreted_question"]
                        )
                    else:
                        return QueryResponse(
                            response=response["final_response"],
                            has_file=False,
                            file_name=None,
                            query_code=response["executed_query"],
                            question_interpretation = response["interpreted_question"]
                        )
                else:
                    # There was an execution error
                    return QueryResponse(
                        response="Oops! Something went wrong while processing your request. \n\nNo worries—this happens sometimes. Could you please try your request again? If the issue continues, our support team is ready to help.",
                        has_file=False,
                        file_name=None,
                        query_code=response["executed_query"],
                        error_detail=response["execution_error"],
                        question_interpretation = response["interpreted_question"]
                    )
            else:
                return QueryResponse(
                    response=response["engager_question"],
                    has_file=False,
                    file_name=None,
                    query_code="",
                    question_interpretation=""
                )

        except Exception as e:
            # Get the full error traceback
            error_traceback = traceback.format_exc()

            # Create detailed error message
            error_detail = f"""
                    Error Type: {type(e).__name__}
                    Error Message: {str(e)}
                    Traceback:
                    {error_traceback}
                    """
            logger.info(error_detail)
            # Return response with error details
            return QueryResponse(
                response="Sorry i encountered an error , try again",
                has_file=False,
                file_name=None,
                query_code="",
                error_detail=error_detail
            )




if __name__ == "__main__":

    CONTEXT ={
        "INFO": "Supported Countries CODES and Their Currencies CODES according to OUR DATABASE",
        "data": {
        "Nigeria (NG)": "NGN (Nigerian Naira)",
        "United Kingdom (GB)": "GBP (British Pound)",
        "United States (US)": "USD (United States Dollar)",
        "Canada (CA)": "CAD (Canadian Dollar)",
        "Kenya (KE)": "KES (Kenyan Shilling)",
        "Ghana (GH)": "GHS (Ghanaian Cedi)",
        "Uganda (UG)": "UGX (Ugandan Shilling)",
        "Cameroon (CM)": "XAF (Central African CFA Franc)",
        "Ethiopia (ET)": "ETB (Ethiopian Birr)",
        "Haiti (HT)": "HTG (Haitian Gourde)",
        "Austria (AT)": "EUR (Euro)",
        "Belgium (BE)": "EUR (Euro)",
        "Croatia (HR)": "EUR (Euro)",
        "Cyprus (CY)": "EUR (Euro)",
        "Estonia (EE)": "EUR (Euro)",
        "Finland (FI)": "EUR (Euro)",
        "France (FR)": "EUR (Euro)",
        "Germany (DE)": "EUR (Euro)",
        "Greece (GR)": "EUR (Euro)",
        "Ireland (IE)": "EUR (Euro)",
        "Italy (IT)": "EUR (Euro)",
        "Latvia (LV)": "EUR (Euro)",
        "Lithuania (LT)": "EUR (Euro)",
        "Luxembourg (LU)": "EUR (Euro)",
        "Malta (MT)": "EUR (Euro)",
        "Netherlands (NL)": "EUR (Euro)",
        "Portugal (PT)": "EUR (Euro)",
        "Slovakia (SK)": "EUR (Euro)",
        "Slovenia (SI)": "EUR (Euro)",
        "Spain (ES)": "EUR (Euro)",
        "Romania (RO)": "EUR (Euro)",
        "Bulgaria (BG)": "EUR (Euro)",
        "Czech Republic (CZ)": "EUR (Euro)",
        "Denmark (DK)": "EUR (Euro)",
        "Hungary (HU)": "EUR (Euro)",
        "Poland (PL)": "EUR (Euro)",
        "Sweden (SE)": "EUR (Euro)",
        "Norway (NO)": "EUR (Euro)",
        "Ukraine (UA)": "EUR (Euro)",
        "Australia (AU)": "AUD (Australian Dollar)",
        "Russia (RU)": "RUB (Russian Ruble)",
        "Burkina Faso (BF)": "XOF (West African CFA Franc)",
        "Mali (ML)": "XOF (West African CFA Franc)",
        "Senegal (SN)": "XOF (West African CFA Franc)",
        "Togo (TG)": "XOF (West African CFA Franc)",
        "Guinea (GN)": "GNF (Guinean Franc)",
        "Benin (BJ)": "XOF (West African CFA Franc)",
        "Ivory Coast (CI)": "XOF (West African CFA Franc)",
        "Egypt (EG)": "EGP (Egyptian Pound)",
        "Mexico (MX)": "MXN (Mexican Peso)",
        "China (CN)": "CNY (Chinese Yuan)",
        "India (IN)": "INR (Indian Rupee)",
        "Philippines (PH)": "PHP (Philippine Peso)",
        "Brazil (BR)": "BRL (Brazilian Real)",
        "Uruguay (UY)": "UYU (Uruguayan Peso)",
        "Pakistan (PK)": "PKR (Pakistani Rupee)",
        "Rwanda (RW)": "RWF (Rwandan Franc)",
        "South Africa (ZA)": "ZAR (South African Rand)"
        }
    }




    history = [{'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'share a breakdown of how many virtual account users we have by country, sorted from highest to lowest.'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'To ensure I provide the most accurate information, could you please clarify the following:  - Are you interested in all users with virtual accounts or only those who have been active recently? - Should the breakdown include only specific countries or all countries where we have users? - Do you want to include any additional filters, such as account status or creation date?  These details will help me create the most useful response for your needs.\n\n```sql\nTo ensure I provide the most accurate information, could you please clarify the following:\n\n- Are you interested in all users with virtual accounts or only those who have been active recently?\n- Should the breakdown include only specific countries or all countries where we have users?\n- Do you want to include any additional filters, such as account status or creation date?\n\nThese details will help me create the most useful response for your needs.\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'all users, return ans slack message and limit to jus 20 of them ,name and email , consider all countries, no other filter'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'Here is a list of 20 users with their names and emails:  1. chesh142 - <mailto:<EMAIL>|<EMAIL>> 2. amarh990 - <mailto:<EMAIL>|<EMAIL>> 3. rosem753 - <mailto:<EMAIL>|<EMAIL>> 4. lawal596 - <mailto:<EMAIL>|<EMAIL>> 5. k482157 - <mailto:<EMAIL>|<EMAIL>> 6. soloc24 - <mailto:<EMAIL>|<EMAIL>> 7. mokwu82 - <mailto:<EMAIL>|<EMAIL>> 8. abdel984 - <mailto:<EMAIL>|<EMAIL>> 9. kibab971 - <mailto:<EMAIL>|<EMAIL>> 10. markj678 - <mailto:<EMAIL>|<EMAIL>> 11. julieokpetu4807 - <mailto:<EMAIL>|<EMAIL>> 12. bunmi595 - <mailto:<EMAIL>|<EMAIL>> 13. updat749 - <mailto:<EMAIL>|<EMAIL>> 14. jamal618 - <mailto:<EMAIL>|<EMAIL>> 15. henry323 - <mailto:<EMAIL>|<EMAIL>> 16. rasaq560 - <mailto:<EMAIL>|<EMAIL>> 17. sunda500 - <mailto:<EMAIL>|<EMAIL>> 18. marya954 - <mailto:<EMAIL>|<EMAIL>> 19. neilo416 - <mailto:<EMAIL>|<EMAIL>> 20. ayoom662 - <mailto:<EMAIL>|<EMAIL>>  Let me know if you need any more information!\n\n```sql\n*SQL Query:*\n```sql\nSELECT "userName" AS "Name", "email" AS "Email"\nFROM "ACCOUNTS"\nLIMIT 20;\n```\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'i asked for how many virtual account, forgottent?'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'It seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:  - A count of virtual account users per country. - The results sorted by the number of users in descending order. - The top 20 countries in terms of user count. - Names and emails of users from these countries.  Could you please confirm if this is what you need or if there are any changes?\n\n```sql\nIt seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:\n\n- A count of virtual account users per country.\n- The results sorted by the number of users in descending order.\n- The top 20 countries in terms of user count.\n- Names and emails of users from these countries.\n\nCould you please confirm if this is what you need or if there are any changes?\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'how many users singed up today'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': '3,484 users signed up today.\n\n```sql\n*SQL Query:*\n```sql\nSELECT COUNT(*) AS "Number of Signups Today" FROM "ACCOUNTS" WHERE DATE("createdAt") = CURRENT_DATE;\n```\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'tthnaks'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'Processing your request...'}]
    # Constants
    current_dir = os.path.dirname(os.path.abspath(__file__))

    DB_STRUCTURE_PATH = os.path.join(current_dir, "data/processed/database_structure.json")
    EXAMPLE_FILE_PATH = os.path.join(current_dir, "data/examples.json")


    with open(EXAMPLE_FILE_PATH, "r") as f:
            training_examples = json.load(f)

    with open(DB_STRUCTURE_PATH, "r") as f:
            DATABASE = json.load(f)
    # Example usage
    query1 = "How many users signed up today,update? cos another user must have signbed by now ?"
    query = "How many users signed up today, set engager done to yes, no further questions"
    query  = "please help with Account statement for 2024 for  <EMAIL> , set engager done to yes, no further questions" 
    query  = "can you analyze num,ber of transactions per country in bar graph" 
    provider = "mongodb"
    connection_params = {
        "username": "afriex",
        "password": "9zpYf5qVILceF09C",
        "cluster_url": "cluster0.uyahz.mongodb.net",
        "database": "dev"
    }


    handler = QueryHandler(DATABASE, training_examples, CONTEXT, query=query, history=history,provider=provider,connection_params=connection_params)

    
    # Assuming process_query is an async function, we need to run it in an event loop
    import asyncio

    async def main():
        response = await handler.process_query()
        print(response)


    asyncio.run(main())
