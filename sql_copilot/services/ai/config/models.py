

import random
class ModelConfig:
    USER_ENGAGER_TEMPERATURE = 0.00005
    _USER_ENGAGER_MODELS = ["claude-3-7-sonnet-latest", "claude-3-5-sonnet-latest"]
    
    TABLE_FILTER_TEMPERATURE = 0.00005
    _TABLE_FILTER_MODELS = ["claude-3-7-sonnet-latest", "claude-3-5-sonnet-latest"]
    
    QUERY_GENERATOR_TEMPERATURE = 0.00005
    _QUERY_GENERATOR_MODELS = ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219", "claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"]
     
    RESPONDER_TEMPERATURE = 0.5
    RESPONDER_MODEL = "gpt-4o"
    
    @property
    def TABLE_FILTER_MODEL(self):
        """Get a randomly selected table filter model"""
        return random.choice(self._TABLE_FILTER_MODELS)
    
    @property
    def QUERY_GENERATOR_MODEL(self):
        """Get a randomly selected query generator model"""
        model = random.choice(self._QUERY_GENERATOR_MODELS)
        print(f"Using MODEL : {model}")
        return model
    
    @property
    def USER_ENGAGER_MODEL(self):
        """Get a randomly selected query generator model"""
        model = random.choice(self._USER_ENGAGER_MODELS)
        print(f"Using MODEL : {model}")
        return model