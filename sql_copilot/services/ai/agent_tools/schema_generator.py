from langchain_core.tools import tool
from typing import List, Dict
from sql_copilot.services.ai.utils import get_filtered_db


@tool
def schema_generator(connection_id: str, filtered_tables: List[str], database_structure: Dict, db_type: str) -> Dict:
    """
    Fetch the structure (schema) of a database to help understand available tables and columns.
    
    Args:
        connection_id: Identifier for the database connection
        filtered_tables: List of relevant tables to consider
        database_structure: Complete database structure
        db_type: Type of database (sql, no_sql)
    
    Returns:
        JSON schema describing the filtered tables
    """
    try:
        # Get filtered database structure
        filtered_db = get_filtered_db(
            {"filtered_tables": filtered_tables}, 
            database_structure["tables"], 
            type=db_type
        )
        return {
            "status": "success",
            "schema": filtered_db,
            "connection_id": connection_id
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "connection_id": connection_id
        } 