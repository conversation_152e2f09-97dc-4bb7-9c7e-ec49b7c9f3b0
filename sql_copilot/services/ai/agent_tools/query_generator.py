from langchain_core.tools import tool
from typing import List, Dict, Literal
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON>plate
from langchain_anthropic import ChatAnthropic
import random
from sql_copilot.services.ai.utils import get_filtered_db, get_database_version_info
from sql_copilot.services.ai.prompts.query_generator import QUERY_GENERATOR
from sql_copilot.services.ai.agent_tools.custom_query_executor import QUERY_EXECUTORS
from sql_copilot.services.ai.config.models import ModelConfig
from config import Config


@tool
def query_generator(connection_id: str, 
                  filtered_tables: List[str], user_question: str, 
                  database_structure: Dict, provider: str, connection_params: Dict,
                  training_examples: List[Dict] = None, context: Dict = None,
                  company_name: str = None, db_type: str = "sql") -> Dict:
    """
    Generate SQL/NOSQL queries based on user questions.
    
    Args:
        connection_id: ID of the database
        filtered_tables: Tables to include in generation context
        user_question: The user's question to convert to SQL
        database_structure: Complete database structure
        provider: Database provider (mysql, postgres, etc.)
        connection_params: Database connection parameters
        training_examples: Training examples for query generation
        context: Additional context
        company_name: Company name for context
        db_type: Type of database (sql, no_sql)
    
    Returns:
        SQL query string
    """
    try:
        # Get database structure for the filtered tables
        filtered_db_structure = get_filtered_db(
            {"filtered_tables": filtered_tables}, 
            database_structure["tables"], 
            type=db_type
        )
        
        # Get version info
        version_info = get_database_version_info(
            provider, connection_params, QUERY_EXECUTORS
        )
        
        # Create prompt
        prompt = ChatPromptTemplate.from_messages([
            ("system", QUERY_GENERATOR[provider]()),
            ("user", f"User question: {user_question}")
        ])
        
        # Select model
        models = ["claude-sonnet-4-20250514", "claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"]
        model_name = random.choice(models)
        model_config = ModelConfig()
        
        model = ChatAnthropic(
            api_key=Config.ANTHROPIC_API_KEY_2,
            model=model_name,
            temperature=model_config.QUERY_GENERATOR_TEMPERATURE,
            max_tokens_to_sample=4096
        )
        
        # Generate query
        response = model.invoke(prompt.format_messages(
            database_structure=filtered_db_structure,
            training_examples=training_examples or [],
            user_question=user_question,
            context=context or {},
            company_name=company_name or "",
            provider=provider,
            other_db_info=version_info
        ))
        
        query = response.content
        
        return {
            "status": "success",
            "query": query,
            "connection_id": connection_id
        }
            
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "connection_id": connection_id
        } 