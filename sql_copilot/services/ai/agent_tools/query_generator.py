from langchain_core.tools import tool
from typing import List, Dict, Literal
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate
from langchain_anthropic import ChatAnthropic
import random
import json
from sql_copilot.services.ai.utils import get_filtered_db, get_database_version_info
from sql_copilot.services.ai.prompts.query_generator import QUERY_GENERATOR
from sql_copilot.services.ai.agent_tools.custom_query_executor import QUERY_EXECUTORS
from sql_copilot.services.ai.config.models import ModelConfig
from config import Config
from app.databse import get_db_session
from app.core.db_utils import get_active_tables_with_fields_and_overview
from app.core.crypto_utils import decrypt_data


@tool
def query_generator(connection_id: str,
                  filtered_tables: List[str], user_question: str,
                  database_structure: Dict = None, provider: str = None,
                  training_examples: List[Dict] = None, context: Dict = None,
                  company_name: str = None, db_type: str = None) -> Dict:
    """
    Generate SQL/NOSQL queries based on user questions.
    Fetches connection parameters from database using connection_id.

    Args:
        connection_id: ID of the database connection
        filtered_tables: Tables to include in generation context
        user_question: The user's question to convert to SQL
        database_structure: Complete database structure (optional, will fetch if not provided)
        provider: Database provider (optional, will fetch if not provided)
        training_examples: Training examples for query generation
        context: Additional context
        company_name: Company name for context
        db_type: Type of database (optional, will fetch if not provided)

    Returns:
        SQL query string
    """
    try:
        # Fetch database info using connection_id if not provided
        if not database_structure or not provider or not db_type:
            db = get_active_tables_with_fields_and_overview(get_db_session(), int(connection_id))
            if db is None:
                return {
                    "status": "error",
                    "error": "Could not load database info for the given connection_id",
                    "connection_id": connection_id
                }

            # Decrypt connection params
            connection_params = json.loads(decrypt_data(db["connection_params"]))

            # Use fetched values if not provided
            if not database_structure:
                database_structure = db
            if not provider:
                provider = db["provider"]
            if not db_type:
                db_type = db["db_type"]
        else:
            # If database_structure is provided, we still need connection_params for version info
            db = get_active_tables_with_fields_and_overview(get_db_session(), int(connection_id))
            if db is None:
                return {
                    "status": "error",
                    "error": "Could not load database info for the given connection_id",
                    "connection_id": connection_id
                }
            connection_params = json.loads(decrypt_data(db["connection_params"]))

        # Get database structure for the filtered tables
        filtered_db_structure = get_filtered_db(
            {"filtered_tables": filtered_tables},
            database_structure["tables"],
            type=db_type
        )

        # Get version info
        version_info = get_database_version_info(
            provider, connection_params, QUERY_EXECUTORS
        )
        
        # Create prompt
        prompt = ChatPromptTemplate.from_messages([
            ("system", QUERY_GENERATOR[provider]()),
            ("user", f"User question: {user_question}")
        ])
        
        # Select model
        models = ["claude-sonnet-4-20250514", "claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"]
        model_name = random.choice(models)
        model_config = ModelConfig()
        
        model = ChatAnthropic(
            api_key=Config.ANTHROPIC_API_KEY_2,
            model=model_name,
            temperature=model_config.QUERY_GENERATOR_TEMPERATURE,
            max_tokens_to_sample=4096
        )
        
        # Generate query
        response = model.invoke(prompt.format_messages(
            database_structure=filtered_db_structure,
            training_examples=training_examples or [],
            user_question=user_question,
            context=context or {},
            company_name=company_name or "",
            provider=provider,
            other_db_info=version_info
        ))
        
        query = response.content
        
        return {
            "status": "success",
            "query": query,
            "connection_id": connection_id
        }
            
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "connection_id": connection_id
        } 