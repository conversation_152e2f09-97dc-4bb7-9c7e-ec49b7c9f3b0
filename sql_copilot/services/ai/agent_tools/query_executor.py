from langchain_core.tools import tool
from typing import Dict, Literal
from sql_copilot.services.ai.agent_tools.custom_query_executor import QUERY_EXECUTORS


@tool
def executor(query_string: str, connection_id: str, provider: str, connection_params: Dict, 
            mode: Literal["direct", "file"] = "direct") -> Dict:
    """
    Execute SQL queries on a database and return the result.
    
    Args:
        query_string: SQL query to execute
        connection_id: Database identifier
        provider: Database provider (mysql, postgres, etc.)
        connection_params: Database connection parameters
        mode: "direct" returns data directly, "file" saves to file and returns path
    
    Returns:
        If mode="direct": Execution results (rows of data)
        If mode="file": File path containing the saved results
    """
    try:
        data, error = QUERY_EXECUTORS[provider](
            query=query_string,
            connection_params=connection_params
        )
        
        if error:
            return {
                "status": "error",
                "error": error,
                "connection_id": connection_id,
                "query": query_string
            }
        
        if mode == "direct":
            return {
                "status": "success",
                "data": data,
                "connection_id": connection_id,
                "query": query_string,
                "row_count": len(data) if data else 0
            }
        else:  # file mode
            # Save results to file
            import json
            file_path = f"/tmp/results_{connection_id}_{hash(query_string)}.json"
            with open(file_path, 'w') as f:
                json.dump({
                    "data": data,
                    "query": query_string,
                    "row_count": len(data) if data else 0,
                    "connection_id": connection_id
                }, f, indent=2)
            
            return {
                "status": "success",
                "file_path": file_path,
                "connection_id": connection_id,
                "query": query_string,
                "row_count": len(data) if data else 0
            }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "connection_id": connection_id,
            "query": query_string
        } 