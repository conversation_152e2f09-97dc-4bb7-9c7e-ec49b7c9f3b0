from pymongo import MongoClient
from pymongo.command_cursor import CommandCursor
from typing import Any, Union, List, Tuple, Dict
import subprocess
import sys
import tempfile
import os
import json
import pickle
import multiprocessing
import signal
import traceback


def construct_mongo_connection_string(
    username: str,
    password: str,
    cluster_url: str,
    database: str,
    options: str = "retryWrites=true&w=majority"
) -> str:
    return f"mongodb+srv://{username}:{password}@{cluster_url}/{database}?{options}"


# Option 1: Restricted Globals (Most Similar to Your Current Code)
def execute_query_mongodb(
    connection_params: Dict[str, str],
    query: str
) -> Tuple[Union[List[Any], None], Union[str, None]]:
    """
    Execute MongoDB query with heavily restricted global environment.
    Allows most query patterns but blocks dangerous operations.
    """
    try:
        connection_string = construct_mongo_connection_string(
            connection_params['username'],
            connection_params['password'],
            connection_params['cluster_url'],
            connection_params['database']
        )
        
        client = MongoClient(connection_string)
        db = client[connection_params['database']]
        
        # Create restricted globals - only allow safe MongoDB operations
        safe_globals = {
            "__builtins__": {
                # Allow only essential built-ins
                "len": len,
                "list": list,
                "dict": dict,
                "int": int,
                "float": float,
                "str": str,
                "bool": bool,
                "range": range,
                "enumerate": enumerate,
                "zip": zip,
                "max": max,
                "min": min,
                "sum": sum,
                "abs": abs,
                "round": round,
            },
            "db": db,
            # Add common MongoDB operations that might be used
            "ObjectId": client.admin.command('ismaster')  # Safe way to get ObjectId
        }
        
        # Block dangerous operations by checking the query string
        dangerous_patterns = [
            'import', 'exec', 'eval', 'compile', 'open', 'file',
            '__import__', '__builtins__', 'globals', 'locals',
            'subprocess', 'os.', 'sys.', 'socket', 'urllib',
            'requests', 'http', 'ftp', 'smtp'
        ]
        
        query_lower = query.lower()
        for pattern in dangerous_patterns:
            if pattern in query_lower:
                return None, f"Query contains potentially dangerous operation: {pattern}"
        
        # Execute with restricted environment
        result = eval(query, safe_globals, {})
        
        if isinstance(result, CommandCursor):
            result = list(result)
            
        print(f"Query executed successfully: {len(result) if isinstance(result, list) else 1} items")
        return result, None
        
    except Exception as e:
        error_msg = f"MongoDB query failed: {str(e)}"
        print(error_msg)
        return None, error_msg