import snowflake.connector
from typing import Op<PERSON>, <PERSON><PERSON>, List, Any

def execute_query_snowflake_aggressive_ssl_fix(
    query: str, 
    connection_params: dict, 
    schema: Optional[str] = None
) -> Tuple[Optional[List[Any]], Optional[str]]:
    """
    Executes a Snowflake query with aggressive SSL and connection handling settings.
    Useful in environments where standard SSL validation fails.

    Args:
        query (str): The SQL query to execute.
        connection_params (dict): Snowflake connection parameters.
        schema (Optional[str]): Optional schema override.

    Returns:
        Tuple[Optional[List[Any]], Optional[str]]: 
            - List of result rows if successful, else None.
            - Error message string if failure, else None.
    """
    print(f"Executing query: {query}")

    conn = None
    cursor = None

    try:
        conn_params = connection_params.copy()

        if schema:
            conn_params['schema'] = schema

        # Aggressive SSL fix settings
        conn_params.update({
            'insecure_mode': True,
            'ocsp_response_cache_filename': None,
            'validate_default_parameters': False,
            'disable_request_pooling': True,
            'client_prefetch_threads': 1,
            'client_session_keep_alive': True,
            'client_session_keep_alive_heartbeat_frequency': 3600,
            'numpy': False,
        })

        print("Applying aggressive SSL certificate fixes...")

        conn = snowflake.connector.connect(**conn_params)
        cursor = conn.cursor()
        cursor.execute(query)
        results = cursor.fetchall()

        print(f"Query executed successfully. Fetched {len(results)} rows.")
        return results, None

    except Exception as e:
        error_msg = f"Error executing query with aggressive SSL fix: {str(e)}"
        print(error_msg)
        return None, error_msg

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()



def execute_query_snowflake_batch(query: str, connection_params, schema: Optional[str] = None, batch_size: int = 500) -> Tuple[Optional[List[Any]], Optional[str]]:
    """Execute SQL query and return results with batch processing."""
    print(f"Query: {query}")
    print(f"Batch size: {batch_size}")
    
    if schema:
        connection_params['schema'] = schema
    
    try:
        connection = snowflake.connector.connect(**connection_params)
        cursor = connection.cursor()
        cursor.execute(query)
        
        # Fetch results in batches
        all_results = []
        batch_count = 0
        
        while True:
            batch_count += 1
            print(f"Fetching batch {batch_count}...")
            
            # Fetch batch of results
            batch_results = cursor.fetchmany(batch_size)
            
            if not batch_results:
                print("No more results to fetch.")
                break
                
            all_results.extend(batch_results)
            print(f"Batch {batch_count}: Fetched {len(batch_results)} records. Total so far: {len(all_results)}")
            
            # Optional: Add a small delay between batches
            time.sleep(0.1)
        
        
        
        print(f"Total records fetched: {len(all_results)}")
        return all_results, None
        
    except Exception as e:
        error_msg = f"Error during batch execution: {str(e)}"
        print(error_msg)
        return None, error_msg

    finally:
      cursor.close()
      connection.close()
      
# def execute_query_snowflake_aggressive_ssl_fix(
#     query: str, 
#     connection_params: dict, 
#     schema: Optional[str] = None
# ) -> Tuple[Optional[List[Any]], Optional[str]]:
#     """
#     More aggressive SSL fix approach for persistent certificate issues.
#     """
#     print(f"Query: {query}")
    
#     # Apply the most comprehensive SSL fixes
#     conn_params = connection_params.copy()
    
#     if schema:
#         conn_params['schema'] = schema
    
#     # Aggressive SSL bypass parameters
#     conn_params.update({
#         'insecure_mode': True,
#         'ocsp_response_cache_filename': None,
#         'validate_default_parameters': False,
#         'disable_request_pooling': True,
#         'client_prefetch_threads': 1,
        
#         # Additional aggressive parameters
#         'client_session_keep_alive': True,
#         'client_session_keep_alive_heartbeat_frequency': 3600,
        
#         # Try to force different result handling
#         'numpy': False,  # Disable numpy optimization that might affect result fetching
#     })
    
#     print("Applying aggressive SSL certificate fixes...")
    
   
#     connection = snowflake.connector.connect(**conn_params)
#     cursor = connection.cursor()
    
#     cursor.execute(query)
#     results = cursor.fetchall()
    
#     cursor.close()
#     connection.close()
    
#     return results
        
    