import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Optional, Tuple, List, Any

def execute_query_postgres(
    query: str, 
    connection_params: dict, 
    schema: Optional[str] = None
) -> Tuple[Optional[List[Any]], Optional[str]]:
    """
    Execute SQL query on PostgreSQL database and return results.

    Args:
        query (str): SQL query to execute
        connection_params (dict): Database connection parameters
        schema (Optional[str]): Optional schema name to use

    Returns:
        Tuple[Optional[List[Any]], Optional[str]]: 
            - Query results as list of dicts if successful
            - Error message string if failure, else None
    """
    print(f"Executing PostgreSQL query: {query}")

    conn = None
    cursor = None

    try:
        conn_params = connection_params.copy()

        if schema:
            # Set schema using PostgreSQL's `search_path` option
            conn_params['options'] = f'-c search_path={schema}'

        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        cursor.execute(query)
        results = cursor.fetchall()
        results_list = [dict(row) for row in results]

        print(f"Query executed successfully. Fetched {len(results_list)} rows.")
        return results_list, None

    except Exception as e:
        error_msg = f"Error executing PostgreSQL query: {str(e)}"
        print(error_msg)
        return None, error_msg

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
