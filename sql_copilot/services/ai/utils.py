
from dotenv import load_dotenv
import os
import logging
import json

def get_filtered_db(state: dict, DATABASE: dict, type: str = "sql") -> list:
    if type not in ["sql", "no_sql"]:
        raise ValueError("type must be 'sql' or 'no_sql'")

    if type == "sql":
        database_structure = [
                table for table in DATABASE if table["table_name"] in state["filtered_tables"]
            ]
    else:
        database_structure = [
                table for table in DATABASE if table.get("collection_name") or table.get("table_name") in state["filtered_tables"]
            ]
    return database_structure


def get_current_date() -> str:
    from datetime import datetime
    import pytz

    # Get current date and time in UTC
    utc_now = datetime.now(pytz.utc)
    # Format the current date and time in words
    return utc_now.strftime("%A, %B %d, %Y at %I:%M %p UTC")




def get_database_version_info(provider: str, connection_params: dict, query_executors: dict) -> str:
    """
    Get database version information for different providers.
    
    Args:
        provider (str): Database provider name ('postgres', 'mysql', etc.)
        connection_params (dict): Database connection parameters
        query_executors (dict): Dictionary of query executor functions
        
    Returns:
        str: Formatted version information string
    """
    version_queries = {
        "postgres": "SELECT version();",
        "mysql": "SELECT VERSION() as version;",
        # Add more providers as needed
        # "oracle": "SELECT * FROM v$version WHERE banner LIKE 'Oracle%';",
        # "sqlserver": "SELECT @@VERSION as version;"
    }
    
    
    if provider not in version_queries:
        return f"VERSION INFO: Not Provider for {provider.upper()} "
    
    try:
        version_query = version_queries[provider]
        result = query_executors[provider](query=version_query, connection_params=connection_params)
        
        if result and len(result) > 0:
            # Handle different response formats
            if provider == "postgres":
                version_info = result[0]['version']
            elif provider == "mysql":
                version_info = result[0]['version']
            
            print(f"{provider.upper()} Version: {version_info}")
            return f"{provider.upper()} VERSION: {version_info}"
        else:
            return f"{provider.upper()} VERSION: Not specified"
            
    except Exception as e:
        print(f"Error getting {provider} version: {str(e)}")
        return f"{provider.upper()} VERSION: Not specified"


