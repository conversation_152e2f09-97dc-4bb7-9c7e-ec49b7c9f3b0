import json
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from typing import List, Dict, Optional, TypedDict
from langgraph.graph import StateGraph
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

# Define your state

# Define your state
class AgentState(TypedDict):
    messages: List[HumanMessage | AIMessage]
    question: str

    # User engager
    engager_done: bool = False
    interpreted_question: str
    status: str
    engager_question: str

    generate_query:bool=False

    # Table filter
    filtered_tables: List[str] = []
    retry_count: int = 0

    # Query generator
    executed_query: str
    mode: str
    has_analytics: bool = False
    plot_code: str
    generator_message: str
    filemodecols: List[str]
    is_account_statement: bool = False

    # Query reviewer
    query_approved: bool = False
    reviewer_message: str = ""

    # Query execution
    data: list
    execution_error: str = None

    # Responder
    download_name: str = None
    final_response: str