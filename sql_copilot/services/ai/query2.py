"""
SQL Copilot: AI-powered SQL query generation and execution system with token tracking.
"""

import requests
import json
import os
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
from typing_extensions import TypedDict, Literal

# Lang<PERSON>hain imports
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.prompts import Chat<PERSON>rom<PERSON><PERSON>emplate, MessagesPlaceholder
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

# Pydantic imports
from pydantic import BaseModel

# Environment and config
from dotenv import load_dotenv
from config import Config
from .config.models import ModelConfig

# Internal imports
from .prompts.user_engager import get_user_engagement_prompt_v3
from .prompts.responder import response_formulation_prompt
from .prompts.table_filter import table_filter_prompt_v2
from .prompts.query_generator.mongodb import mongo_query_guidance
from .prompts.query_generator import QUERY_GENERATOR
from .utils import get_filtered_db, get_database_version_info
from .core import AgentState
from .agent_tools.custom_query_executor import QUERY_EXECUTORS
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider




# =====================================================
# DATA MODELS AND TYPES
# =====================================================

@dataclass
class Message:
    """Represents a conversation message."""
    role: str  # 'human' or 'ai'
    content: str


@dataclass
class TokenUsage:
    """Tracks token consumption for API calls."""
    input_tokens: int = 0
    output_tokens: int = 0
    
    def __add__(self, other: 'TokenUsage') -> 'TokenUsage':
        return TokenUsage(
            input_tokens=self.input_tokens + other.input_tokens,
            output_tokens=self.output_tokens + other.output_tokens
        )
    
    @property
    def total_tokens(self) -> int:
        return self.input_tokens + self.output_tokens


class UserEngagnerOutput(BaseModel):
    """Output model for user engagement step."""
    generate_query: bool
    interpreted_question: str
    status: str
    engager_question: str


class TableFilterOutput(BaseModel):
    """Output model for table filtering step."""
    tables: List[str]


class QueryGeneratorOutput(BaseModel):
    """Output model for query generation step."""
    executed_query: str
    mode: str
    has_analytics: bool
    plot_code: str
    filemodecols: List[str]
    download_name: str


class ResponderOutput(BaseModel):
    """Output model for response formulation step."""
    message: str


# =====================================================
# CONFIGURATION CONSTANTS
# =====================================================

class ModelConstants:
    """Centralized model configuration constants."""
    
    def __init__(self):
        self.model_config = ModelConfig()
        self.claude_api_key = Config.ANTHROPIC_API_KEY_2
        
        # User Engager
        self.USER_ENGAGER_TEMPERATURE = ModelConfig.USER_ENGAGER_TEMPERATURE
        self.USER_ENGAGER_MODEL = ModelConfig.USER_ENGAGER_MODEL
        
        # Table Filter
        self.TABLE_FILTER_TEMPERATURE = ModelConfig.TABLE_FILTER_TEMPERATURE
        self.TABLE_FILTER_MODEL = ModelConfig.TABLE_FILTER_MODEL
        
        # Query Generator
        self.QUERY_GENERATOR_TEMPERATURE = ModelConfig.QUERY_GENERATOR_TEMPERATURE
        self.QUERY_GENERATOR_MODEL = ModelConfig.QUERY_GENERATOR_MODEL
        
        # Responder
        self.RESPONDER_TEMPERATURE = ModelConfig.RESPONDER_TEMPERATURE
        self.RESPONDER_MODEL = ModelConfig.RESPONDER_MODEL


# =====================================================
# TOKEN TRACKING UTILITIES
# =====================================================

class TokenTracker:
    """Handles token usage tracking for different AI providers."""
    
    def __init__(self):
        self.openai_usage = TokenUsage()
        self.anthropic_usage = TokenUsage()
    
    def _extract_token_usage(self, response: Any) -> TokenUsage:
        """Extract token usage from various response formats."""
        input_tokens = output_tokens = 0
        
        # Handle Anthropic responses
        if hasattr(response, 'usage'):
            # Direct usage attribute (newer Anthropic client)
            usage = response.usage
            input_tokens = getattr(usage, 'input_tokens', 0)
            output_tokens = getattr(usage, 'output_tokens', 0)
        
        # Handle LangChain ChatAnthropic responses
        elif hasattr(response, 'response_metadata') and response.response_metadata:
            metadata = response.response_metadata
            
            # Check for usage in metadata
            if hasattr(metadata, 'usage'):
                usage = metadata.usage
                input_tokens = getattr(usage, 'input_tokens', 0)
                output_tokens = getattr(usage, 'output_tokens', 0)
            elif isinstance(metadata, dict):
                # Try different possible paths in the metadata dictionary
                if 'token_usage' in metadata:
                    token_usage = metadata['token_usage']
                    input_tokens = token_usage.get('prompt_tokens', 0)
                    output_tokens = token_usage.get('completion_tokens', 0)
                elif 'usage' in metadata:
                    usage = metadata['usage']
                    input_tokens = usage.get('input_tokens', 0) or usage.get('prompt_tokens', 0)
                    output_tokens = usage.get('output_tokens', 0) or usage.get('completion_tokens', 0)
        
        # Handle OpenAI responses
        elif hasattr(response, 'model_dump'):
            # Try to use model_dump() method for Pydantic models
            try:
                dump = response.model_dump()
                if 'usage' in dump:
                    usage = dump['usage']
                    input_tokens = usage.get('prompt_tokens', 0)
                    output_tokens = usage.get('completion_tokens', 0)
            except Exception as e:
                print(f"Error extracting from model_dump: {e}")
        
        # Try to access raw response for LangChain structured output
        if input_tokens == 0 and output_tokens == 0 and hasattr(response, '_raw_response'):
            raw = response._raw_response
            if hasattr(raw, 'usage'):
                usage = raw.usage
                input_tokens = getattr(usage, 'prompt_tokens', 0) or getattr(usage, 'input_tokens', 0)
                output_tokens = getattr(usage, 'completion_tokens', 0) or getattr(usage, 'output_tokens', 0)
        
        return TokenUsage(input_tokens=input_tokens, output_tokens=output_tokens)
       
    
    def extract_and_track_usage(self, response: Any, provider: str) -> None:
        """Extract token usage from model response and update counters."""
        try:
            # For debugging
            print(f"Response type: {type(response)}")
            if hasattr(response, 'usage'):
                print(f"Direct usage attribute found: {response.usage}")
            if hasattr(response, 'response_metadata'):
                print(f"Response metadata found: {response.response_metadata}")
            if hasattr(response, '_raw_response'):
                print(f"Raw response found with type: {type(response._raw_response)}")
                
            token_usage = self._extract_token_usage(response)
            
            if provider.lower() == 'openai':
                self.openai_usage += token_usage
            elif provider.lower() == 'anthropic':
                self.anthropic_usage += token_usage
                
            print(f"Token usage for {provider}: Input={token_usage.input_tokens}, Output={token_usage.output_tokens}")
            
            # If no tokens were extracted, log a warning
            if token_usage.total_tokens == 0:
                print(f"WARNING: No token usage information extracted from {provider} response")
                
        except Exception as e:
            print(f"Error extracting token usage: {e}")
            import traceback
            traceback.print_exc()
    
    def get_summary(self) -> Dict[str, Dict[str, int]]:
        """Get comprehensive token usage summary."""
        return {
            "openai": {
                "input_tokens": self.openai_usage.input_tokens,
                "output_tokens": self.openai_usage.output_tokens,
                "total_tokens": self.openai_usage.total_tokens
            },
            "anthropic": {
                "input_tokens": self.anthropic_usage.input_tokens,
                "output_tokens": self.anthropic_usage.output_tokens,
                "total_tokens": self.anthropic_usage.total_tokens
            },
            "combined_total": {
                "input_tokens": self.openai_usage.input_tokens + self.anthropic_usage.input_tokens,
                "output_tokens": self.openai_usage.output_tokens + self.anthropic_usage.output_tokens,
                "total_tokens": self.openai_usage.total_tokens + self.anthropic_usage.total_tokens
            }
        }
    
    def print_summary(self) -> None:
        """Print formatted token usage summary."""
        usage = self.get_summary()
        
        print("\n" + "="*50)
        print("TOKEN USAGE SUMMARY")
        print("="*50)
        
        print(f"OpenAI Usage:")
        print(f"  Input Tokens:  {usage['openai']['input_tokens']:,}")
        print(f"  Output Tokens: {usage['openai']['output_tokens']:,}")
        print(f"  Total Tokens:  {usage['openai']['total_tokens']:,}")
        
        print(f"\nAnthropic Usage:")
        print(f"  Input Tokens:  {usage['anthropic']['input_tokens']:,}")
        print(f"  Output Tokens: {usage['anthropic']['output_tokens']:,}")
        print(f"  Total Tokens:  {usage['anthropic']['total_tokens']:,}")
        
        print(f"\nCombined Total:")
        print(f"  Input Tokens:  {usage['combined_total']['input_tokens']:,}")
        print(f"  Output Tokens: {usage['combined_total']['output_tokens']:,}")
        print(f"  Total Tokens:  {usage['combined_total']['total_tokens']:,}")
        print("="*50)


# =====================================================
# WORKFLOW STEP HANDLERS
# =====================================================

class WorkflowSteps:
    """Handles individual workflow steps with proper separation of concerns."""
    
    def __init__(self, copilot_instance: 'SqlCopilot'):
        self.copilot = copilot_instance
        self.constants = copilot_instance.constants
        self.token_tracker = copilot_instance.token_tracker
    
    def user_engager(self, state: AgentState) -> Dict[str, Any]:
        """Handle user engagement and question interpretation."""
        print("Entering user_engager method.")
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", get_user_engagement_prompt_v3()),
            MessagesPlaceholder(variable_name="messages")
        ])
        
        model = ChatAnthropic(
            api_key=self.constants.claude_api_key,
            model=self.constants.model_config.USER_ENGAGER_MODEL,
            temperature=self.constants.USER_ENGAGER_TEMPERATURE
        )
        model = model.with_structured_output(UserEngagnerOutput)
        
        prompt_input = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": self.copilot.context,
            "database_structure": self.copilot.database_structure["schemas"],
            "training_examples": self.copilot.training_examples,
            'provider': self.copilot.provider,
            "company_name": self.copilot.company_name
        })
        
        response = model.invoke(prompt_input)
        self.token_tracker.extract_and_track_usage(response, 'anthropic')
        
        print("User engagement response received.")
        print(f"USER ENGAGER RESPONSE: {response}")
        
        return {
            "generate_query": response.generate_query,
            "interpreted_question": response.interpreted_question,
            "status": response.status,
            "engager_question": response.engager_question,
            "retry_count": state.get("retry_count", 0)
        }
    
    def tables_filter(self, state: AgentState) -> Dict[str, Any]:
        """Filter relevant tables based on the query."""
        print("Entering tables_filter method.")
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", table_filter_prompt_v2()),
            MessagesPlaceholder(variable_name="messages")
        ])
        
        print(f"TABLE_FILTER_MODEL: {self.constants.model_config.TABLE_FILTER_MODEL}")
        model = ChatAnthropic(
            api_key=self.constants.claude_api_key,
            model=self.constants.model_config.TABLE_FILTER_MODEL,
            temperature=self.constants.TABLE_FILTER_TEMPERATURE
        )
        model = model.with_structured_output(TableFilterOutput)
        
        prompt_input = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": self.copilot.context,
            "database_structure": self.copilot.database_structure["schemas"],
            "interpreted_question": state["interpreted_question"]
        })
        
        response = model.invoke(prompt_input)
        self.token_tracker.extract_and_track_usage(response, 'anthropic')
        
        print(f"Tables filtered response received: {response.tables}")
        return {"filtered_tables": response.tables}
    
    def query_generator(self, state: AgentState) -> Dict[str, Any]:
        """Generate SQL query based on filtered tables and user requirements."""
        print("Entering query_generator method.")
        
        database_structure = get_filtered_db(
            state, 
            self.copilot.database_structure["tables"], 
            type=self.copilot.db_type
        )
        
        # Handle retry logic
        current_retry_count = state.get("retry_count", 0)
        is_retry = current_retry_count > 0 or state.get("execution_error") is not None
        
        if is_retry:
            current_retry_count += 1
            print(f"RETRY MODE: Attempt {current_retry_count}/{self.copilot.max_retry}")
        
        # Get database version info
        version_info = get_database_version_info(
            self.copilot.provider, 
            self.copilot.connection_params, 
            QUERY_EXECUTORS
        )
        print(f"Database version info: {version_info}")
        
        # Build enhanced prompt for retries
        base_prompt = QUERY_GENERATOR[self.copilot.provider]()
        enhanced_prompt = self._build_retry_prompt(base_prompt, state, is_retry, current_retry_count)
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", enhanced_prompt),
            MessagesPlaceholder(variable_name="messages")
        ])
        
        print(f"Query Generator MODEL: {self.constants.model_config.QUERY_GENERATOR_MODEL}")
        model = ChatAnthropic(
            api_key=self.constants.claude_api_key,
            model=self.constants.model_config.QUERY_GENERATOR_MODEL,
            temperature=self.constants.QUERY_GENERATOR_TEMPERATURE,
            max_tokens_to_sample=4096
        )
        model = model.with_structured_output(QueryGeneratorOutput)
        
        prompt_input = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": self.copilot.context,
            "database_structure": database_structure,
            "training_examples": self.copilot.training_examples,
            "user_question": state["interpreted_question"],
            "no_sql_sample": self.copilot.no_sql_sample,
            "company_name": self.copilot.company_name,
            "provider": self.copilot.provider,
            "other_db_info": f"\n{version_info}\n"
        })
        
        response = model.invoke(prompt_input)
        self.token_tracker.extract_and_track_usage(response, 'anthropic')
        
        print(f"Query generated. Retry count: {current_retry_count}")
        
        return {
            "executed_query": response.executed_query,
            "mode": response.mode,
            "has_analytics": response.has_analytics,
            "plot_code": response.plot_code,
            "filemodecols": response.filemodecols,
            "retry_count": current_retry_count,
            "download_name": response.download_name
        }
    
    def _build_retry_prompt(self, base_prompt: str, state: AgentState, is_retry: bool, current_retry_count: int) -> str:
        """Build enhanced prompt for retry scenarios."""
        if not is_retry:
            return base_prompt
        
        retry_instructions = """
        
        🔄 **QUERY RETRY MODE ACTIVATED** 🔄

        The previous query encountered an execution error. Please carefully analyze the error and generate a corrected query.

        **Previous Query That Failed:**
        {previous_query}

        **Execution Error Encountered:**
        {execution_error}

        **RETRY INSTRUCTIONS:**
        1. Carefully analyze the execution error message
        2. Identify the specific issue (syntax, missing columns, wrong table names, etc.)
        3. Generate a corrected query that addresses the error
        4. Ensure the new query follows all syntax rules for {provider}
        5. Double-check column names, table names, and data types against the database structure
        6. Validate that all referenced fields exist in the provided database schema

        This is retry attempt #{retry_count} of {max_retry}. Make sure the corrected query works properly.
        """.format(
            previous_query=state.get("generated_query", ""),
            execution_error=state.get("execution_error", ""),
            provider=self.copilot.provider,
            retry_count=current_retry_count,
            max_retry=self.copilot.max_retry
        )
        
        return base_prompt + retry_instructions
    
    def query_executor(self, state: AgentState) -> Dict[str, Any]:
        """Execute the generated SQL query."""
        print("Entering query_executor method.")
        
        current_query = state["executed_query"]
        error_message = None
        data = None
        retry_count = state.get("retry_count", 0)
        
        print(f"Executing query (attempt {retry_count + 1}): {current_query[:100]}...")
        
        try:
            data, error = QUERY_EXECUTORS[self.copilot.provider](
                query=current_query,
                connection_params=self.copilot.connection_params
            )
            print("Query executed successfully.")
        except Exception as error:
            error_message = str(error)
            print(f"Error executing query: {error_message}")
        
        return {
            "data": data,
            "execution_error": error_message,
            "retry_count": retry_count
        }
    
    def responder(self, state: AgentState) -> Dict[str, Any]:
        """Formulate the final response to the user."""
        print("Entering responder method.")
        
        data = None if state["mode"] == "file" else state["data"]
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", response_formulation_prompt()),
            MessagesPlaceholder(variable_name="messages")
        ])
        
        model = ChatOpenAI(
            model=self.constants.RESPONDER_MODEL,
            temperature=self.constants.RESPONDER_TEMPERATURE
        )
        model = model.with_structured_output(ResponderOutput)
        
        prompt_input = prompt.invoke({
            "messages": state["messages"],
            "interpreted_question": state["interpreted_question"],
            "data": data,
            "execution_error": state["execution_error"],
            "mode": state["mode"],
        })
        
        response = model.invoke(prompt_input)
        self.token_tracker.extract_and_track_usage(response, 'openai')
        
        print("Responder response generated.")
        return {"final_response": response.message}


# =====================================================
# MAIN SQL COPILOT CLASS
# =====================================================

class SqlCopilot:
    """
    Main SQL Copilot class that orchestrates the entire workflow.
    Handles AI-powered SQL query generation, execution, and response formulation.
    """
    
    def __init__(
        self,
        provider: str,
        connection_params: dict,
        database_structure: List[Dict],
        training_examples: Optional[List[Dict]] = None,
        context: Optional[Dict] = None,
        history: Optional[List[Message]] = None,
        query: Optional[str] = None,
        db_type: str = "sql",
        no_sql_sample: Optional[Any] = None,
        connection_id: Optional[str] = None,
        company_name: Optional[str] = None
    ):
        """Initialize SQL Copilot with configuration and dependencies."""
        
        # Core configuration
        self.provider = provider
        self.connection_params = connection_params
        self.database_structure = database_structure
        self.training_examples = training_examples
        self.context = context
        self.history = history if history is not None else []
        self.query = query
        self.db_type = db_type
        self.no_sql_sample = no_sql_sample
        self.connection_id = connection_id
        self.company_name = company_name
        self.max_retry = 1
        
        # Initialize components
        self.constants = ModelConstants()
        self.token_tracker = TokenTracker()
        self.workflow_steps = WorkflowSteps(self)
        
        # Handle NoSQL specific setup
        if self.db_type == "no_sql":
            self.no_sql_sample = mongo_query_guidance
        
        print("SqlCopilot initialized with training examples and database structure.")
    
    # =====================================================
    # WORKFLOW CREATION AND MANAGEMENT
    # =====================================================
    
    def create_workflow(self) -> StateGraph:
        """Create and configure the LangGraph workflow."""
        print("Creating workflow.")
        
        workflow = StateGraph(state_schema=AgentState)
        
        # Add nodes
        workflow.add_edge(START, "user_engager")
        workflow.add_node("user_engager", self.workflow_steps.user_engager)
        workflow.add_node("query_generator", self.workflow_steps.query_generator)
        workflow.add_node("query_executor", self.workflow_steps.query_executor)
        workflow.add_node("responder", self.workflow_steps.responder)
        workflow.add_node("tables_filter", self.workflow_steps.tables_filter)
        
        # Define standard edges
        workflow.add_edge("tables_filter", "query_generator")
        workflow.add_edge("responder", END)
        
        # Add conditional edges
        workflow.add_conditional_edges("user_engager", self._conditional_edge_responder)
        workflow.add_conditional_edges("query_executor", self._conditional_edge_executor)
        workflow.add_conditional_edges("query_generator", self._conditional_edge_query_generator)
        
        # Compile with memory
        memory = MemorySaver()
        app = workflow.compile(checkpointer=memory)
        
        print("Workflow created successfully.")
        return app
    
    # =====================================================
    # CONDITIONAL EDGE HANDLERS
    # =====================================================
    
    def _conditional_edge_responder(self, state: AgentState) -> Literal[END, "tables_filter"]:
        """Determine whether to end or proceed to table filtering."""
        return END if not state["generate_query"] else "tables_filter"
    
    def _conditional_edge_executor(self, state: AgentState) -> Literal["responder", "query_generator"]:
        """Determine whether to retry query generation or proceed to response."""
        execution_error = state.get("execution_error")
        retry_count = state.get("retry_count", 0)
        
        print(f"Conditional edge executor - Error: {bool(execution_error)}, Retry count: {retry_count}/{self.max_retry}")
        
        if execution_error and retry_count < self.max_retry:
            print(f"Execution error detected. Will retry. Current count: {retry_count}")
            return "query_generator"
        else:
            if execution_error:
                print(f"Max retries ({self.max_retry}) reached. Proceeding to responder with error.")
            else:
                print("Query executed successfully. Proceeding to responder.")
            return "responder"
    
    def _conditional_edge_query_generator(self, state: AgentState) -> Literal["query_executor"]:
        """After query generation, always proceed to execution."""
        return "query_executor"
    
    # =====================================================
    # UTILITY METHODS
    # =====================================================
    
    def convert_to_langchain_messages(self, messages: List[Message]) -> List[Union[HumanMessage, AIMessage]]:
        """Convert Message objects to LangChain message objects."""
        return [
            HumanMessage(content=msg["message"]) if msg["role"] == "user" 
            else AIMessage(content=msg["message"])
            for msg in messages
        ]
    
    # =====================================================
    # MAIN EXECUTION METHOD
    # =====================================================
    
    def invoke(self) -> Dict[str, Any]:
        """
        Execute the complete SQL Copilot workflow.
        
        Returns:
            Dict containing the workflow output and token usage statistics.
        """
        print("Invoking the workflow.")
        
        # Setup workflow
        conversation_id = "mr_random"
        config = {"configurable": {"thread_id": conversation_id}}
        app = self.create_workflow()
        
        # Prepare input messages
        if not self.history:
            input_messages = [HumanMessage(content=self.query)] if self.query else [HumanMessage(content="Let's get started")]
        else:
            history = self.convert_to_langchain_messages(self.history)
            input_messages = history + ([HumanMessage(content=self.query)] if self.query else [])
        
        # Initialize state
        initial_state = {
            "messages": input_messages,
            "retry_count": 0
        }
        
        # Execute workflow
        output = app.invoke(initial_state, config)
        
        # Add token usage reporting
        self.token_tracker.print_summary()
        output["token_usage"] = self.token_tracker.get_summary()
        
        print("Workflow invoked successfully.")
        return output