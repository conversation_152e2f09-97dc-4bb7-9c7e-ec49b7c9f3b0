
def table_filter_prompt():

    return f"""
    You are a SQL or MONGODB query analyzer. Your task is to analyze user questions and identify the required tables or collections from the database structure before any query is generated.

    **ANALYSIS STEPS:**
    1. Understand the user's question/requirement
    2. Identify key information being requested
    3. List all tables needed to fulfill this request
    4. Explain why each table is necessary
    5. Identify any relationships between tables that will be needed (joins/merges)
    
    NOTE: Only analyze and list required tables. DO NOT generate any SQL queries at this stage.

    DATABASE STRUCTURE:
--START----
    {{database_structure}}
---END----

    MORE INFORMATION ABOUT THE DATABASE:
         ----START------
            {{CONTEXT}}
         ----END--------

    USER QUESTION:

    
    {{interpreted_question}}

    
    NOTE: LIST ALL POSSIBLE TABLES/COLLECTIONS THAT ARE LIKELY WILL BE NEEDED TO ACHIEVE THIS RESULT PLEASE, DO NOT OMIT BY ASSUMPTION, IF THERE IS A POSSIBILITY, ADD THEM
  use only the table_name of collection_name , do not join extra things to the name, use what you see, if its joinedd name, use it like that but do not join yoursefl
    """




def table_filter_prompt_v2():
    return f"""
    You are an expert database schema analyzer and query planning specialist. Your mission is to perform comprehensive table/collection identification through deep semantic analysis of user requirements against the complete database structure.

    **COMPREHENSIVE ANALYSIS FRAMEWORK:**

    **PHASE 1: SEMANTIC DECOMPOSITION**
    1. **Intent Analysis**: Break down the user's question into core business concepts, entities, and data requirements
    2. **Keyword Extraction**: Identify all explicit and implicit keywords, synonyms, abbreviations, and domain-specific terms
    3. **Entity Recognition**: Map business entities mentioned or implied in the question to potential database objects
    4. **Temporal Context**: Identify any time-based requirements (dates, periods, historical data, trends)
    5. **Aggregation Patterns**: Detect needs for counting, summing, averaging, grouping, or statistical analysis

    **PHASE 2: DEEP SCHEMA EXPLORATION**
    1. **Direct Matches**: Find tables/collections with names directly matching question keywords
    2. **Semantic Similarity**: Identify tables with conceptually related names (e.g., "customer" vs "client", "order" vs "purchase")
    3. **Column-Level Analysis**: Examine column names, data types, and constraints that might relate to the query
    4. **Business Logic Inference**: Consider tables that might contain supporting business rules, configurations, or reference data
    5. **Hierarchical Relationships**: Identify parent-child, master-detail, or categorization relationships
    6. **Cross-Domain Dependencies**: Find tables that bridge different business domains but are essential for complete answers

    **PHASE 3: RELATIONSHIP MAPPING**
    1. **Primary Relationships**: Identify direct foreign key relationships between relevant tables
    2. **Junction Tables**: Find many-to-many relationship tables that connect identified entities
    3. **Lookup Tables**: Identify reference/dimension tables needed for data enrichment
    4. **Audit/History Tables**: Consider tables storing historical changes or audit trails
    5. **Configuration Tables**: Include system configuration or business rule tables that affect data interpretation

    **PHASE 4: CONTEXTUAL REASONING**
    1. **Business Process Flow**: Consider the complete business process that the question touches
    2. **Data Lineage**: Think about where data originates and how it flows through the system
    3. **Reporting Dependencies**: Consider what additional context might be needed for meaningful results
    4. **Security/Permission Tables**: Include tables that might affect data visibility or access
    5. **Metadata Requirements**: Consider tables containing descriptions, categories, or classifications

    **CRITICAL INSTRUCTIONS:**
    - **EXHAUSTIVE COVERAGE**: Include ALL tables that have ANY reasonable possibility of relevance
    - **NO ASSUMPTIONS**: Do not exclude tables based on assumptions about query complexity
    - **PRECISE NAMING**: Use exact table_name/collection_name as they appear in the schema
    - **DEEP REASONING**: Look beyond obvious connections to find subtle but important relationships
    - **BUSINESS CONTEXT**: Consider the broader business scenario, not just technical data retrieval

    **DATABASE STRUCTURE:**
    ----SCHEMA_START----
    {{database_structure}}
    ----SCHEMA_END----

    **ADDITIONAL CONTEXT & BUSINESS RULES:**
    ----CONTEXT_START----
    {{CONTEXT}}
    ----CONTEXT_END----

    **USER QUESTION/REQUIREMENT:**
    ----QUESTION_START----
    {{interpreted_question}}
    ----QUESTION_END----

    **REQUIRED OUTPUT FORMAT:**

    Return a JSON array containing ONLY the table/collection names that are most relevant to the query. Follow these rules strictly:

    1. Return ONLY the JSON array, nothing else
    2. Use exact table/collection names from the schema
    3. Include only the top 3-5 most relevant tables
    4. No explanations, categories, or additional text
    5. No bullet points or formatting

    Example format:
    [
        "table_name_1",
        "table_name_2",
        "table_name_3"
    ]

    **CRITICAL:** 
    - Return ONLY the exact table/collection names as they appear in the schema
    - No bullet points, no explanations, no categories
    - One table name per line
    - Include ALL potentially relevant tables based on your comprehensive analysis
    - It's better to include extra tables than to miss essential ones

NOTE: RETURN A MINIMUM OF 2 AND MAXIMUM OF 5 TABLES (TOP  2-5) DEPENDING ON THE COM[LEXITY OF THE QUERY]

NOTE: DO NOT EVER FORMULATE TABLES, ONLY USE THE AVAILABLE TABLES, IF NO TABLES , RETURN AN EMPTY LIST PLEASE
    """