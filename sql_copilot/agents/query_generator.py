from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from typing import List, Dict
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from ..prompt import get_query_generator_prompt
from ..data import get_filtered_db

class QueryGeneratorOutput(BaseModel):
    generated_query: str
    mode: str
    has_analytics: bool
    plot_code: str
    filemodecols: List[str]
    is_account_statement: bool

TEMPERATURE = 0.00005
MODEL = "gpt-4o"

def query_generator(state):
    database_structure = get_filtered_db(state)
    print(database_structure)
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_query_generator_prompt()),
        MessagesPlaceholder(variable_name="messages")
    ])
    model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)
    model = model.with_structured_output(QueryGeneratorOutput)
    prompt = prompt.invoke({
        "messages": state["messages"],
        "CONTEXT": state["context"],
        "database_structure": state["database_structure"],
        "training_examples": state["training_examples"],
        "user_question": state["interpreted_question"]
    })
    
    response = model.invoke(prompt)

    return {
        "generated_query": response.generated_query,
        "mode": response.mode,
        "has_analytics": response.has_analytics,
        "plot_code": response.plot_code,
        "filemodecols": response.filemodecols,
        "is_account_statement": response.is_account_statement
    }
