import os
from dotenv import load_dotenv
import snowflake.connector



def query_executor(state):
    
    """
    Execute SQL query with AI-powered retry on failure
    Returns: (data, error_message, final_query)
    """

    if state["is_account_statement"]:
        schema = "TRANSFORMED"
    
    connection = snowflake.connector.connect(
    user=os.getenv('SNOWFLAKE_USER'),
    password=os.getenv('SNOWFLAKE_PASSWORD'),
    account=os.getenv('SNOWFLAKE_ACCOUNT'),  # Corrected from 'account' to 'account_identifier'
    role=os.getenv('ROLE'),
    database=os.getenv('DATABASE'),
    schema=os.getenv('SNOWFLAKE_SCHEMA'),  # Corrected from 'SNOW_FLAKE_SCHEMA' to 'SNOWFLAKE_SCHEMA'
    warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    session_parameters={'QUERY_TAG': 'py-copy-into'},)
    
    current_query = state["generated_query"]
    error_message = None
    data = None
    
    try:
            cursor = connection.cursor()
            result = cursor.execute(current_query)
            data = result.fetchall()

    except Exception as error:
            error_message = str(error)
    
    
    return {"data": data, "execution_error": error_message}
