import anthropic
import os

api_key = os.getenv("ANTHROPIC_API_KEY_2")
client = anthropic.Anthropic(api_key=api_key)

from dotenv import load_dotenv
import os
import json
import asyncio
from sql_copilot.training_samples import get_all_samples
from datetime import datetime
import pytz
from data.example_context import CONTEXT
import time
import random

# Get current date and time in UTC
utc_now = datetime.now(pytz.utc)
# Format the current date and time in words
current_date = utc_now.strftime("%A, %B %d, %Y at %I:%M %p UTC")



def get_query_generator_prompt_cache(training_examples,context):
    return f"""
     You are SQL-COPILOT SPECIFICALLY BUILT FOR AFRIEX DATABASE IN AN AGENTIC multi-agent system designed to process database queries. Your primary responsibily is to generate sql-query based on the full interpreted user question
    
    
    2. **Syntax Formatting:**
       - Enclose **column names**, **table names**, and **schema names** in double quotes (`"`).
       - Enclose **string values** in single quotes (`'`).
       - Ensure case sensitivity is respected as per <PERSON><PERSON>lake's rules.
       - Example:
         ```sql
         SELECT "columnName"
         FROM "schemaName"."tableName"
         WHERE "columnName" = 'value';
         ```

    3. **Query Restrictions:**
       - Only generate queries for data retrieval (SELECT statements). Avoid DELETE, UPDATE, or INSERT or acessing information like user passwords operations.
       - Validate that referenced fields exist in the database schema.

    4. **Database Structure:**
       - Carefully analyze the structure of the database, including schemas, tables, and columns.
       - Use JOIN operations if necessary to ensure accurate results.

    5 **Variant Data Handling:**
       - If querying variant fields, extract data correctly without creating non-existent columns.
       - Properly handle variant data types to ensure accurate querying.

    6. **Response Modes:**
       - **Direct Response Mode:** Provide a concise numerical or textual answer directly.
       - **File Mode:** Generate a file for large datasets. Include a message notifying users about potentially long processing times.
       - **Analytics Mode:** Generate plots using Python (pandas, matplotlib) when analysis is required. The function must always be named `plot_data`.

    7. **Output Format:**
       - Return a json format  with the following keys:
         - `mode`: `"direct"`, `"file"`, or `"analytics"` depending on the response type., Do not set to file mode when you are still asking questions please, follow striclly
         - `reason`: A reason for disallowance (empty string if allowed).
         - `generated_query`: The generated SQL query.
         - `filemodecols`: Column names for **both File Mode and Analytics Mode** outputs in correct order. These will be used to load the data into a dataframe.
         - `plot_code`: Python code for generating plots (only in analytics mode).
         is_account_statement: True or False: if question has to do with user account statement
    NOTE: Return a json fomartted output , nothing before or below
    7. **Additional Notes:**
       - Avoid SQL code blocks (```) in responses.
       - Always round numerical values to 2 decimal places unless specified otherwise.
       - Ensure the queried data matches the `filemodecols` for proper dataframe loading in both File Mode and Analytics Mode.
       - For time-related queries, use the current date as a reference and avoid assumptions about specific dates or periods.
       - Creatively generate queries by analyzing unique values and mixed data types (e.g., variant fields), adhering strictly to the provided database structure.
       - In analytics mode, keep plots simple, insightful, and free from unnecessary complexity.
       - Only use available tables; do not create new ones.

    8. **Plot Code Requirements (For Analytics Mode):**
       - Use pandas and matplotlib to process the dataframe and generate a plot.
       - Save the plot in the `static/plots/` directory with a unique filename using `uuid`.
       - Return the path to the saved plot file.
    9.
     - KINDLY LOOK THE EXAMPLES QUERY AND USE THEM WHEN NECESSAYR AS THEY ARE ACCURATE QUERY FOR THOSE QUESTIONS SEEN
    **Examples:**
    - **Direct Response Mode:** 
      User asks, "How many customers were paid today?" → Provide a direct numerical answer.
    - **File Mode:** 
      User requests a list of customers → Generate a file with appropriate column headers. Include `filemodecols` for dataframe compatibility.
    - **Analytics Mode:** 
      User asks for a trend analysis → Generate a plot using the `plot_data` function. Include `filemodecols` for dataframe compatibility.

    10:
    Esnure consistency in the response of your query especially if user is askng the same question , make sure you are consistent with your answer and sql query you generate
    **Final Note:**
    Ensure all queries are valid, efficient, and aligned with the user's request. Always prioritize clarity and accuracy in both queries and responses. Remember, `filemodecols` must be included for both **File Mode** and **Analytics Mode** to ensure proper dataframe generation.
    NOTE: YOU MAY BE INTEGRATED ON OTHER PLATFORMS LIKE SLACK ETC, IF THAT IS IT , YOU WILL BE PROVIDED WITH USER QUERIES HISTORY TO THE BOT  FROM OLDEST TO LATEST AND USER CURRENT QUESTION FOR HELPFUL CONVERSATIONAL FLOW WITHIN THE SAME THREAD OR CHAT PAGE
            IF PLATFORM IS SLACK : THE CONVERSATION HISTORY IS REFRERING TO CURRENT THREAD
    NOTE: ONLY SELECT FILE MODE WHEN NECESSARY , FOR EXAMPLE SOME DATA CAN BE EASILTY TO THE USER INSEAD OF SENDING FILE
    NOTE: WHEN DEALING WITH CATEGORICAL COLUMN, ONLY USE THE UNIQUE VALUES PRESENT IN THE DATABASE STRUCTURE PROVIDED, DO NOT FORMULATE VALUES, FOLLOW THIS STRITCLY
    
    MORE INFORMATION ABOUT THE DATABASE:
         <additional_info>
       {context}
         ----END--------
         </additional_info>



EXAMPLES OF QUESTION AND QUERY
<training_samples>
{training_examples}
</training_samples>

NOTE: QUESTION THAT HAS TO DO WITH GENERATING ACCOUNT STATEMENT FOR A SPECIFIC USE IS FILE MODE AND ALWAYS USE THE EXAMPLE PROVIDED FOR GENERATING ACCOUNT STATEMENT and only do this id user explicitly ask to generate account statement for a specific user email
NOTE: IF QUESTION REQUESTED FOR ACCOUNT STATEMENT , FORGET ABIOUT THE DATABASE STRUCTURE PASSED AND USE THE EXAMPLE QUERY FOR ACCOUNT STATEMENT , THAT ALWAYS WROKS

NOTE: THE EXAMPLE QUESTION AND CORRESPODING QUERY ARE VERY CORRECT AND WRITTENT BY THE DATABASE ADMINISTARTR , USE THEM TO LEARN , THEY ALWAYS WORK
NOTE VERY CRITICAL PLEASE: THE output formatting is very important as I will be loading it with json.loads. This is very critical, so make sure it is formatted properly and never return an error. AVOID ERRO LIKE Invalid control character at: line 4 column 39 (char 80)

RETURN THE FORMATTED JSON FORMAT WITH NOTHING BEFORE OR AFTER AND WELL FORMATTED, THIS IS CRTICAL !!
    """
# Define paths relative to the root

def exponential_backoff(func, max_retries=5):
    for i in range(max_retries):
        try:
            return func()
        except anthropic._exceptions.OverloadedError as e:
            if i == max_retries - 1:
                raise e
            wait_time = (2 ** i) + random.uniform(0, 1)  # Add jitter
            print(f"API overloaded. Retrying in {wait_time:.2f} seconds...")
            time.sleep(wait_time)


def query_generator_claude_cache(question,model,temperature,prompt,database,history=None):
    messages = [{"role": "user", "content": f"""DATABASE STRUCTURE :<database_structure>
{database}
</database_structure>"""},{"role": "user", "content": f"""CURRENT DATE (HELPFUL IN DATE RELATED QUERIES, USE THIS PLEASE):{current_date}"""}]
    if history:
        messages = messages +  history + [{"role": "user", "content": f"""USER QUESTION TO GENERATE QUERY FOR:

{question}"""}]
    else:
        messages =  messages + [{"role": "user", "content": f"""USER QUESTION TO GENERATE QUERY FOR:

{question}"""}]

    def make_request():
        return client.messages.create(
            model=model,
            max_tokens=1500,
            temperature=temperature,
            system=[
                {
                    "type": "text",
                    "text": f"{prompt}",
                    "cache_control": {"type": "ephemeral"}
                }
            ],
            messages=messages,
        )

    response = exponential_backoff(make_request)
    
    print(response.usage.model_dump_json())
    return json.loads(response.content[0].text) # Return the response for further processing
