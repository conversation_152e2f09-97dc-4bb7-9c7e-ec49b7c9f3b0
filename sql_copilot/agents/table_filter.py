
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from typing import List
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from ..prompt import table_filter_prompt


class TableFilterOuput(BaseModel):
    tables: List[str]

TEMPERATURE = 0.092
MODEL = "gpt-4o-mini"

def tables_filter(state):
   
   prompt = ChatPromptTemplate.from_messages([
        ("system", table_filter_prompt()),
        MessagesPlaceholder(variable_name="messages")
    ])
   
   model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)
   model = model.with_structured_output(TableFilterOuput)
   prompt = prompt.invoke({
            "messages": state["messages"],
            "CONTEXT": state["context"],
            "database_structure": state["database_structure"],  # Get database structure from state
            "interpreted_question": state["interpreted_question"]
        })
   
   response = model.invoke(prompt)

   return {"filtered_tables": response.tables}
