"""Describe your change

Revision ID: 99dc16a492a2
Revises: 21070d79871c
Create Date: 2025-07-12 01:21:56.604683

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '99dc16a492a2'
down_revision: Union[str, None] = '21070d79871c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messaging_connections', sa.Column('team_id', sa.String(length=50), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messaging_connections', 'team_id')
    # ### end Alembic commands ###
