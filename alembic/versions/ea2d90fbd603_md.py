"""md

Revision ID: ea2d90fbd603
Revises: 41cbc5cf01ab
Create Date: 2025-07-11 10:50:30.536067

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ea2d90fbd603'
down_revision: Union[str, None] = '41cbc5cf01ab'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('messaging_connections', 'db_connection_id',
               existing_type=mysql.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('messaging_connections', 'db_connection_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
