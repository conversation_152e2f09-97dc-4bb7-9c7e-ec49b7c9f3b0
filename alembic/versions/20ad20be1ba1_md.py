"""md

Revision ID: 20ad20be1ba1
Revises: ea2d90fbd603
Create Date: 2025-07-11 11:27:22.030739

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '20ad20be1ba1'
down_revision: Union[str, None] = 'ea2d90fbd603'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messaging_connections', sa.Column('status', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messaging_connections', 'status')
    # ### end Alembic commands ###
