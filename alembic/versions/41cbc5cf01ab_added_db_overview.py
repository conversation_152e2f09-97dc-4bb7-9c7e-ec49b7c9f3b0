"""added db overview

Revision ID: 41cbc5cf01ab
Revises: dad740a4e593
Create Date: 2025-07-08 13:26:14.908893

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '41cbc5cf01ab'
down_revision: Union[str, None] = 'dad740a4e593'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connections', sa.Column('db_overview', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('connections', 'db_overview')
    # ### end Alembic commands ###
