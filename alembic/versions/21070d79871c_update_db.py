"""update db

Revision ID: 21070d79871c
Revises: 20ad20be1ba1
Create Date: 2025-07-11 12:43:58.035929

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '21070d79871c'
down_revision: Union[str, None] = '20ad20be1ba1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connections', sa.Column('db_overview', sa.Text(), nullable=True))
    op.add_column('messaging_connections', sa.Column('status', sa.String(length=20), nullable=True))
    op.alter_column('messaging_connections', 'db_connection_id',
               existing_type=mysql.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('messaging_connections', 'db_connection_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.drop_column('messaging_connections', 'status')
    op.drop_column('connections', 'db_overview')
    # ### end Alembic commands ###
