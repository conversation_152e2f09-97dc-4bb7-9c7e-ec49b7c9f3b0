"""Describe your change

Revision ID: 6f694c360d68
Revises: 99dc16a492a2
Create Date: 2025-07-12 21:27:29.536256

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6f694c360d68'
down_revision: Union[str, None] = '99dc16a492a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('messaging_connections_ibfk_2', 'messaging_connections', type_='foreignkey')
    op.create_foreign_key(None, 'messaging_connections', 'connections', ['db_connection_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messaging_connections', type_='foreignkey')
    op.create_foreign_key('messaging_connections_ibfk_2', 'messaging_connections', 'connections', ['db_connection_id'], ['id'])
    # ### end Alembic commands ###
