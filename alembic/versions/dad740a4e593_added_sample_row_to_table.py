"""added sample row to table

Revision ID: dad740a4e593
Revises: 9a62db3aaadd
Create Date: 2025-06-29 22:09:15.781978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dad740a4e593'
down_revision: Union[str, None] = '9a62db3aaadd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('connection_tables', sa.Column('sample_row', sa.Text(), nullable=True))
    op.add_column('connections', sa.Column('status', sa.String(length=20), nullable=True))
    op.add_column('fields', sa.Column('is_categorical', sa.<PERSON>(), nullable=True))
    op.add_column('fields', sa.Column('is_datetime', sa.<PERSON>(), nullable=True))
    op.add_column('fields', sa.Column('categorical_values', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fields', 'categorical_values')
    op.drop_column('fields', 'is_datetime')
    op.drop_column('fields', 'is_categorical')
    op.drop_column('connections', 'status')
    op.drop_column('connection_tables', 'sample_row')
    # ### end Alembic commands ###
