#!/usr/bin/env python3
"""
Test script to verify that the database-integrated query generator works correctly.
This script uses a real connection_id to fetch database information.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import SqlCopilot
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_database_integration():
    """Test that the query generator works with database integration"""

    # Use a real connection_id from your database
    connection_id = 23  # Using connection_id 23 as requested

    print("Testing database integration...")
    print(f"Using connection_id: {connection_id}")
    print("-" * 50)

    try:
        # Import database utilities to fetch connection info
        from app.databse import get_db_session
        from app.core.db_utils import get_active_tables_with_fields_and_overview
        from app.core.crypto_utils import decrypt_data
        import json

        # Fetch database info using connection_id (like QueryHandler does)
        db = get_active_tables_with_fields_and_overview(get_db_session(), connection_id)
        if db is None:
            raise ValueError("Could not load database info for the given connection_id.")

        # Decrypt connection params
        connection_params = json.loads(decrypt_data(db["connection_params"]))

        # Process schemas like QueryHandler does
        schemas = json.loads(db["schemas"])
        active_tables = db.get("tables") or db.get("collections")
        active_table_names = {t["table_name"] for t in active_tables} if active_tables else set()

        tables_overview = []
        for schema in schemas:
            overview = schema.get("tables_overview") or schema.get("collections_overview")
            if overview:
                tables_overview.extend(overview)

        if tables_overview and active_table_names:
            filtered_tables_overview = [
                t for t in tables_overview
                if t.get("table_name") in active_table_names or t.get("collection_name") in active_table_names
            ]
        else:
            filtered_tables_overview = []

        db["schemas"] = filtered_tables_overview

        # Initialize SqlCopilot with database info (but tools will fetch connection_params internally)
        copilot = SqlCopilot(
            provider=db["provider"],
            connection_params=connection_params,  # This is for SqlCopilot init, tools will fetch internally
            database_structure=db,
            query="How many users do we have in total?",
            connection_id=connection_id,
            company_name="Test Company",
            db_type=db["db_type"]
        )
        
        print("✅ SqlCopilot initialized successfully with database integration!")
        print(f"Provider: {copilot.provider}")
        print(f"DB Type: {copilot.db_type}")
        print(f"Database structure loaded: {len(copilot.database_structure.get('tables', []))} tables")
        print("-" * 50)
        
        # Invoke the workflow
        print("Invoking the workflow...")
        result = copilot.invoke()
        
        print("Workflow completed!")
        print("-" * 50)
        
        # Check if we got a generated query
        if result.get("generated_query"):
            print("✅ SUCCESS: Generated query extracted successfully!")
            print(f"Generated Query: {result['generated_query']}")
        else:
            print("❌ ISSUE: No generated query found in result")
            print("Available keys in result:", list(result.keys()))
        
        # Print execution results if available
        if result.get("execution_results"):
            print(f"Execution Results: {result['execution_results']}")
        
        # Print any errors
        if result.get("execution_error"):
            print(f"Execution Error: {result['execution_error']}")
            
        return result
        
    except ValueError as e:
        if "Could not load database info" in str(e):
            print(f"❌ ERROR: Invalid connection_id ({connection_id})")
            print("Please make sure you're using a valid connection_id from your database.")
            print("You can check available connection_ids in your database's 'connections' table.")
        else:
            print(f"❌ ERROR: {str(e)}")
        return None
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_different_queries():
    """Test with different types of queries"""
    
    connection_id = 23  # Using connection_id 23 as requested
    
    test_queries = [
        "How many users signed up today?",
        "Show me the top 10 users by transaction count",
        "What is the total revenue this month?",
        "How many active connections do we have?"
    ]
    
    print("\n" + "="*60)
    print("TESTING DIFFERENT QUERIES")
    print("="*60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test {i}: {query} ---")
        
        try:
            copilot = SqlCopilot(
                connection_id=connection_id,
                query=query,
                company_name="Test Company"
            )
            
            result = copilot.invoke()
            
            if result.get("generated_query"):
                print(f"✅ Query generated: {result['generated_query'][:100]}...")
            else:
                print("❌ No query generated")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("="*60)
    print("DATABASE INTEGRATION TEST")
    print("="*60)
    
    # Test basic functionality
    result = test_database_integration()
    
    # Test different queries if the basic test works
    if result is not None:
        test_different_queries()
    
    print("\n" + "="*60)
    print("TEST COMPLETED")
    print("="*60)
