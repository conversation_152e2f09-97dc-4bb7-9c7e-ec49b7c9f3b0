<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signup - index</title>
    <link rel="stylesheet" href="/static/css/signup.css">
</head>
<body>
    <div class="header-nav">
        <div class="logo">index</div>
        <div class="nav-buttons">
            <a href="#" class="nav-link">Features</a>
            <a href="#" class="nav-link">Pricing</a>
            <a href="#" class="nav-link">FAQ</a>
            <a href="#" class="nav-link">Contact</a>
            <a href="/login" class="nav-link">Log in</a>
            <a href="/signup" class="register-btn">Sign up</a>
        </div>
    </div>

    <div class="main-content">
        <div class="signup-container">
            <div class="signup-header">
                <h1 class="signup-title">Register</h1>
                <p class="signup-subtitle">Create a new account to get started.</p>
            </div>

            <form class="signup-form" action="/signup" method="POST">
                <div class="error-message" id="error-message" style="display: none;">
                </div>
                
                <div class="success-message" id="success-message" style="display: none;">
                </div>
                
                <div class="form-group">
                    <label class="form-label">First Name</label>
                    <input 
                        type="text" 
                        name="first_name" 
                        class="form-input" 
                        placeholder="Enter your first name"
                        required
                    >
                </div>

                <div class="form-group">
                    <label class="form-label">Last Name</label>
                    <input 
                        type="text" 
                        name="last_name" 
                        class="form-input" 
                        placeholder="Enter your last name"
                        required
                    >
                </div>

                <div class="form-group">
                    <label class="form-label">Organization Name</label>
                    <input 
                        type="text" 
                        name="organization_name" 
                        class="form-input" 
                        placeholder="Enter your organization name"
                        required
                    >
                </div>

                <div class="form-group">
                    <label class="form-label">Organization Email</label>
                    <input 
                        type="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="Enter your organization email"
                        required
                    >
                </div>

                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input 
                        type="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="Create a password"
                        required
                    >
                </div>

                <div class="form-group">
                    <label class="form-label">Confirm Password</label>
                    <input 
                        type="password" 
                        name="confirm_password" 
                        class="form-input" 
                        placeholder="Confirm your password"
                        required
                    >
                </div>

                <button type="submit" class="signup-btn">
                    Register
                </button>

                <div class="login-link">
                    Already have an account? <a href="/login">Login</a>
                </div>
            </form>
        </div>
    </div>

    <!-- OTP Modal -->
    <div id="otp-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.3); z-index:1000; align-items:center; justify-content:center;">
        <div style="background:white; border-radius:12px; max-width:350px; width:100%; padding:32px; box-shadow:0 4px 24px rgba(0,0,0,0.15); margin:auto;">
            <h2 style="font-size:22px; font-weight:600; margin-bottom:8px;">Verify Your Email</h2>
            <p id="otp-modal-subtitle" style="color:#64748b; font-size:14px; margin-bottom:18px;">We've sent a verification code to your email. Please enter it below to continue.</p>
            <div class="error-message" id="otp-error-message" style="display:none;"></div>
            <div class="success-message" id="otp-success-message" style="display:none;"></div>
            <input type="text" id="otp-code-input" class="form-input" placeholder="Enter OTP code" maxlength="6" style="text-align:center; letter-spacing:2px; margin-bottom:16px;" required />
            <button id="verify-otp-btn" class="signup-btn" style="margin-bottom:10px;">Verify OTP</button>
            <div style="text-align:center; margin-bottom:10px;">
                <span style="color:#64748b; font-size:14px;">Didn't receive a code?</span>
                <a href="#" id="resend-otp-link" style="color:#3b82f6; font-weight:500; margin-left:4px; cursor:pointer;">Resend</a>
            </div>
            <button id="close-otp-modal" style="background:none; border:none; color:#64748b; font-size:14px; cursor:pointer; display:block; margin:auto;">Cancel</button>
        </div>
    </div>

    <script>
        document.querySelector('.signup-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.signup-btn');
            const originalText = submitBtn.textContent;
            const errorDiv = document.getElementById('error-message');
            const successDiv = document.getElementById('success-message');
            
            // Hide previous messages
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            // Check if passwords match
            if (formData.get('password') !== formData.get('confirm_password')) {
                errorDiv.textContent = 'Passwords do not match';
                errorDiv.style.display = 'block';
                return;
            }
            
            try {
                submitBtn.textContent = 'Registering...';
                submitBtn.disabled = true;
                
                const response = await fetch('/api/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        full_name: formData.get('first_name') + ' ' + formData.get('last_name'),
                        email: formData.get('email'),
                        password: formData.get('password'),
                        organization_name: formData.get('organization_name')
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Show OTP modal instead of redirect
                    document.getElementById('otp-modal').style.display = 'flex';
                    document.getElementById('otp-modal-subtitle').textContent = `We've sent a verification code to ${formData.get('email')}. Please enter it below to continue.`;
                    window.pendingSignupEmail = formData.get('email');
                    window.pendingSignupPassword = formData.get('password');
                } else {
                    errorDiv.textContent = result.detail || 'Registration failed. Please try again.';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred. Please try again.';
                errorDiv.style.display = 'block';
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // OTP Modal JS
        const otpModal = document.getElementById('otp-modal');
        const otpErrorDiv = document.getElementById('otp-error-message');
        const otpSuccessDiv = document.getElementById('otp-success-message');
        const otpCodeInput = document.getElementById('otp-code-input');
        const verifyOtpBtn = document.getElementById('verify-otp-btn');
        const resendOtpLink = document.getElementById('resend-otp-link');
        const closeOtpModalBtn = document.getElementById('close-otp-modal');

        verifyOtpBtn.addEventListener('click', async function() {
            otpErrorDiv.style.display = 'none';
            otpSuccessDiv.style.display = 'none';
            const code = otpCodeInput.value.trim();
            if (!code || code.length !== 6) {
                otpErrorDiv.textContent = 'Please enter the 6-digit OTP code.';
                otpErrorDiv.style.display = 'block';
                return;
            }
            verifyOtpBtn.textContent = 'Verifying...';
            verifyOtpBtn.disabled = true;
            try {
                const response = await fetch('/api/auth/verify-otp', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: window.pendingSignupEmail, otp_code: code })
                });
                const result = await response.json();
                if (response.ok) {
                    // OTP verified, now log in the user
                    const loginResponse = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ email: window.pendingSignupEmail, password: window.pendingSignupPassword })
                    });
                    const loginResult = await loginResponse.json();
                    if (loginResponse.ok) {
                        document.cookie = `access_token=${loginResult.access_token}; path=/; max-age=86400; SameSite=Strict`;
                        otpSuccessDiv.textContent = 'OTP verified! Redirecting...';
                        otpSuccessDiv.style.display = 'block';
                        setTimeout(() => { window.location.href = '/dashboard'; }, 1200);
                    } else {
                        otpErrorDiv.textContent = 'OTP verified, but login failed. Please log in manually.';
                        otpErrorDiv.style.display = 'block';
                    }
                } else {
                    otpErrorDiv.textContent = result.detail || 'Invalid OTP code. Please try again.';
                    otpErrorDiv.style.display = 'block';
                }
            } catch (error) {
                otpErrorDiv.textContent = 'An error occurred. Please try again.';
                otpErrorDiv.style.display = 'block';
            } finally {
                verifyOtpBtn.textContent = 'Verify OTP';
                verifyOtpBtn.disabled = false;
            }
        });

        resendOtpLink.addEventListener('click', async function(e) {
            e.preventDefault();
            otpErrorDiv.style.display = 'none';
            otpSuccessDiv.style.display = 'none';
            try {
                const response = await fetch('/api/auth/resend-otp', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: window.pendingSignupEmail })
                });
                const result = await response.json();
                if (response.ok) {
                    otpSuccessDiv.textContent = 'OTP resent! Check your email.';
                    otpSuccessDiv.style.display = 'block';
                } else {
                    otpErrorDiv.textContent = result.detail || 'Failed to resend OTP.';
                    otpErrorDiv.style.display = 'block';
                }
            } catch (error) {
                otpErrorDiv.textContent = 'An error occurred. Please try again.';
                otpErrorDiv.style.display = 'block';
            }
        });

        closeOtpModalBtn.addEventListener('click', function() {
            otpModal.style.display = 'none';
        });
    </script>
</body>
</html>