<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - index</title>
    <link rel="stylesheet" href="/static/css/connections.css">
    <link rel="stylesheet" href="/static/css/connection_view_light.css">
    <style>
        .sidebar-footer {
            border-top: 1px solid #e5e7eb;
            padding-top: 18px;
            margin-top: 24px;
        }
        .logout-btn {
            width: 100%;
            padding: 12px 0;
            background: #ef4444;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 18px;
            box-shadow: 0 2px 8px rgba(239,68,68,0.08);
            transition: background 0.2s;
        }
        .logout-btn:hover {
            background: #dc2626;
        }
        .settings-container {
            max-width: 440px;
            margin: 0 !important;
            padding-top: 0 !important;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.07);
            padding: 40px 32px 32px 32px;
        }
        .settings-header {
            text-align: center;
            margin-bottom: 24px;
        }
        .settings-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 6px;
        }
        .settings-subtitle {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 14px;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .form-input::placeholder {
            color: #9ca3af;
        }
        .signup-btn {
            width: 100%;
            padding: 12px;
            background: #1a202c;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 24px;
        }
        .signup-btn:hover {
            background: #2d3748;
        }
        .signup-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .error-message {
            background: #fef2f2;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #fecaca;
        }
        .success-message {
            background: #f0fdf4;
            color: #16a34a;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #bbf7d0;
        }
        .main-content {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            height: 100vh;
        }
        .content-area {
            flex: 1;
            display: block;
            padding: 0 !important;
            margin: 0 !important;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon"></div>
                index
            </div>
        </div>
        <nav class="sidebar-nav">
            <a href="/dashboard" class="nav-item">
                <div class="nav-icon">📊</div>
                Dashboard
            </a>
            <a href="/connections" class="nav-item">
                <div class="nav-icon">🔗</div>
                Connections
            </a>
            <a href="/settings" class="nav-item active">
                <div class="nav-icon">⚙️</div>
                Settings
            </a>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">{{ user.full_name[0]|upper if user.full_name else 'U' }}</div>
                <div class="user-info">
                    <div class="user-name">{{ user.full_name or 'User' }}</div>
                    <div class="company-name">{{ user.organization_name or '' }}</div>
                </div>
            </div>
            <form method="post" action="/logout">
                <button type="submit" class="logout-btn">Log out</button>
            </form>
        </div>
    </div>
    <div class="main-content light-mode">
        <div class="content-area">
            <div class="settings-container">
                <div class="settings-header">
                    <h1 class="settings-title">Account Settings</h1>
                    <p class="settings-subtitle">Update your account and organization details below.</p>
                </div>
                <div class="error-message" id="error-message" style="display: {% if error %}block{% else %}none{% endif %};">
                    {{ error }}
                </div>
                <div class="success-message" id="success-message" style="display: {% if success %}block{% else %}none{% endif %};">
                    {{ success }}
                </div>
                <form method="post" action="/settings" class="signup-form">
                    <div class="form-group">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" id="full_name" name="full_name" class="form-input" value="{{ user.full_name }}" required>
                    </div>
                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" id="email" name="email" class="form-input" value="{{ user.email }}" required>
                    </div>
                    <div class="form-group">
                        <label for="organization_name" class="form-label">Organization Name</label>
                        <input type="text" id="organization_name" name="organization_name" class="form-input" value="{{ user.organization_name }}" required>
                    </div>
                    <div class="form-group">
                        <label for="organization_description" class="form-label">Organization Description</label>
                        <input type="text" id="organization_description" name="organization_description" class="form-input" value="{{ user.organization_description or '' }}">
                    </div>
                    <button type="submit" class="signup-btn" style="margin-top:12px;">Update</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html> 