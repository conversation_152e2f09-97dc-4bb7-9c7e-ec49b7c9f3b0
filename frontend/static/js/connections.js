// Tab switching functionality
const tabs = document.querySelectorAll('.tab');
tabs.forEach(tab => {
    tab.addEventListener('click', (e) => {
        e.preventDefault();
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove('active'));
        // Add active class to clicked tab
        tab.classList.add('active');
        // Here you could add logic to show/hide different connection types
        // For now, we'll just handle the visual state
    });
});

// --- Patch: Track connected platforms and connection state ---
let connectedPlatforms = new Set();
let connectedIds = {};

// --- Card click handler (robust) ---
const connectionCards = document.querySelectorAll('.connection-card');
connectionCards.forEach(card => {
    card.addEventListener('click', (e) => {
        // Don't trigger if clicking on any button or inside a button
        if (e.target.closest('.connection-button')) return;
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        const button = card.querySelector('.connection-button');
        // Only show alert if the button says 'Coming Soon' or 'Connect' and not connected
        if (button && (button.textContent.includes('Coming Soon') || button.textContent.includes('Connect'))) {
            if (button.textContent.includes('Connect')) {
                // Optionally, open modal for connect (handled by button), or do nothing
            }
        }
        // If connected, do nothing (button handles redirect)
    });
});

// --- Button click handler (robust) ---
const connectionButtons = document.querySelectorAll('.connection-button');
connectionButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const card = button.closest('.connection-card');
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        // Prevent opening connect modal if already connected (View button)
        if (button.textContent === 'View') {
            // Redirect to connection view page (if implemented)
            const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
            let platform = name;
            if (platform === 'postgresql') platform = 'postgres';
            const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
            if (connectionObjs.length > 0) {
                // Redirect to connection view page for the first connection
                window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
            }
            return;
        }
        if (button.textContent.includes('Connect')) {
            if (connectionName === 'Snowflake') {
                // Open the modal directly for Snowflake
                snowflakeModal.style.display = 'flex';
                snowflakeStatus.textContent = '';
                schemasSection.style.display = 'none';
                generateSchemaBtn.style.display = 'none';
            } else {
                // Here you would typically open a modal or redirect to configuration page for other DBs
            }
        } else if (button.textContent.includes('Coming Soon')) {
            alert(`${connectionName} coming soon!`);
        }
    });
});

// Snowflake Modal Logic
const snowflakeModal = document.getElementById('snowflake-modal');
const closeSnowflakeModal = document.getElementById('close-snowflake-modal');
const snowflakeForm = document.getElementById('snowflake-form');
const snowflakeStatus = document.getElementById('snowflake-status');
const connectBtn = document.getElementById('connect-snowflake-btn');
const schemasSection = document.getElementById('schemas-section');
const generateSchemaBtn = document.getElementById('generate-schema-btn');
const submitSnowflakeBtn = document.getElementById('submit-snowflake-btn');
const logModal = document.getElementById('log-modal');
const closeLogModal = document.getElementById('close-log-modal');
const terminalLog = document.getElementById('terminal-log');

let snowflakeFormData = null;
let availableSchemas = [];

// --- WebSocket Progress UI ---
let ws = null;
let progressArea = null;
let reportArea = null;

function setupProgressUI() {
    // Create or get progress area
    progressArea = document.getElementById('schema-progress-area');
    if (!progressArea) {
        progressArea = document.createElement('div');
        progressArea.id = 'schema-progress-area';
        progressArea.style = 'margin-top:20px; background:#f3f4f6; border-radius:8px; padding:12px; font-size:14px; max-height:180px; overflow-y:auto;';
        snowflakeForm.parentNode.insertBefore(progressArea, snowflakeForm.nextSibling);
    }
    progressArea.innerHTML = '';
    // Create or get report area
    reportArea = document.getElementById('schema-report-area');
    if (!reportArea) {
        reportArea = document.createElement('div');
        reportArea.id = 'schema-report-area';
        reportArea.style = 'margin-top:16px; background:#e0f2fe; border-radius:8px; padding:12px; font-size:14px; display:none;';
        progressArea.parentNode.insertBefore(reportArea, progressArea.nextSibling);
    }
    reportArea.innerHTML = '';
    reportArea.style.display = 'none';
}

function appendProgressMessage(msg) {
    if (!progressArea) return;
    const div = document.createElement('div');
    div.textContent = msg;
    progressArea.appendChild(div);
    progressArea.scrollTop = progressArea.scrollHeight;
}

function showFinalReport(report) {
    if (!reportArea) return;
    reportArea.style.display = 'block';
    reportArea.innerHTML = '<b>Schema Generation Report:</b><br>' +
        '<pre style="white-space:pre-wrap; word-break:break-word;">' + JSON.stringify(report, null, 2) + '</pre>';
}

function showCompletionUI(report) {
    // Update modal title
    const modalTitle = document.querySelector('#log-modal h2');
    if (modalTitle) {
        modalTitle.textContent = '🎉 Success!';
        modalTitle.style.color = '#10b981';
    }
    
    // Clear the terminal log and show completion UI
    terminalLog.innerHTML = '';
    
    // Create completion container
    const completionContainer = document.createElement('div');
    completionContainer.style = 'display:flex; flex-direction:column; align-items:center; justify-content:center; height:100%; padding:20px; text-align:center;';
    
    // Create animated success circle
    const successCircle = document.createElement('div');
    successCircle.style = `
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #10b981, #059669);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        animation: successPulse 0.6s ease-out;
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
    `;
    
    // Create checkmark
    const checkmark = document.createElement('div');
    checkmark.innerHTML = '✓';
    checkmark.style = `
        color: white;
        font-size: 48px;
        font-weight: bold;
        animation: checkmarkAppear 0.4s ease-out 0.3s both;
    `;
    
    successCircle.appendChild(checkmark);
    
    // Create success message
    const successMessage = document.createElement('h2');
    successMessage.textContent = 'Schema Generation Complete!';
    successMessage.style = `
        color: #10b981;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
        animation: fadeInUp 0.5s ease-out 0.4s both;
    `;
    
    // Create report summary
    const reportSummary = document.createElement('div');
    reportSummary.style = `
        background: #f8fafc;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        border: 1px solid #e2e8f0;
        width: 100%;
        max-width: 500px;
        animation: fadeInUp 0.5s ease-out 0.5s both;
    `;
    
    // Format report data
    const totalDatabases = report.total_databases || 0;
    const totalTables = report.total_tables_found || 0;
    const successfulTables = report.successfully_analyzed || 0;
    const failedTables = report.failed_analysis || 0;
    const executionTime = report.execution_time || 0;
    
    reportSummary.innerHTML = `
        <h3 style="color: #1f2937; font-size: 18px; font-weight: 600; margin-bottom: 16px; text-align: center;">Analysis Summary</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; text-align: left;">
            <div style="background: white; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                <div style="color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Databases</div>
                <div style="color: #1f2937; font-size: 24px; font-weight: 700;">${totalDatabases}</div>
            </div>
            <div style="background: white; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                <div style="color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Tables Found</div>
                <div style="color: #1f2937; font-size: 24px; font-weight: 700;">${totalTables}</div>
            </div>
            <div style="background: white; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                <div style="color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Successfully Analyzed</div>
                <div style="color: #10b981; font-size: 24px; font-weight: 700;">${successfulTables}</div>
            </div>
            <div style="background: white; padding: 12px; border-radius: 8px; border: 1px solid #e5e7eb;">
                <div style="color: #6b7280; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">Execution Time</div>
                <div style="color: #1f2937; font-size: 24px; font-weight: 700;">${executionTime}s</div>
            </div>
        </div>
        ${failedTables > 0 ? `<div style="margin-top: 12px; padding: 8px 12px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; color: #dc2626; font-size: 14px;">⚠️ ${failedTables} table(s) failed analysis</div>` : ''}
    `;
    
    // Create close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Continue to Dashboard';
    closeButton.style = `
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
        border: none;
        padding: 12px 32px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        animation: fadeInUp 0.5s ease-out 0.6s both;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    `;
    
    closeButton.addEventListener('mouseenter', () => {
        closeButton.style.transform = 'translateY(-2px)';
        closeButton.style.boxShadow = '0 6px 20px rgba(99, 102, 241, 0.4)';
    });
    
    closeButton.addEventListener('mouseleave', () => {
        closeButton.style.transform = 'translateY(0)';
        closeButton.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.3)';
    });
    
    closeButton.addEventListener('click', () => {
        logModal.style.display = 'none';
        window.location.reload();
    });
    
    // Assemble the completion UI
    completionContainer.appendChild(successCircle);
    completionContainer.appendChild(successMessage);
    completionContainer.appendChild(reportSummary);
    completionContainer.appendChild(closeButton);
    
    // Replace terminal content with completion UI
    terminalLog.appendChild(completionContainer);
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes successPulse {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        @keyframes checkmarkAppear {
            0% { transform: scale(0) rotate(45deg); opacity: 0; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        
        @keyframes fadeInUp {
            0% { transform: translateY(20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
}

closeSnowflakeModal.addEventListener('click', () => {
    snowflakeModal.style.display = 'none';
});
snowflakeModal.addEventListener('click', (e) => {
    if (e.target === snowflakeModal) snowflakeModal.style.display = 'none';
});

// On form submit: validate, test connection, fetch schemas
snowflakeForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    if (connectBtn.style.display === 'none') return; // Only handle connect step here
    snowflakeStatus.textContent = '';
    schemasSection.style.display = 'none';
    connectBtn.disabled = true;
    connectBtn.querySelector('.button-spinner').style.display = 'inline-block';
    // Validate fields
    const formData = new FormData(snowflakeForm);
    const requiredFields = ['account','user','password','role','warehouse','database'];
    for (const field of requiredFields) {
        if (!formData.get(field)) {
            snowflakeStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in all required fields.</span>`;
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
    }
    snowflakeStatus.textContent = 'Connecting...';
    snowflakeFormData = {
        backend: 'snowflake',
        connection_params: {
            account: formData.get('account'),
            user: formData.get('user'),
            password: formData.get('password'),
            role: formData.get('role'),
            warehouse: formData.get('warehouse'),
            database: formData.get('database')
        },
        optional: {}
    };
    try {
        // 1. Test connection
        const testRes = await fetch('/api/v1/db-schema/test-connection', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(snowflakeFormData)
        });
        const testResult = await testRes.json();
        if (!testRes.ok || !testResult.success) {
            snowflakeStatus.innerHTML = '<span style="color:#dc2626;">Invalid credentials. Please check your Snowflake details.</span>' +
                (testResult.error_detail ? `<div style="color:#6b7280; font-size:12px; margin-top:4px;">${testResult.error_detail}</div>` : '');
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        snowflakeStatus.textContent = 'Connection successful! Fetching schemas...';
        // 2. Fetch schemas
        const schemaRes = await fetch('/api/v1/db-schema/list-schemas', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(snowflakeFormData)
        });
        const schemaResult = await schemaRes.json();
        if (!schemaRes.ok || !schemaResult.schemas) {
            snowflakeStatus.textContent = 'Failed to fetch schemas: ' + (schemaResult.detail || 'Unknown error');
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        availableSchemas = schemaResult.schemas;
        if (availableSchemas.length === 0) {
            snowflakeStatus.textContent = 'No schemas found.';
            connectBtn.disabled = false;
            connectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        // 3. Show checkboxes
        schemasSection.innerHTML = '<label style="font-weight:600; color:#374151;">Select schemas to analyze:</label><div style="margin-top:8px; display:flex; flex-wrap:wrap; gap:10px;">' +
            availableSchemas.map(s => `<label style=\"background:#f3f4f6; border-radius:6px; padding:6px 12px; margin-bottom:4px; display:flex; align-items:center; gap:6px; cursor:pointer;\"><input type=\"checkbox\" name=\"selected_schemas\" value=\"${s}\" style=\"margin-right:6px;\">${s}</label>`).join('') + '</div>';
        schemasSection.style.display = 'block';
        connectBtn.style.display = 'none';
        submitSnowflakeBtn.style.display = 'inline-block';
        snowflakeStatus.textContent = 'Select schemas and click Submit.';
    } catch (err) {
        snowflakeStatus.textContent = 'Network error: ' + err.message;
        connectBtn.disabled = false;
        connectBtn.querySelector('.button-spinner').style.display = 'none';
    }
    connectBtn.disabled = false;
    connectBtn.querySelector('.button-spinner').style.display = 'none';
});

// Enable/disable Submit button based on selection
schemasSection.addEventListener('change', (e) => {
    const checked = schemasSection.querySelectorAll('input[type=\"checkbox\"]:checked');
    submitSnowflakeBtn.disabled = checked.length === 0;
});

// On submit, start schema generation and open log modal
submitSnowflakeBtn.addEventListener('click', async (e) => {
    e.preventDefault();
    // Get selected schemas
    const selected = Array.from(schemasSection.querySelectorAll('input[type=\"checkbox\"]:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        snowflakeStatus.textContent = 'Please select at least one schema.';
        return;
    }
    snowflakeStatus.textContent = 'Starting schema generation...';
    // Open log modal and clear logs
    logModal.style.display = 'flex';
    terminalLog.textContent = '';
    // Send to generate endpoint
    const data = {
        ...snowflakeFormData,
        optional: { schemas: selected }
    };
    try {
        const response = await fetch('/api/v1/db-schema/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (response.ok) {
            snowflakeStatus.textContent = 'Schema generation started! Request ID: ' + result.request_id;
            if (ws) { ws.close(); }
            ws = new WebSocket(`ws://${window.location.host}/ws/schema-progress/${result.request_id}`);
            ws.onmessage = (event) => {
                try {
                    const msg = event.data;
                    let parsed = null;
                    try { parsed = JSON.parse(msg); } catch {}
                    if (parsed && parsed.type === 'heartbeat') {
                        // Ignore heartbeat messages
                        return;
                    }
                    if (parsed && parsed.type === 'final_report') {
                        showCompletionUI(parsed.report);
                        ws.close(); // Close the WebSocket after final report
                    } else {
                        terminalLog.innerHTML += '<div>' + (parsed && parsed.message ? parsed.message : msg) + '</div>';
                    }
                } catch (err) {
                    terminalLog.innerHTML += '<div>' + event.data + '</div>';
                }
                terminalLog.scrollTop = terminalLog.scrollHeight;
            };
            ws.onclose = () => {
                terminalLog.innerHTML += '<div style="color:#f87171;">WebSocket connection closed.</div>';
            };
        } else {
            snowflakeStatus.textContent = 'Error: ' + (result.detail || result.message || 'Unknown error');
        }
    } catch (err) {
        snowflakeStatus.textContent = 'Network error: ' + err.message;
    }
});

closeLogModal.addEventListener('click', () => {
    logModal.style.display = 'none';
    if (ws) ws.close();
});
logModal.addEventListener('click', (e) => {
    if (e.target === logModal) logModal.style.display = 'none';
    if (ws) ws.close();
});

(async function() {
    // Fetch connected databases
    let connected = [];
    try {
        const res = await fetch('/api/v1/user-connections');
        if (res.ok) {
            connected = await res.json();
        }
    } catch (e) {
        console.error('Failed to fetch user connections', e);
    }

    // Map platforms for easy lookup
    connectedPlatforms = new Set(connected.map(c => c.database_platform.toLowerCase()));
    connectedIds = {};
    connected.forEach(c => {
        connectedIds[c.database_platform.toLowerCase()] = c.id;
    });

    // Update each card/button
    document.querySelectorAll('.connection-card').forEach(card => {
        const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
        let platform = name;
        if (platform === 'postgresql') platform = 'postgres'; // match backend naming
        // Find all connection objects for this platform
        const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
        const isConnected = connectionObjs.length > 0;
        const cardOptions = card.querySelector('.card-options');
        const optionsBtn = card.querySelector('.options-btn');
        const optionsDropdown = card.querySelector('.options-dropdown');
        const dropdownDisconnect = card.querySelector('.dropdown-disconnect');
        const connectBtn = card.querySelector('.connection-button');
        // Add or update Connected label
        let connectedLabel = card.querySelector('.connected-label');
        if (!connectedLabel) {
            connectedLabel = document.createElement('div');
            connectedLabel.className = 'connected-label';
            connectedLabel.style = 'color:#22c55e; font-weight:600; margin-top:8px; font-size:15px;';
            card.insertBefore(connectedLabel, connectBtn.nextSibling);
        }
        if (isConnected) {
            // Show options icon for connected
            if (cardOptions) cardOptions.style.display = 'inline-block';
            if (connectBtn) {
                connectBtn.textContent = 'View';
                connectBtn.classList.remove('primary');
                connectBtn.classList.add('secondary');
                connectBtn.style.display = 'inline-flex';
                connectBtn.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // Always redirect to connection view page for the first connection
                    window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
                };
            }
            connectedLabel.textContent = 'Connected';
            connectedLabel.style.display = 'block';
            if (optionsBtn && optionsDropdown && dropdownDisconnect) {
                optionsBtn.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    document.querySelectorAll('.options-dropdown').forEach(dd => { if (dd !== optionsDropdown) dd.style.display = 'none'; });
                    optionsDropdown.style.display = optionsDropdown.style.display === 'block' ? 'none' : 'block';
                };
                dropdownDisconnect.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    disconnectModal.dataset.connectionId = connectionObjs[0].id;
                    disconnectModal.style.display = 'flex';
                    optionsDropdown.style.display = 'none';
                };
            }
        } else {
            // Hide options for not connected
            if (cardOptions) cardOptions.style.display = 'none';
            if (connectBtn) {
                connectBtn.textContent = 'Connect';
                connectBtn.classList.add('primary');
                connectBtn.classList.remove('secondary');
                connectBtn.style.display = 'inline-flex';
                connectBtn.onclick = null;
            }
            connectedLabel.textContent = '';
            connectedLabel.style.display = 'none';
        }
    });
    // Hide dropdown on outside click
    document.addEventListener('click', function(e) {
        document.querySelectorAll('.options-dropdown').forEach(dd => dd.style.display = 'none');
    });
})();

// --- Generalized Modal Logic for All DBs ---
const dbModal = document.getElementById('snowflake-modal');
const closeDbModal = document.getElementById('close-snowflake-modal');
const dbForm = document.getElementById('snowflake-form');
const dbStatus = document.getElementById('snowflake-status');
const dbConnectBtn = document.getElementById('connect-snowflake-btn');
const dbSchemasSection = document.getElementById('schemas-section');
const dbSubmitBtn = document.getElementById('submit-snowflake-btn');
let dbFormData = null;
let dbAvailableSchemas = [];
let currentDbType = null;

// Helper to set up the modal for a given db type
function setupDbModal(dbType) {
    currentDbType = dbType;
    // Set modal title dynamically
    const dbModalTitle = document.getElementById('db-modal-title');
    if (dbType === 'snowflake') dbModalTitle.textContent = 'Connect to Snowflake';
    else if (dbType === 'postgres') dbModalTitle.textContent = 'Connect to PostgreSQL';
    else if (dbType === 'mysql') dbModalTitle.textContent = 'Connect to MySQL';
    else if (dbType === 'mongodb') dbModalTitle.textContent = 'Connect to MongoDB';
    else dbModalTitle.textContent = 'Connect to Database';
    // Clear form
    dbForm.reset();
    dbStatus.textContent = '';
    dbSchemasSection.style.display = 'none';
    dbConnectBtn.style.display = 'inline-block';
    dbSubmitBtn.style.display = 'none';
    // Show/hide fields based on dbType
    dbForm.querySelectorAll('div').forEach(div => div.style.display = 'none');
    if (dbType === 'snowflake') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            // Only show Snowflake-specific and common fields, NOT host/port
            if (["account","role","warehouse","user","password","database"].some(f => text.includes(f))) div.style.display = 'block';
            // Hide 'username' field for snowflake
            if (text.includes('username')) div.style.display = 'none';
        });
    } else if (dbType === 'postgres') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            if (["host","port","database","user","password"].some(f => text.includes(f))) div.style.display = 'block';
            // Hide 'username' field for postgres
            if (text.includes('username')) div.style.display = 'none';
        });
    } else if (dbType === 'mysql') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            // For MySQL, show host, port, user, password but NOT database
            if (["host","port","user","password"].some(f => text.includes(f))) div.style.display = 'block';
            // Hide 'username' field for mysql
            if (text.includes('username')) div.style.display = 'none';
        });
    } else if (dbType === 'mongodb') {
        dbForm.querySelectorAll('div').forEach(div => {
            const label = div.querySelector('label');
            if (!label) return;
            const text = label.textContent.toLowerCase();
            // For MongoDB, show username, password, database, cluster_url, options
            if (["username","password","database","cluster","options"].some(f => text.includes(f))) div.style.display = 'block';
            // Hide 'user' field for mongodb
            if (text === 'user:') div.style.display = 'none';
        });
    }
}

// --- Button click handler (robust, generalized) ---
connectionButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const card = button.closest('.connection-card');
        const connectionName = card.querySelector('.connection-name').textContent.trim();
        // Prevent opening connect modal if already connected (View button)
        if (button.textContent === 'View') {
            // Redirect to connection view page (if implemented)
            const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
            let platform = name;
            if (platform === 'postgresql') platform = 'postgres';
            const connectionObjs = connected.filter(c => c.database_platform.toLowerCase() === platform);
            if (connectionObjs.length > 0) {
                // Redirect to connection view page for the first connection
                window.location.href = `/connection_view?connection_id=${connectionObjs[0].id}`;
            }
            return;
        }
        if (button.textContent.includes('Connect')) {
            if (connectionName === 'Snowflake') {
                setupDbModal('snowflake');
                dbModal.style.display = 'flex';
            } else if (connectionName === 'PostgreSQL') {
                setupDbModal('postgres');
                dbModal.style.display = 'flex';
            } else if (connectionName === 'MySQL') {
                setupDbModal('mysql');
                dbModal.style.display = 'flex';
            } else if (connectionName === 'MongoDB') {
                setupDbModal('mongodb');
                dbModal.style.display = 'flex';
            }
        } else if (button.textContent.includes('Coming Soon')) {
            alert(`${connectionName} coming soon!`);
        }
    });
});

closeDbModal.addEventListener('click', () => {
    dbModal.style.display = 'none';
});
dbModal.addEventListener('click', (e) => {
    if (e.target === dbModal) dbModal.style.display = 'none';
});

// --- Generalized Form Submit Handler ---
dbForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    if (dbConnectBtn.style.display === 'none') return;
    dbStatus.textContent = '';
    dbSchemasSection.style.display = 'none';
    dbConnectBtn.disabled = true;
    dbConnectBtn.querySelector('.button-spinner').style.display = 'inline-block';
    // Gather form data based on db type
    const formData = new FormData(dbForm);
    let backend, connection_params;
    if (currentDbType === 'snowflake') {
        const requiredFields = ['account','user','password','role','warehouse','database'];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in all required fields.</span>`;
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
        }
        backend = 'snowflake';
        connection_params = {
            account: formData.get('account'),
            user: formData.get('user'),
            password: formData.get('password'),
            role: formData.get('role'),
            warehouse: formData.get('warehouse'),
            database: formData.get('database')
        };
    } else if (currentDbType === 'postgres') {
        const requiredFields = ['host','port','database','user','password'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                missingFields.push(field.charAt(0).toUpperCase() + field.slice(1));
            }
        }
        if (missingFields.length > 0) {
            dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in: ${missingFields.join(', ')}.</span>`;
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        backend = 'postgres';
        connection_params = {
            host: formData.get('host'),
            port: parseInt(formData.get('port'), 10),
            database: formData.get('database'),
            user: formData.get('user'),
            password: formData.get('password')
        };
    } else if (currentDbType === 'mysql') {
        const requiredFields = ['host','port','user','password'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                missingFields.push(field.charAt(0).toUpperCase() + field.slice(1));
            }
        }
        if (missingFields.length > 0) {
            dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in: ${missingFields.join(', ')}.</span>`;
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        backend = 'mysql';
        connection_params = {
            host: formData.get('host'),
            port: parseInt(formData.get('port'), 10),
            user: formData.get('user'),
            password: formData.get('password')
        };
        // Only include database if provided
        if (formData.get('database')) {
            connection_params.database = formData.get('database');
        }
    } else if (currentDbType === 'mongodb') {
        const requiredFields = ['username','password','cluster_url','database'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!formData.get(field)) {
                missingFields.push(field.charAt(0).toUpperCase() + field.slice(1).replace('_', ' '));
            }
        }
        if (missingFields.length > 0) {
            dbStatus.innerHTML = `<span style='color:#dc2626;'>Please fill in: ${missingFields.join(', ')}.</span>`;
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        backend = 'mongodb';
        connection_params = {
            username: formData.get('username'),
            password: formData.get('password'),
            cluster_url: formData.get('cluster_url'),
            database: formData.get('database'),
            options: formData.get('options') || 'retryWrites=true&w=majority'
        };
    }
    dbFormData = { backend, connection_params, optional: {} };
    try {
        // 1. Test connection
        const testRes = await fetch('/api/v1/db-schema/test-connection', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dbFormData)
        });
        const testResult = await testRes.json();
        if (!testRes.ok || !testResult.success) {
            dbStatus.innerHTML = '<span style="color:#dc2626;">Invalid credentials. Please check your details.</span>' +
                (testResult.error_detail ? `<div style="color:#6b7280; font-size:12px; margin-top:4px;">${testResult.error_detail}</div>` : '');
            dbConnectBtn.disabled = false;
            dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
            return;
        }
        dbStatus.textContent = 'Connection successful!';
        // 2. Fetch schemas if needed
        if (currentDbType === 'postgres' || currentDbType === 'snowflake' || currentDbType === 'mysql' || currentDbType === 'mongodb') {
            dbStatus.textContent = 'Connection successful! Fetching schemas...';
            const schemaRes = await fetch('/api/v1/db-schema/list-schemas', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(dbFormData)
            });
            const schemaResult = await schemaRes.json();
            if (!schemaRes.ok || !schemaResult.schemas) {
                dbStatus.textContent = 'Failed to fetch schemas: ' + (schemaResult.detail || 'Unknown error');
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
            dbAvailableSchemas = schemaResult.schemas;
            if (dbAvailableSchemas.length === 0) {
                dbStatus.textContent = 'No schemas found.';
                dbConnectBtn.disabled = false;
                dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
                return;
            }
            // Show checkboxes for schemas for Postgres, Snowflake, MySQL, and MongoDB
            let labelText = 'Select schemas to analyze:';
            if (currentDbType === 'mysql') {
                labelText = 'Select databases to analyze:';
            } else if (currentDbType === 'mongodb') {
                labelText = 'Select collections to analyze:';
            }
            // Create the checkbox section with Select All option
            const selectAllId = 'select-all-' + currentDbType;
            const checkboxesHtml = `
                <label style="font-weight:600; color:#374151;">${labelText}</label>
                <div style="margin-top:12px; margin-bottom:8px;">
                    <label style="background:#e0f2fe; border-radius:6px; padding:8px 12px; display:inline-flex; align-items:center; gap:6px; cursor:pointer; border:1px solid #0891b2; font-weight:500;">
                        <input type="checkbox" id="${selectAllId}" style="margin-right:6px;">Select All
                    </label>
                </div>
                <div style="display:flex; flex-wrap:wrap; gap:10px;">
                    ${dbAvailableSchemas.map(s => `<label style=\"background:#f3f4f6; border-radius:6px; padding:6px 12px; margin-bottom:4px; display:flex; align-items:center; gap:6px; cursor:pointer;\"><input type=\"checkbox\" name=\"selected_schemas\" value=\"${s}\" style=\"margin-right:6px;\">${s}</label>`).join('')}
                </div>
            `;
            dbSchemasSection.innerHTML = checkboxesHtml;
            
            // Add Select All functionality
            const selectAllCheckbox = document.getElementById(selectAllId);
            const schemaCheckboxes = dbSchemasSection.querySelectorAll('input[name="selected_schemas"]');
            
            selectAllCheckbox.addEventListener('change', () => {
                schemaCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                // Trigger change event to update submit button state
                dbSchemasSection.dispatchEvent(new Event('change'));
            });
            
            // Update Select All state when individual checkboxes change
            schemaCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const checkedCount = Array.from(schemaCheckboxes).filter(cb => cb.checked).length;
                    selectAllCheckbox.checked = checkedCount === schemaCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < schemaCheckboxes.length;
                });
            });
            
            dbSchemasSection.style.display = 'block';
            dbConnectBtn.style.display = 'none';
            dbSubmitBtn.style.display = 'inline-block';
            if (currentDbType === 'mysql') {
                dbStatus.textContent = 'Select database(s) and click Submit.';
            } else if (currentDbType === 'mongodb') {
                dbStatus.textContent = 'Select collection(s) and click Submit.';
            } else {
                dbStatus.textContent = 'Select schema(s) and click Submit.';
            }
        }
    } catch (err) {
        dbStatus.textContent = 'Network error: ' + err.message;
        dbConnectBtn.disabled = false;
        dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
    }
    dbConnectBtn.disabled = false;
    dbConnectBtn.querySelector('.button-spinner').style.display = 'none';
});

// Enable/disable Submit button based on selection (for Postgres/Snowflake/MySQL/MongoDB)
dbSchemasSection.addEventListener('change', (e) => {
    if (currentDbType === 'postgres' || currentDbType === 'snowflake' || currentDbType === 'mysql' || currentDbType === 'mongodb') {
        const checked = dbSchemasSection.querySelectorAll('input[type="checkbox"]:checked');
        dbSubmitBtn.disabled = checked.length === 0;
    } else {
        const checked = dbSchemasSection.querySelectorAll('input[type="radio"]:checked');
        dbSubmitBtn.disabled = checked.length === 0;
    }
});

// On submit, start schema generation and open log modal
dbSubmitBtn.addEventListener('click', async (e) => {
    e.preventDefault();
    let selectedSchemas = [];
    if (currentDbType === 'postgres' || currentDbType === 'snowflake' || currentDbType === 'mysql' || currentDbType === 'mongodb') {
        selectedSchemas = Array.from(dbSchemasSection.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
        if (selectedSchemas.length === 0) {
            if (currentDbType === 'mysql') {
                dbStatus.textContent = 'Please select at least one database.';
            } else if (currentDbType === 'mongodb') {
                dbStatus.textContent = 'Please select at least one collection.';
            } else {
                dbStatus.textContent = 'Please select at least one schema.';
            }
            return;
        }
        if (currentDbType === 'mysql') {
            dbFormData.optional = { databases: selectedSchemas };
        } else {
            dbFormData.optional = { schemas: selectedSchemas };
        }
    } else {
        selectedSchemas = Array.from(dbSchemasSection.querySelectorAll('input[type="radio"]:checked')).map(cb => cb.value);
        if (selectedSchemas.length === 0) {
            dbStatus.textContent = 'Please select a schema.';
            return;
        }
        dbFormData.optional = { schemas: selectedSchemas };
    }
    dbStatus.textContent = 'Starting schema generation...';
    logModal.style.display = 'flex';
    terminalLog.textContent = '';
    try {
        const response = await fetch('/api/v1/db-schema/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dbFormData)
        });
        const result = await response.json();
        if (response.ok) {
            dbStatus.textContent = 'Schema generation started! Request ID: ' + result.request_id;
            
            // Close any existing WebSocket connection
            if (ws) { 
                ws.close(); 
                ws = null;
            }
            
            // Clear the terminal log to start fresh
            terminalLog.textContent = '';
            
            // Create new WebSocket connection
            ws = new WebSocket(`ws://${window.location.host}/ws/schema-progress/${result.request_id}`);
            
            ws.onopen = () => {
                terminalLog.innerHTML += '<div style="color:#10b981;">🔗 Connected to live progress feed</div>';
                terminalLog.scrollTop = terminalLog.scrollHeight;
            };
            
            ws.onmessage = (event) => {
                try {
                    const msg = event.data;
                    let parsed = null;
                    try { parsed = JSON.parse(msg); } catch {}
                    
                    // Handle heartbeat messages with optional display
                    if (parsed && parsed.type === 'heartbeat') {
                        if (parsed.message) {
                            terminalLog.innerHTML += '<div style="color:#6b7280; font-style:italic;">' + parsed.message + '</div>';
                        }
                        terminalLog.scrollTop = terminalLog.scrollHeight;
                        return;
                    }
                    
                    if (parsed && parsed.type === 'final_report') {
                        showCompletionUI(parsed.report);
                        ws.close(); // Close the WebSocket after final report
                    } else {
                        // Display the message (could be timestamped or plain text)
                        const displayMsg = parsed && parsed.message ? parsed.message : msg;
                        terminalLog.innerHTML += '<div>' + displayMsg + '</div>';
                    }
                } catch (err) {
                    terminalLog.innerHTML += '<div>' + event.data + '</div>';
                }
                terminalLog.scrollTop = terminalLog.scrollHeight;
            };
            ws.onclose = () => {
                terminalLog.innerHTML += '<div style="color:#f87171;">WebSocket connection closed.</div>';
            };
        } else {
            dbStatus.textContent = 'Error: ' + (result.detail || result.message || 'Unknown error');
        }
    } catch (err) {
        dbStatus.textContent = 'Network error: ' + err.message;
    }
});

closeLogModal.addEventListener('click', () => {
    logModal.style.display = 'none';
    if (ws) ws.close();
});
logModal.addEventListener('click', (e) => {
    if (e.target === logModal) logModal.style.display = 'none';
    if (ws) ws.close();
});

// Disconnect modal logic (outside async IIFE)
const disconnectModal = document.getElementById('disconnect-modal');
const disconnectCancelBtn = document.getElementById('disconnect-cancel-btn');
const disconnectConfirmBtn = document.getElementById('disconnect-confirm-btn');
if (disconnectModal && disconnectCancelBtn && disconnectConfirmBtn) {
    disconnectCancelBtn.addEventListener('click', () => {
        disconnectModal.style.display = 'none';
    });
    disconnectModal.addEventListener('click', (e) => {
        if (e.target === disconnectModal) disconnectModal.style.display = 'none';
    });
    disconnectConfirmBtn.addEventListener('click', async () => {
        const connectionId = disconnectModal.dataset.connectionId;
        if (!connectionId) return;
        disconnectConfirmBtn.disabled = true;
        disconnectConfirmBtn.textContent = 'Disconnecting...';
        try {
            const res = await fetch(`/api/v1/connections/${connectionId}/disconnect`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });
            if (res.ok) {
                // Reset the card to disconnected state instead of removing it
                const card = Array.from(document.querySelectorAll('.connection-card')).find(card => {
                    const name = card.querySelector('.connection-name').textContent.trim().toLowerCase();
                    let platform = name;
                    if (platform === 'postgresql') platform = 'postgres';
                    return connectedIds[platform] == connectionId;
                });
                if (card) {
                    // Hide options menu
                    const cardOptions = card.querySelector('.card-options');
                    if (cardOptions) cardOptions.style.display = 'none';
                    // Show the connect button
                    const connectBtn = card.querySelector('.connection-button');
                    if (connectBtn) {
                        connectBtn.textContent = 'Connect';
                        connectBtn.classList.add('primary');
                        connectBtn.classList.remove('secondary');
                        connectBtn.style.display = 'inline-flex';
                    }
                    // Optionally reset description/status
                    // const desc = card.querySelector('.connection-description');
                    // if (desc) desc.textContent = 'Connect to your ... database.';
                }
            } else {
                const data = await res.json();
                alert(data.detail || 'Failed to disconnect.');
            }
        } catch (e) {
            alert('Failed to disconnect.');
        } finally {
            disconnectConfirmBtn.disabled = false;
            disconnectConfirmBtn.textContent = 'Disconnect';
            disconnectModal.style.display = 'none';
        }
    });
} 

const dataSourcesTab = document.getElementById('data-sources-tab');
const messagingTab = document.getElementById('messaging-tab');
const dataSourcesGrid = document.getElementById('data-sources-grid');
const messagingGrid = document.getElementById('messaging-grid');

dataSourcesTab.addEventListener('click', function(e) {
    e.preventDefault();
    dataSourcesTab.classList.add('active');
    messagingTab.classList.remove('active');
    dataSourcesGrid.style.display = '';
    messagingGrid.style.display = 'none';
});

messagingTab.addEventListener('click', function(e) {
    e.preventDefault();
    messagingTab.classList.add('active');
    dataSourcesTab.classList.remove('active');
    messagingGrid.style.display = '';
    dataSourcesGrid.style.display = 'none';
}); 