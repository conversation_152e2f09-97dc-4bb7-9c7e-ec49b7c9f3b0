const urlParams = new URLSearchParams(window.location.search);
const connectionId = urlParams.get('connection_id');
let tablesData = [];
let filteredTables = [];
let tablesPage = 1;
let tablesPageSize = 10;

async function fetchTables() {
    const res = await fetch(`/api/v1/connections/${connectionId}/tables?page=1&page_size=100`);
    if (!res.ok) throw new Error('Failed to fetch tables');
    const data = await res.json();
    tablesData = data.tables;
    filteredTables = tablesData.slice();
    renderTables();
    renderPagination();
}

function renderTables() {
    const tbody = document.getElementById('tables-list-body');
    tbody.innerHTML = '';
    const start = (tablesPage - 1) * tablesPageSize;
    const end = start + tablesPageSize;
    const pageTables = filteredTables.slice(start, end);
    pageTables.forEach(table => {
        const tr = document.createElement('tr');
        // Name
        const tdName = document.createElement('td');
        tdName.textContent = table.name;
        tr.appendChild(tdName);
        // Description
        const tdDesc = document.createElement('td');
        tdDesc.textContent = table.description || '';
        tr.appendChild(tdDesc);
        // Status
        const tdStatus = document.createElement('td');
        tdStatus.innerHTML = `<span style="color:${table.status === 'active' ? '#22c55e' : '#f87171'}; font-weight:600;">${table.status}</span>`;
        tr.appendChild(tdStatus);
        // View fields button
        const tdBtn = document.createElement('td');
        const btn = document.createElement('button');
        btn.textContent = 'View Fields';
        btn.className = 'view-fields-btn';
        btn.style = 'padding:6px 16px; border-radius:6px; border:none; background:#6366f1; color:#fff; font-weight:600; cursor:pointer;';
        btn.addEventListener('click', () => {
            window.location.href = `connection_view.html?connection_id=${connectionId}&table_id=${table.id}`;
        });
        tdBtn.appendChild(btn);
        tr.appendChild(tdBtn);
        tbody.appendChild(tr);
    });
}

function renderPagination() {
    const controls = document.getElementById('tables-pagination-controls');
    controls.innerHTML = '';
    const totalPages = Math.ceil(filteredTables.length / tablesPageSize);
    for (let i = 1; i <= totalPages; i++) {
        const btn = document.createElement('button');
        btn.textContent = i;
        btn.className = (i === tablesPage ? 'active' : '');
        btn.addEventListener('click', () => {
            tablesPage = i;
            renderTables();
        });
        controls.appendChild(btn);
    }
}

const searchInput = document.getElementById('tables-search');
searchInput.addEventListener('input', () => {
    const q = searchInput.value.toLowerCase();
    filteredTables = tablesData.filter(t => t.name.toLowerCase().includes(q) || (t.description && t.description.toLowerCase().includes(q)));
    tablesPage = 1;
    renderTables();
    renderPagination();
});

fetchTables(); 