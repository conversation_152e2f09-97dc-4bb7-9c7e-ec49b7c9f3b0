document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const connectionId = urlParams.get('connection_id');
    const tablesList = document.getElementById('tables-list');
    const fieldsSection = document.getElementById('fields-section');
    const fieldsList = document.getElementById('fields-list');
    const selectedTableName = document.getElementById('selected-table-name');
    const paginationControls = document.getElementById('pagination-controls');
    let currentPage = 1;
    let pageSize = 10;
    let totalTables = 0;

    if (!connectionId) {
        tablesList.innerHTML = '<div style="color:#dc2626;">No connection selected.</div>';
        return;
    }

    async function fetchTables(page = 1) {
        tablesList.innerHTML = '<div class="loading">Loading tables...</div>';
        fieldsSection.style.display = 'none';
        try {
            const res = await fetch(`/api/v1/connections/${connectionId}/tables?page=${page}&page_size=${pageSize}`);
            if (!res.ok) throw new Error('Failed to fetch tables');
            const data = await res.json();
            totalTables = data.total;
            renderTables(data.tables);
            renderPagination(data.page, data.page_size, data.total);
        } catch (e) {
            tablesList.innerHTML = `<div style='color:#dc2626;'>${e.message}</div>`;
        }
    }

    function renderTables(tables) {
        if (!tables.length) {
            tablesList.innerHTML = '<div>No tables found for this connection.</div>';
            return;
        }
        tablesList.innerHTML = '';
        tables.forEach(table => {
            const div = document.createElement('div');
            div.className = 'table-row';
            div.style = 'display:flex; align-items:center; justify-content:space-between; padding:10px 0; border-bottom:1px solid #e5e7eb; cursor:pointer; position:relative;';
            div.innerHTML = `
                <div class="table-main" style="display:flex; align-items:center; flex:1; gap:12px; cursor:pointer;">
                    <span class="chevron" style="display:inline-block; transition:transform 0.2s; font-size:16px;">▶</span>
                    <span style="font-weight:600;">${table.name}</span>
                    <span class="table-desc" style="color:#6b7280; font-size:13px; cursor:pointer;">${table.description || ''}</span>
                </div>
                <label style="margin-left:16px; display:flex; align-items:center; gap:6px; cursor:pointer;">
                    <input type="checkbox" class="table-status-checkbox" data-id="${table.id}" ${table.status === 'active' ? 'checked' : ''}>
                    <span style="color:#6b7280;">Active</span>
                </label>
            `;
            // Add a container for fields (hidden by default)
            const fieldsContainer = document.createElement('div');
            fieldsContainer.className = 'fields-inner-container';
            fieldsContainer.style = 'display:none; padding:10px 0 10px 32px; background:#f9fafb; border-radius:8px;';
            div.appendChild(fieldsContainer);
            let expanded = false;
            const tableMain = div.querySelector('.table-main');
            const chevron = div.querySelector('.chevron');
            tableMain.addEventListener('click', async function(e) {
                e.stopPropagation();
                expanded = !expanded;
                if (expanded) {
                    chevron.style.transform = 'rotate(90deg)';
                    fieldsContainer.innerHTML = '<div class="loading">Loading fields...</div>';
                    fieldsContainer.style.display = 'block';
                    try {
                        const res = await fetch(`/api/v1/tables/${table.id}/fields`);
                        if (!res.ok) throw new Error('Failed to fetch fields');
                        const fields = await res.json();
                        if (!fields.length) {
                            fieldsContainer.innerHTML = '<div>No fields found for this table.</div>';
                        } else {
                            fieldsContainer.innerHTML = '';
                            fields.forEach(field => {
                                const fieldDiv = document.createElement('div');
                                fieldDiv.className = 'field-row';
                                fieldDiv.style = 'display:flex; align-items:center; gap:18px; padding:6px 0; border-bottom:1px solid #e5e7eb;';
                                fieldDiv.innerHTML = `
                                    <span style="font-weight:500;">${field.name}</span>
                                    <span class="field-desc" style="color:#6b7280; font-size:13px; cursor:pointer;">${field.description || ''}</span>
                                    <span style="color:#6b7280;">${field.data_type}</span>
                                    <span style="color:#6b7280;">${field.status}</span>
                                `;
                                // Inline edit for field description
                                const fieldDescSpan = fieldDiv.querySelector('.field-desc');
                                fieldDescSpan.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const input = document.createElement('input');
                                    input.type = 'text';
                                    input.value = field.description || '';
                                    input.style = 'font-size:13px; color:#6b7280;';
                                    fieldDescSpan.replaceWith(input);
                                    input.focus();
                                    input.addEventListener('blur', async function() {
                                        await saveFieldDescription(field.id, input.value);
                                        fieldDescSpan.textContent = input.value;
                                        input.replaceWith(fieldDescSpan);
                                    });
                                    input.addEventListener('keydown', async function(ev) {
                                        if (ev.key === 'Enter') {
                                            input.blur();
                                        }
                                    });
                                });
                                fieldsContainer.appendChild(fieldDiv);
                            });
                        }
                    } catch (err) {
                        fieldsContainer.innerHTML = `<div style='color:#dc2626;'>${err.message}</div>`;
                    }
                } else {
                    chevron.style.transform = '';
                    fieldsContainer.style.display = 'none';
                }
            });
            // Inline edit for table description
            const tableDescSpan = div.querySelector('.table-desc');
            tableDescSpan.addEventListener('click', function(e) {
                e.stopPropagation();
                const input = document.createElement('input');
                input.type = 'text';
                input.value = table.description || '';
                input.style = 'font-size:13px; color:#6b7280;';
                tableDescSpan.replaceWith(input);
                input.focus();
                input.addEventListener('blur', async function() {
                    await saveTableDescription(table.id, input.value);
                    tableDescSpan.textContent = input.value;
                    input.replaceWith(tableDescSpan);
                });
                input.addEventListener('keydown', async function(ev) {
                    if (ev.key === 'Enter') {
                        input.blur();
                    }
                });
            });
            div.querySelector('.table-status-checkbox').addEventListener('change', async function(e) {
                e.stopPropagation();
                await toggleTableStatus(table.id, this.checked ? 'active' : 'inactive');
            });
            div.addEventListener('mouseover', function() {
                div.style.background = '#f3f4f6';
            });
            div.addEventListener('mouseout', function() {
                div.style.background = '';
            });
            tablesList.appendChild(div);
        });
    }

    async function toggleTableStatus(tableId, newStatus) {
        try {
            await fetch(`/api/v1/tables/${tableId}/status`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: newStatus })
            });
            fetchTables(currentPage);
        } catch (e) {
            alert('Failed to update table status');
        }
    }

    function renderPagination(page, pageSize, total) {
        paginationControls.innerHTML = '';
        const totalPages = Math.ceil(total / pageSize);
        if (totalPages <= 1) return;
        for (let i = 1; i <= totalPages; i++) {
            const btn = document.createElement('button');
            btn.textContent = i;
            btn.className = (i === page ? 'active' : '');
            btn.style = 'margin:0 4px; padding:4px 10px; border-radius:4px; border:1px solid #e5e7eb; background:' + (i === page ? '#6366f1; color:#fff;' : '#fff; color:#374151;');
            btn.addEventListener('click', function() {
                currentPage = i;
                fetchTables(currentPage);
            });
            paginationControls.appendChild(btn);
        }
    }

    async function showFieldsForTable(tableId, tableName) {
        fieldsSection.style.display = 'block';
        selectedTableName.textContent = tableName;
        fieldsList.innerHTML = '<div class="loading">Loading fields...</div>';
        try {
            const res = await fetch(`/api/v1/tables/${tableId}/fields`);
            if (!res.ok) throw new Error('Failed to fetch fields');
            const fields = await res.json();
            renderFields(fields, tableId);
        } catch (e) {
            fieldsList.innerHTML = `<div style='color:#dc2626;'>${e.message}</div>`;
        }
    }

    function renderFields(fields, tableId) {
        if (!fields.length) {
            fieldsList.innerHTML = '<div>No fields found for this table.</div>';
            return;
        }
        fieldsList.innerHTML = '';
        fields.forEach(field => {
            const div = document.createElement('div');
            div.className = 'field-row';
            div.style = 'display:flex; align-items:center; justify-content:space-between; padding:8px 0; border-bottom:1px solid #e5e7eb;';
            div.innerHTML = `
                <span style="font-weight:500;">${field.name}</span>
                <span style="margin-left:16px; color:#6b7280;">${field.data_type}</span>
                <span style="margin-left:16px; color:#6b7280;">${field.status}</span>
                <button class="toggle-field-status" data-id="${field.id}" style="margin-left:16px;">${field.status === 'active' ? 'Deactivate' : 'Activate'}</button>
            `;
            div.querySelector('.toggle-field-status').addEventListener('click', async function(e) {
                e.stopPropagation();
                await toggleFieldStatus(field.id, field.status === 'active' ? 'inactive' : 'active', tableId, field.name);
            });
            fieldsList.appendChild(div);
        });
    }

    async function toggleFieldStatus(fieldId, newStatus, tableId, tableName) {
        try {
            await fetch(`/api/v1/fields/${fieldId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: newStatus })
            });
            showFieldsForTable(tableId, tableName);
        } catch (e) {
            alert('Failed to update field status');
        }
    }

    async function saveTableDescription(tableId, newDesc) {
        try {
            await fetch(`/api/v1/tables/${tableId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ description: newDesc })
            });
        } catch (e) {
            alert('Failed to update table description');
        }
    }

    async function saveFieldDescription(fieldId, newDesc) {
        try {
            await fetch(`/api/v1/fields/${fieldId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ description: newDesc })
            });
        } catch (e) {
            alert('Failed to update field description');
        }
    }

    // Initial fetch
    fetchTables(currentPage);
}); 