// Sidebar toggle
const sidebar = document.getElementById('sidebar');
const sidebarToggle = document.getElementById('sidebar-toggle');
sidebarToggle.addEventListener('click', () => {
    if (sidebar.style.display === 'none') {
        sidebar.style.display = 'block';
    } else {
        sidebar.style.display = 'none';
    }
});

// Tab switching
const tabs = document.querySelectorAll('.tab');
const tabContents = document.querySelectorAll('.tab-content');
tabs.forEach(tab => {
    tab.addEventListener('click', () => {
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        tabContents.forEach(tc => tc.style.display = 'none');
        document.getElementById(tab.dataset.tab + '-tab').style.display = 'block';
    });
});

// Get connection_id from URL
const urlParams = new URLSearchParams(window.location.search);
const connectionId = urlParams.get('connection_id');

let tablesData = [];
let filteredTablesData = [];
let fieldsData = [];
let filteredFields = [];
let currentPage = 1;
let pageSize = 10;
let selectedTableId = null;

async function fetchTables() {
    console.log('Fetching tables for connection ID:', connectionId);
    const url = `/api/v1/connections/${connectionId}/tables?page=1&page_size=100`;
    console.log('Fetch URL:', url);
    
    const res = await fetch(url);
    console.log('Response status:', res.status, res.statusText);
    
    if (!res.ok) {
        const errorText = await res.text();
        console.error('Failed to fetch tables. Response:', errorText);
        throw new Error(`Failed to fetch tables: ${res.status} ${res.statusText}`);
    }
    
    const data = await res.json();
    console.log('Fetched data:', data);
    console.log('Data structure:', JSON.stringify(data, null, 2));
    
    if (!data.tables) {
        console.error('No tables field in response. Full response:', data);
        tablesData = [];
    } else {
        tablesData = data.tables;
        console.log('Tables found:', tablesData.length);
        if (tablesData.length > 0) {
            console.log('First table example:', tablesData[0]);
        }
    }
    
    filteredTablesData = tablesData.slice(); // Initialize filtered data
    
    console.log('Tables data set to:', tablesData);
    return tablesData;
}

async function fetchFields(tableId) {
    const res = await fetch(`/api/v1/tables/${tableId}/fields`);
    if (!res.ok) throw new Error('Failed to fetch fields');
    return await res.json();
}

function renderTableSelector() {
    const select = document.getElementById('table-select');
    select.innerHTML = '';
    
    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    
    if (filteredTablesData.length === 0) {
        defaultOption.textContent = 'No tables found';
    } else {
        defaultOption.textContent = 'Select a table...';
    }
    select.appendChild(defaultOption);
    
    // Add all filtered tables to dropdown
    filteredTablesData.forEach(table => {
        const option = document.createElement('option');
        option.value = table.id;
        option.textContent = table.name;
        select.appendChild(option);
    });
    
    // Set selected value
    select.value = selectedTableId || '';
    
    // Add change event listener (only once)
    select.removeEventListener('change', handleTableChange);
    select.addEventListener('change', handleTableChange);
    
    updateTableToolbar();
    
    console.log('Rendered table selector with', filteredTablesData.length, 'tables');
}

async function handleTableChange() {
    const select = document.getElementById('table-select');
    selectedTableId = select.value;
    updateTableToolbar();
    
    if (selectedTableId) {
        await loadFields(selectedTableId);
    } else {
        // Clear fields table when no table is selected
        const tbody = document.getElementById('fields-table-body');
        tbody.innerHTML = '';
        const controls = document.getElementById('pagination-controls');
        controls.innerHTML = '';
        fieldsData = [];
        filteredFields = [];
    }
}

function updateTableToolbar() {
    const table = tablesData.find(t => t.id == selectedTableId);
    const statusCheckbox = document.getElementById('table-status-checkbox');
    const editBtn = document.getElementById('table-edit-btn');
    
    if (!table) {
        statusCheckbox.checked = false;
        statusCheckbox.disabled = true;
        editBtn.disabled = true;
        editBtn.onclick = null;
        return;
    }
    
    statusCheckbox.checked = table.status === 'active';
    statusCheckbox.disabled = true;
    editBtn.disabled = false;
    editBtn.onclick = function() {
        showTableEditModal(table);
    };
}

function showTableEditModal(table) {
    const modal = document.getElementById('table-edit-modal');
    const descInput = document.getElementById('modal-table-description');
    const statusInput = document.getElementById('modal-table-status');
    const saveBtn = document.getElementById('modal-save-btn');
    const cancelBtn = document.getElementById('modal-cancel-btn');
    descInput.value = table.description || '';
    statusInput.checked = table.status === 'active';
    modal.style.display = 'flex';
    let originalDesc = descInput.value;
    let originalStatus = statusInput.checked;
    saveBtn.disabled = true;
    descInput.oninput = statusInput.onchange = function() {
        saveBtn.disabled = (descInput.value === originalDesc && statusInput.checked === originalStatus);
    };
    saveBtn.onclick = async function() {
        await updateTableDescription(table.id, descInput.value);
        await updateTableStatus(table.id, statusInput.checked ? 'active' : 'inactive');
        table.description = descInput.value;
        table.status = statusInput.checked ? 'active' : 'inactive';
        updateTableToolbar();
        modal.style.display = 'none';
    };
    cancelBtn.onclick = function() {
        modal.style.display = 'none';
    };
    // Close modal on background click
    modal.onclick = function(e) {
        if (e.target === modal) modal.style.display = 'none';
    };
}

async function updateTableDescription(tableId, newDesc) {
    await fetch(`/api/v1/tables/${tableId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: newDesc })
    });
}

async function updateTableStatus(tableId, newStatus) {
    await fetch(`/api/v1/tables/${tableId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
    });
}

async function loadFields(tableId) {
    fieldsData = await fetchFields(tableId);
    filteredFields = fieldsData.slice();
    currentPage = 1;
    renderFieldsTable();
}

function renderFieldsTable() {
    const tbody = document.getElementById('fields-table-body');
    tbody.innerHTML = '';
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pageFields = filteredFields.slice(start, end);
    pageFields.forEach(field => {
        const tr = document.createElement('tr');
        // Checkbox
        const tdCheck = document.createElement('td');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.checked = field.status === 'active';
        checkbox.addEventListener('change', async () => {
            await updateFieldStatus(field.id, checkbox.checked ? 'active' : 'inactive');
            field.status = checkbox.checked ? 'active' : 'inactive';
        });
        tdCheck.appendChild(checkbox);
        tr.appendChild(tdCheck);
        // Name + type
        const tdName = document.createElement('td');
        tdName.innerHTML = `<span style="font-weight:600;">${field.name}</span> <span style="color:#6b7280; font-size:12px; margin-left:6px;">${field.data_type}</span>`;
        tr.appendChild(tdName);
        // Description (inline editable with Save)
        const tdDesc = document.createElement('td');
        const input = document.createElement('input');
        input.type = 'text';
        input.value = field.description || '';
        const saveBtn = document.createElement('button');
        saveBtn.textContent = 'Save';
        saveBtn.style = 'margin-left:8px; padding:5px 12px; border-radius:6px; border:none; background:#6366f1; color:#fff; font-weight:600; font-size:0.95rem; display:none;';
        let originalValue = input.value;
        input.addEventListener('input', () => {
            saveBtn.style.display = (input.value !== originalValue) ? '' : 'none';
        });
        saveBtn.addEventListener('click', async () => {
            await updateFieldDescription(field.id, input.value);
            field.description = input.value;
            originalValue = input.value;
            saveBtn.style.display = 'none';
        });
        tdDesc.appendChild(input);
        tdDesc.appendChild(saveBtn);
        tr.appendChild(tdDesc);
        // Sample
        const tdSample = document.createElement('td');
        tdSample.textContent = field.categorical_values || '';
        tr.appendChild(tdSample);
        tbody.appendChild(tr);
    });
    renderPagination();
}

function renderPagination() {
    const controls = document.getElementById('pagination-controls');
    controls.innerHTML = '';
    const totalPages = Math.ceil(filteredFields.length / pageSize);
    for (let i = 1; i <= totalPages; i++) {
        const btn = document.createElement('button');
        btn.textContent = i;
        btn.className = (i === currentPage ? 'active' : '');
        btn.addEventListener('click', () => {
            currentPage = i;
            renderFieldsTable();
        });
        controls.appendChild(btn);
    }
}

// Table Search
const tablesSearchInput = document.getElementById('tables-search');
tablesSearchInput.addEventListener('input', () => {
    // Only proceed if tables data is loaded
    if (!tablesData || tablesData.length === 0) {
        console.log('No tables data loaded yet, skipping search');
        return;
    }
    
    const q = tablesSearchInput.value.toLowerCase();
    filteredTablesData = tablesData.filter(t => t.name.toLowerCase().includes(q) || (t.description && t.description.toLowerCase().includes(q)));
    
    // If current selected table is not in filtered results, clear selection
    if (selectedTableId && !filteredTablesData.some(t => t.id == selectedTableId)) {
        selectedTableId = null;
        // Clear fields table
        const tbody = document.getElementById('fields-table-body');
        tbody.innerHTML = '';
        const controls = document.getElementById('pagination-controls');
        controls.innerHTML = '';
    }
    
    renderTableSelector();
});

// Fields Search
const fieldsSearchInput = document.getElementById('fields-search');
fieldsSearchInput.addEventListener('input', () => {
    const q = fieldsSearchInput.value.toLowerCase();
    filteredFields = fieldsData.filter(f => f.name.toLowerCase().includes(q) || (f.description && f.description.toLowerCase().includes(q)));
    currentPage = 1;
    renderFieldsTable();
});

// Select all
const selectAll = document.getElementById('select-all-fields');
selectAll.addEventListener('change', async () => {
    for (const field of filteredFields) {
        await updateFieldStatus(field.id, selectAll.checked ? 'active' : 'inactive');
        field.status = selectAll.checked ? 'active' : 'inactive';
    }
    renderFieldsTable();
});

async function updateFieldDescription(fieldId, newDesc) {
    await fetch(`/api/v1/fields/${fieldId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: newDesc })
    });
}

async function updateFieldStatus(fieldId, newStatus) {
    await fetch(`/api/v1/fields/${fieldId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
    });
}

// Initial load
(async function() {
    if (!connectionId) {
        console.error('No connection ID found in URL');
        return;
    }
    
    try {
        console.log('Loading tables for connection:', connectionId);
        
        // First, let's debug what's actually in the database
        console.log('=== DEBUG: Checking database content ===');
        try {
            const debugRes = await fetch(`/api/v1/connections/${connectionId}/debug`);
            if (debugRes.ok) {
                const debugData = await debugRes.json();
                console.log('DEBUG - Database content:', JSON.stringify(debugData, null, 2));
            } else {
                console.log('DEBUG - Failed to fetch debug info:', debugRes.status, debugRes.statusText);
            }
        } catch (e) {
            console.log('DEBUG - Error fetching debug info:', e);
        }
        console.log('=== END DEBUG ===');
        
        tablesData = await fetchTables();
        console.log('Tables loaded:', tablesData.length, 'tables');
        
        // Always render the table selector, even if no tables
        renderTableSelector();
        
        // Optionally auto-select first table (you can comment this out if you prefer manual selection)
        if (tablesData.length > 0) {
            selectedTableId = tablesData[0].id;
            const select = document.getElementById('table-select');
            select.value = selectedTableId;
            updateTableToolbar();
            await loadFields(selectedTableId);
        }
    } catch (error) {
        console.error('Error loading tables:', error);
        // Still render the dropdown even if fetch fails
        renderTableSelector();
    }
})();

// Disconnect button/modal logic
const disconnectBtn = document.getElementById('disconnect-btn');
const disconnectModal = document.getElementById('disconnect-modal');
const disconnectCancelBtn = document.getElementById('disconnect-cancel-btn');
const disconnectConfirmBtn = document.getElementById('disconnect-confirm-btn');

if (disconnectBtn && disconnectModal && disconnectCancelBtn && disconnectConfirmBtn) {
    disconnectBtn.addEventListener('click', () => {
        disconnectModal.style.display = 'flex';
    });
    disconnectCancelBtn.addEventListener('click', () => {
        disconnectModal.style.display = 'none';
    });
    disconnectModal.addEventListener('click', (e) => {
        if (e.target === disconnectModal) disconnectModal.style.display = 'none';
    });
    disconnectConfirmBtn.addEventListener('click', async () => {
        disconnectConfirmBtn.disabled = true;
        disconnectConfirmBtn.textContent = 'Disconnecting...';
        try {
            const res = await fetch(`/api/v1/connections/${connectionId}/disconnect`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });
            if (res.ok) {
                window.location.href = '/connections';
            } else {
                const data = await res.json();
                alert(data.detail || 'Failed to disconnect.');
            }
        } catch (e) {
            alert('Failed to disconnect.');
        } finally {
            disconnectConfirmBtn.disabled = false;
            disconnectConfirmBtn.textContent = 'Disconnect';
            disconnectModal.style.display = 'none';
        }
    });
} 