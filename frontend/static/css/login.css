* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background: white;
    border-bottom: 1px solid #e2e8f0;
}

.logo {
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
}

.header-nav {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: #718096;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #2d3748;
}

.auth-buttons {
    display: flex;
    gap: 12px;
}

.login-btn-header {
    padding: 8px 16px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-btn-header:hover {
    background: #f7fafc;
}

.register-btn-header {
    padding: 8px 16px;
    background: #2d3748;
    color: white;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.register-btn-header:hover {
    background: #4a5568;
}

.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.login-container {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    padding: 40px;
    width: 100%;
    max-width: 420px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.login-header {
    text-align: left;
    margin-bottom: 32px;
}

.login-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 8px;
}

.login-subtitle {
    font-size: 14px;
    color: #718096;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-input::placeholder {
    color: #a0aec0;
}

.password-requirements {
    font-size: 12px;
    color: #e53e3e;
    margin-top: 6px;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: #2d3748;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.login-btn:hover {
    background: #4a5568;
}

.login-btn:disabled {
    background: #a0aec0;
    cursor: not-allowed;
}

.signup-link {
    text-align: center;
    color: #718096;
    font-size: 14px;
}

.signup-link a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
}

.signup-link a:hover {
    text-decoration: underline;
}

.footer {
    background: #2d3748;
    color: white;
    padding: 60px 40px 40px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
}

.footer-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #a0aec0;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.toast {
    display: none;
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 9999;
    min-width: 300px;
    background: #e53e3e;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.4s, top 0.4s;
}

.toast.success {
    background: #38a169;
}

@media (max-width: 768px) {
    .header {
        padding: 16px 20px;
    }

    .header-nav {
        gap: 20px;
    }

    .nav-link {
        display: none;
    }

    .login-container {
        padding: 32px 24px;
        margin: 0 16px;
    }

    .footer {
        padding: 40px 20px 24px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
} 