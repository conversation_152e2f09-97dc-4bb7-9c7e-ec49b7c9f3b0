body, html {
  background: #f7f8fa;
  color: #222;
  font-family: 'Inter', Arial, sans-serif;
  margin: 0;
  padding: 0;
}
.main-content.light-mode {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 0;
}
.page-header {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 32px 32px 16px 32px;
  margin-bottom: 18px;
}
.page-title {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 8px;
}
.data-source-name {
  font-size: 1.1rem;
  color: #6366f1;
  font-weight: 600;
  margin-bottom: 10px;
}
.data-source-description {
  width: 100%;
  min-height: 48px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 10px;
  font-size: 1rem;
  background: #f7f8fa;
  margin-bottom: 0;
  resize: vertical;
}
.tabs {
  display: flex;
  gap: 32px;
  background: #fff;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0 32px;
  margin-bottom: 0;
  border-bottom: 1px solid #e5e7eb;
}
.tab {
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  padding: 18px 0 12px 0;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-bottom 0.2s;
}
.tab.active {
  color: #1f2937;
  border-bottom: 2px solid #6366f1;
}
.tab-content {
  background: #fff;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24px 32px 32px 32px;
  margin-bottom: 32px;
}
.fields-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}
.fields-search {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 8px 14px;
  font-size: 1rem;
  background: #f7f8fa;
  width: 240px;
}
.fields-table-container {
  overflow-x: auto;
  background: #fff;
  border-radius: 8px;
}
.fields-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
}
.fields-table th, .fields-table td {
  padding: 12px 10px;
  text-align: left;
  font-size: 1rem;
}
.fields-table th {
  color: #6b7280;
  font-weight: 600;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e7eb;
}
.fields-table tr {
  border-bottom: 1px solid #e5e7eb;
}
.fields-table tr:last-child {
  border-bottom: none;
}
.fields-table td {
  background: #fff;
}
.fields-table input[type="text"] {
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  padding: 6px 10px;
  font-size: 1rem;
  background: #f7f8fa;
  width: 100%;
}
.fields-table input[type="checkbox"] {
  accent-color: #6366f1;
  width: 18px;
  height: 18px;
}
.pagination-controls {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 18px;
}
.pagination-controls button {
  background: #f7f8fa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 1rem;
  color: #374151;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
}
.pagination-controls button.active {
  background: #6366f1;
  color: #fff;
  border-color: #6366f1;
}
.sidebar-toggle {
  position: absolute;
  top: 24px;
  left: 24px;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1.5rem;
  padding: 6px 14px;
  cursor: pointer;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
@media (max-width: 900px) {
  .page-header, .tab-content, .tabs {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  .fields-search {
    width: 100%;
  }
} 