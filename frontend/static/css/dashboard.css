* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    height: 100vh;
    display: flex;
}

.sidebar {
    width: 230px;
    background: white;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    padding: 24px 0;
}

.sidebar-header {
    padding: 0 24px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.logo-icon {
    width: 20px;
    height: 20px;
    background: #6366f1;
    border-radius: 4px;
    margin-right: 8px;
}

.sidebar-nav {
    padding: 24px 0;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background: #f9fafb;
    color: #374151;
}

.nav-item.active {
    background: #f3f4f6;
    color: #1f2937;
    font-weight: 500;
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
}

.user-profile {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
}

.company-name {
    font-size: 12px;
    color: #6b7280;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-area {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

.welcome-header {
    margin-bottom: 32px;
}

.welcome-title {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.welcome-subtitle {
    color: #6b7280;
    font-size: 16px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.stat-title {
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.stat-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.stat-icon.blue { background: #3b82f6; }
.stat-icon.purple { background: #8b5cf6; }
.stat-icon.green { background: #10b981; }
.stat-icon.yellow { background: #f59e0b; }

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
}

.stat-growth {
    color: #10b981;
    font-size: 18px;
    font-weight: 600;
}

.section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.section-subtitle {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 24px;
}

.data-sources-list {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
}

.data-source-item {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.data-source-item:last-child {
    border-bottom: none;
}

.data-source-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: white;
    font-size: 18px;
}

.data-source-icon.snowflake { background: #29b5e8; }
.data-source-icon.slack { background: #4a154b; }
.data-source-icon.analytics { background: #f59e0b; }
.data-source-icon.crm { background: #10b981; }

.data-source-info {
    flex: 1;
}

.data-source-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.data-source-details {
    font-size: 14px;
    color: #6b7280;
}

.data-source-status {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 16px;
}

.status-healthy {
    background: #dcfce7;
    color: #15803d;
}

.status-warning {
    background: #fef3c7;
    color: #d97706;
}

.queries-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
}

.query-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #f3f4f6;
}

.query-item:last-child {
    border-bottom: none;
}

.query-icon {
    width: 32px;
    height: 32px;
    background: #ddd6fe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: #7c3aed;
    font-size: 14px;
}

.query-content {
    flex: 1;
}

.query-text {
    font-size: 16px;
    color: #1f2937;
    margin-bottom: 8px;
    font-weight: 500;
}

.query-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
}

.query-author {
    font-weight: 500;
}

.query-source {
    background: #f3f4f6;
    padding: 2px 8px;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .sidebar {
        width: 200px;
    }
    
    .content-area {
        padding: 20px;
    }
} 