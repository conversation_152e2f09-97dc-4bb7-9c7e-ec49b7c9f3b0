* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    height: 100vh;
    display: flex;
}

.sidebar {
    width: 230px;
    background: white;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    padding: 24px 0;
}

.sidebar-header {
    padding: 0 24px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.logo-icon {
    width: 20px;
    height: 20px;
    background: #6366f1;
    border-radius: 4px;
    margin-right: 8px;
}

.sidebar-nav {
    padding: 24px 0;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background: #f9fafb;
    color: #374151;
}

.nav-item.active {
    background: #f3f4f6;
    color: #1f2937;
    font-weight: 500;
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
}

.user-profile {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-right: 12px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
}

.company-name {
    font-size: 12px;
    color: #6b7280;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-area {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.page-subtitle {
    color: #6b7280;
    font-size: 16px;
}

.connection-tabs {
    display: flex;
    gap: 32px;
    margin-bottom: 32px;
    border-bottom: 1px solid #e5e7eb;
}

.tab {
    display: flex;
    align-items: center;
    padding: 12px 0;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab.active {
    color: #1f2937;
    border-bottom-color: #6366f1;
}

.tab-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.connections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 800px;
}

.connection-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.connection-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.connection-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.connection-icon.snowflake {
    background: #29b5e8;
}

.connection-icon.postgresql {
    background: #336791;
}

.connection-icon.mysql {
    background: #f29111;
}

.connection-icon.mongodb {
    background: #47a248;
}

.connection-name {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.connection-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 24px;
    line-height: 1.5;
}

.connection-button {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.connection-button.primary {
    background: #1f2937;
    color: white;
}

.connection-button.primary:hover {
    background: #374151;
}

.connection-button.secondary {
    background: #f9fafb;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.connection-button.secondary:hover {
    background: #f3f4f6;
}

.button-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
}

/* Spinner for loading state in buttons */
.button-spinner {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2.5px solid #fff;
  border-top: 2.5px solid #6366f1;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  vertical-align: middle;
  margin-right: 8px;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .connections-grid {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .content-area {
        padding: 20px;
    }
}

/* Snowflake Modal Styles */
#snowflake-modal-content {
  background: white;
  border-radius: 14px;
  padding: 18px 12px 18px 12px;
  max-width: 360px;
  width: 96vw;
  margin: 40px auto;
  position: relative;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  max-height: 90vh;
  overflow-y: auto;
}
#close-snowflake-modal {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
}
@media (max-width: 600px) {
  #snowflake-modal-content {
    max-width: 98vw !important;
    padding: 18px 6vw 18px 6vw !important;
  }
  #close-snowflake-modal {
    top: 6px !important;
    right: 6px !important;
    font-size: 22px !important;
  }
}

/* Terminal Log Modal */
#log-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.7);
  z-index: 2000;
  align-items: center;
  justify-content: center;
}
#log-modal-content {
  background: #18181b;
  border-radius: 14px;
  padding: 32px 24px 24px 24px;
  max-width: 700px;
  width: 95vw;
  margin: auto;
  position: relative;
  box-shadow: 0 8px 32px rgba(0,0,0,0.28);
  display: flex;
  flex-direction: column;
  min-height: 400px;
}
#close-log-modal {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #a1a1aa;
}
#terminal-log {
  background: #23272e;
  color: #fafafa;
  font-family: monospace;
  border-radius: 8px;
  padding: 18px;
  font-size: 15px;
  min-height: 220px;
  max-height: 350px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
#terminal-log div {
  margin-bottom: 4px;
  line-height: 1.5;
}
#terminal-log pre {
  color: #a3e635;
  margin: 0;
  font-size: 14px;
}
@media (max-width: 700px) {
  #log-modal-content {
    max-width: 99vw !important;
    padding: 12px 2vw 12px 2vw !important;
  }
}

/* Schema selection area improvement */
#schemas-section label[style*="font-weight:600"] {
  font-size: 15px;
  margin-bottom: 6px;
  display: block;
}
#schemas-section > div {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
#schemas-section label[style*="background:#f3f4f6"] {
  background: #f3f4f6;
  border-radius: 6px;
  padding: 6px 12px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  border: 1px solid #e5e7eb;
  transition: background 0.2s, border 0.2s;
}
#schemas-section label[style*="background:#f3f4f6"]:hover {
  background: #e0e7ef;
  border-color: #a5b4fc;
}
#schemas-section input[type="checkbox"] {
  accent-color: #6366f1;
}

.fields-inner-container {
  background: #f9fafb;
  border-radius: 8px;
  margin-top: 4px;
  margin-bottom: 4px;
  padding: 10px 0 10px 32px;
}

.disconnect-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    background: #dc2626;
    color: #fff;
    border: none;
    cursor: pointer;
}
.disconnect-btn:hover {
    background: #b91c1c;
}

.card-options {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    margin-left: 8px;
}
.options-btn {
    background: none;
    border: none;
    font-size: 22px;
    color: #6b7280;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    transition: background 0.2s;
}
.options-btn:hover, .options-btn:focus {
    background: #f3f4f6;
    outline: none;
}
.options-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 28px;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    min-width: 120px;
    z-index: 10;
    padding: 4px 0;
}
.options-dropdown button {
    width: 100%;
    background: none;
    border: none;
    color: #dc2626;
    font-weight: 500;
    font-size: 15px;
    padding: 10px 18px;
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s;
}
.options-dropdown button:hover {
    background: #fef2f2;
} 