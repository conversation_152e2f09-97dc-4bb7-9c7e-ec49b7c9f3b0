{"connection_id": 24, "provider": "mongodb", "schemas": [{"collection_name": "contacts", "description": "This collection stores information about individual contacts, including their unique identifiers and associated data. This table contains the following fields: _id, hash, contacts. The purpose of this collection is to manage and retrieve contact information efficiently, allowing for operations such as adding, updating, and querying contact details.", "field_count": 3, "has_sample_documents": true, "status": "deactivated", "categorical_examples": []}, {"collection_name": "assets", "description": "This collection stores information about various digital assets held by users. This table contains the following fields: _id, symbol, amount, owner, createdAt, updatedAt, __v, name, publicKey, privateSecret, baseAddress, wallet, privateKey, eventId. The purpose of this collection is to manage and track the ownership, quantity, and details of digital assets associated with users in the system.", "field_count": 15, "has_sample_documents": true, "status": "deactivated", "categorical_examples": []}, {"collection_name": "people", "description": "This collection stores detailed information about individuals, including their contact details, identification documents, and account information. This table contains the following fields: _id, name, emails, phones, devices, locations, currentCountry, currentEmail, currentPhone, currentDeviceInfo, isSecurityFlagged, idDocuments, addresses, paymentMethods, flags, createdAt, updatedAt, __v, kyc, id, searchId, tierInfo, searchEmail, devices, currentCountry, currentDeviceInfo, limits, externalAccounts, kyc, tierInfo. The purpose of this collection is to manage and track user profiles for various applications, ensuring secure and efficient access to services.", "field_count": 30, "has_sample_documents": true, "status": "deactivated", "categorical_examples": []}], "tables": [{"id": 346, "table_name": "contacts", "schema_name": "dev", "status": "active", "description": "This collection stores information about individual contacts, including their unique identifiers and associated data. This table contains the following fields: _id, hash, contacts. The purpose of this collection is to manage and retrieve contact information efficiently, allowing for operations such as adding, updating, and querying contact details.", "sample_row": "\"{\\n  \\\"_id\\\": \\\"a1b2c3d4e5f67890abcdef12\\\",\\n  \\\"hash\\\": \\\"f1e2d3c4b5a69788abcdef1234567890abcdef1234567890abcdef12345678\\\",\\n  \\\"contacts\\\": [\\n    \\\"1234567890abcdef1234567890abcdef\\\",\\n    \\\"abcdef1234567890abcdef1234567890\\\",\\n    \\\"fedcba0987654321abcdef1234567890\\\"\\n  ]\\n}\"", "fields": [{"id": 3201, "name": "_id", "data_type": "string", "description": "A unique identifier for each contact, automatically generated by MongoDB.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3202, "name": "hash", "data_type": "string", "description": "A hash value used for verifying the integrity of the contact data.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3203, "name": "contacts", "data_type": "array", "description": "An array of objects representing the contact details, such as name, phone number, and email address.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}]}, {"id": 347, "table_name": "assets", "schema_name": "dev", "status": "active", "description": "This collection stores information about various digital assets held by users. This table contains the following fields: _id, symbol, amount, owner, createdAt, updatedAt, __v, name, publicKey, privateSecret, baseAddress, wallet, privateKey, eventId. The purpose of this collection is to manage and track the ownership, quantity, and details of digital assets associated with users in the system.", "sample_row": "\"{\\n  \\\"_id\\\": \\\"72f8a3b45cd64113fa8069ef\\\",\\n  \\\"symbol\\\": \\\"USD\\\",\\n  \\\"amount\\\": \\\"12.34\\\",\\n  \\\"owner\\\": \\\"72b1f4a29a61a1337586898b\\\",\\n  \\\"createdAt\\\": \\\"2023-02-15T10:22:45.963000\\\",\\n  \\\"updatedAt\\\": \\\"2023-02-15T10:35:12.109000\\\",\\n  \\\"__v\\\": 0\\n}\"", "fields": [{"id": 3204, "name": "_id", "data_type": "string", "description": "A unique identifier for each asset record.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3205, "name": "symbol", "data_type": "string", "description": "The ticker symbol representing the asset (e.g., BTC for Bitcoin).", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3206, "name": "amount", "data_type": "string", "description": "The quantity of the asset owned.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3207, "name": "owner", "data_type": "string", "description": "The identifier of the user who owns the asset.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3208, "name": "createdAt", "data_type": "string", "description": "The timestamp when the asset record was created.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3209, "name": "updatedAt", "data_type": "string", "description": "The timestamp when the asset record was last updated.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3210, "name": "__v", "data_type": "integer", "description": "The version key used by MongoDB for document versioning.", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3211, "name": "symbol", "data_type": "string", "description": "The ticker symbol representing the asset (e.g., BTC for Bitcoin).", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3212, "name": "name", "data_type": "string", "description": "The full name of the asset (e.g., Bitcoin).", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3213, "name": "public<PERSON>ey", "data_type": "string", "description": "The public key associated with the asset for transactions.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3214, "name": "privateSecret", "data_type": "string", "description": "A private secret used for securing the asset.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3215, "name": "baseAddress", "data_type": "object", "description": "The base address associated with the asset.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3216, "name": "wallet", "data_type": "array", "description": "The wallet address where the asset is stored.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3217, "name": "privateKey", "data_type": "string", "description": "The private key used to access the asset.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3218, "name": "eventId", "data_type": "string", "description": "An identifier for the event related to the asset.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}]}, {"id": 348, "table_name": "people", "schema_name": "dev", "status": "active", "description": "This collection stores detailed information about individuals, including their contact details, identification documents, and account information. This table contains the following fields: _id, name, emails, phones, devices, locations, currentCountry, currentEmail, currentPhone, currentDeviceInfo, isSecurityFlagged, idDocuments, addresses, paymentMethods, flags, createdAt, updatedAt, __v, kyc, id, searchId, tierInfo, searchEmail, devices, currentCountry, currentDeviceInfo, limits, externalAccounts, kyc, tierInfo. The purpose of this collection is to manage and track user profiles for various applications, ensuring secure and efficient access to services.", "sample_row": "\"{\\n  \\\"_id\\\": \\\"5f6b3e4a2c3d4e5f67890abc\\\",\\n  \\\"name\\\": {\\n    \\\"firstName\\\": \\\"Jane\\\",\\n    \\\"lastName\\\": \\\"Doe\\\",\\n    \\\"fullName\\\": \\\"Jane Doe\\\",\\n    \\\"userName\\\": \\\"xzy-45\\\"\\n  },\\n  \\\"emails\\\": [\\n    {\\n      \\\"emailAddress\\\": \\\"<EMAIL>\\\",\\n      \\\"emailType\\\": \\\"personal\\\",\\n      \\\"isDefault\\\": true,\\n      \\\"isDeactivated\\\": false\\n    }\\n  ],\\n  \\\"phones\\\": [\\n    {\\n      \\\"phoneNumber\\\": \\\"+*************\\\",\\n      \\\"phoneType\\\": \\\"mobile\\\",\\n      \\\"isDefault\\\": true,\\n      \\\"isDeactivated\\\": false,\\n      \\\"status\\\": \\\"active\\\"\\n    }\\n  ],\\n  \\\"devices\\\": [\\n    {\\n      \\\"isDefault\\\": true,\\n      \\\"isDeactivated\\\": false,\\n      \\\"createdAt\\\": \\\"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\\\",\\n      \\\"updatedAt\\\": \\\"2023-04-25T13:30:00\\\"\\n    }\\n  ],\\n  \\\"locations\\\": [\\n    {\\n      \\\"ipAddress\\\": \\\"***********\\\",\\n      \\\"timestamp\\\": \\\"2023-06-15T10:00:00.000000\\\"\\n    }\\n  ],\\n  \\\"currentCountry\\\": \\\"us\\\",\\n  \\\"currentEmail\\\": \\\"<EMAIL>\\\",\\n  \\\"currentPhone\\\": \\\"+*************\\\",\\n  \\\"currentDeviceInfo\\\": {\\n    \\\"isDefault\\\": true,\\n    \\\"isDeactivated\\\": false,\\n    \\\"createdAt\\\": \\\"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\\\",\\n    \\\"updatedAt\\\": \\\"2023-04-25T13:30:00\\\"\\n  },\\n  \\\"isSecurityFlagged\\\": false,\\n  \\\"idDocuments\\\": [],\\n  \\\"addresses\\\": [],\\n  \\\"paymentMethods\\\": [],\\n  \\\"flags\\\": [],\\n  \\\"createdAt\\\": \\\"2023-04-25T13:30:00.131000\\\",\\n  \\\"updatedAt\\\": \\\"2023-09-20T11:00:00.018000\\\",\\n  \\\"__v\\\": 0,\\n  \\\"kyc\\\": {\\n    \\\"dateOfBirth\\\": \\\"1990-01-01\\\",\\n    \\\"status\\\": \\\"inProgress\\\",\\n    \\\"country\\\": \\\"US\\\"\\n  },\\n  \\\"id\\\": \\\"5f6b3e4a2c3d4e5f67890abc\\\",\\n  \\\"searchId\\\": \\\"5f6b3e4a2c3d4e5f67890abc\\\",\\n  \\\"tierInfo\\\": {\\n    \\\"id\\\": \\\"78g90h1j2k3l4m5n6o7p8q9r\\\",\\n    \\\"transactionCount\\\": 5\\n  },\\n  \\\"searchEmail\\\": \\\"<EMAIL>\\\"\\n}\"", "fields": [{"id": 3219, "name": "_id", "data_type": "string", "description": "A unique identifier for each document in the collection.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3220, "name": "name", "data_type": "object", "description": "The full name of the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3221, "name": "emails", "data_type": "array", "description": "A list of email addresses associated with the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3222, "name": "phones", "data_type": "array", "description": "A list of phone numbers associated with the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3223, "name": "devices", "data_type": "array", "description": "A list of devices used by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3224, "name": "locations", "data_type": "array", "description": "A list of geographical locations associated with the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3225, "name": "currentCountry", "data_type": "string", "description": "The current country of residence of the individual.", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3226, "name": "currentEmail", "data_type": "string", "description": "The primary email address currently in use by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3227, "name": "currentPhone", "data_type": "string", "description": "The primary phone number currently in use by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3228, "name": "currentDeviceInfo", "data_type": "object", "description": "Information about the device currently being used by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3229, "name": "isSecurityFlagged", "data_type": "integer", "description": "A boolean indicating if the individual has been flagged for security reasons.", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3230, "name": "idDocuments", "data_type": "array", "description": "A list of identification documents provided by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3231, "name": "addresses", "data_type": "array", "description": "A list of physical addresses associated with the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3232, "name": "paymentMethods", "data_type": "array", "description": "A list of payment methods associated with the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3233, "name": "flags", "data_type": "array", "description": "A list of flags indicating various statuses or attributes of the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3234, "name": "createdAt", "data_type": "string", "description": "The timestamp when the document was created.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3235, "name": "updatedAt", "data_type": "string", "description": "The timestamp when the document was last updated.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3236, "name": "__v", "data_type": "integer", "description": "The version key for the document, used for versioning.", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3237, "name": "kyc", "data_type": "object", "description": "Duplicate entry for Know Your Customer information.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3238, "name": "id", "data_type": "string", "description": "An alternate identifier for the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3239, "name": "searchId", "data_type": "string", "description": "An identifier used for searching purposes.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3240, "name": "tierInfo", "data_type": "object", "description": "Duplicate entry for service tier information.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3241, "name": "searchEmail", "data_type": "string", "description": "An email address used specifically for search purposes.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3242, "name": "devices", "data_type": "array", "description": "A list of devices used by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3243, "name": "currentCountry", "data_type": "string", "description": "The current country of residence of the individual.", "is_categorical": true, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3244, "name": "currentDeviceInfo", "data_type": "object", "description": "Information about the device currently being used by the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3245, "name": "limits", "data_type": "object", "description": "Limits associated with the individual's account.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3246, "name": "externalAccounts", "data_type": "object", "description": "A list of external accounts linked to the individual.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3247, "name": "kyc", "data_type": "object", "description": "Duplicate entry for Know Your Customer information.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3248, "name": "tierInfo", "data_type": "object", "description": "Duplicate entry for service tier information.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}]}], "connection_params": {"username": "afriex", "password": "9zpYf5qVILceF09C", "cluster_url": "cluster0.uyahz.mongodb.net", "database": "dev", "options": "retryWrites=true&w=majority"}, "db_type": "no_sql"}