{"connection_id": 23, "provider": "postgres", "schemas": [{"table_name": "finance.loan_payments", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is used to track individual payments made towards loans, capturing details about each payment transaction, including the amount paid, the date of payment, and the current status of the payment."}, {"table_name": "finance.loans", "description": "This table contains information about various loans issued to individuals or entities. It tracks the details of each loan, including its type, amount, interest rate, duration, and current status. This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status. The purpose of this table is to manage and monitor loan agreements and their terms effectively."}], "tables": [{"id": 339, "table_name": "finance.loan_payments", "schema_name": "finance", "status": "active", "description": "This table contains the following fields: payment_id, loan_id, amount, payment_date, status. The 'loan_payments' table is used to track individual payments made towards loans, capturing details about each payment transaction, including the amount paid, the date of payment, and the current status of the payment.", "sample_row": "[]", "fields": [{"id": 3159, "name": "payment_id", "data_type": "integer", "description": "A unique identifier for each payment record.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3160, "name": "loan_id", "data_type": "integer", "description": "The identifier for the loan associated with this payment.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3161, "name": "amount", "data_type": "numeric", "description": "The amount of money paid in this transaction.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3162, "name": "payment_date", "data_type": "timestamp without time zone", "description": "The date when the payment was made.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3163, "name": "status", "data_type": "character varying", "description": "The current status of the payment (e.g., completed, pending, failed).", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}]}, {"id": 340, "table_name": "finance.loans", "schema_name": "finance", "status": "active", "description": "This table contains information about various loans issued to individuals or entities. It tracks the details of each loan, including its type, amount, interest rate, duration, and current status. This table contains the following fields: loan_id, loan_type, amount, interest_rate, start_date, end_date, status. The purpose of this table is to manage and monitor loan agreements and their terms effectively.", "sample_row": "[]", "fields": [{"id": 3164, "name": "loan_id", "data_type": "integer", "description": "A unique identifier for each loan record.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3165, "name": "loan_type", "data_type": "character varying", "description": "The type of loan (e.g., personal, mortgage, auto, etc.).", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3166, "name": "amount", "data_type": "numeric", "description": "The total amount of money borrowed in the loan.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3167, "name": "interest_rate", "data_type": "numeric", "description": "The percentage of interest charged on the loan amount.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3168, "name": "start_date", "data_type": "timestamp without time zone", "description": "The date when the loan period begins.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3169, "name": "end_date", "data_type": "timestamp without time zone", "description": "The date when the loan period ends.", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}, {"id": 3170, "name": "status", "data_type": "character varying", "description": "The current status of the loan (e.g., active, paid off, defaulted).", "is_categorical": false, "is_datetime": false, "categorical_values": null, "status": "active"}]}], "connection_params": {"host": "database-1.cw7tvjq91kjw.us-east-1.rds.amazonaws.com", "port": 5432, "database": "ai_copilot", "user": "afriex_samuel", "password": "afri-Sam22*3", "schema": null}, "db_type": "sql"}