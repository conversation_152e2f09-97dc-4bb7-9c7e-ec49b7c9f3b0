{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting psycopg2-binary\n", "  Downloading psycopg2_binary-2.9.10-cp311-cp311-macosx_14_0_arm64.whl.metadata (4.9 kB)\n", "Downloading psycopg2_binary-2.9.10-cp311-cp311-macosx_14_0_arm64.whl (3.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: psycopg2-binary\n", "Successfully installed psycopg2-binary-2.9.10\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install psycopg2-binary\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Schema: public\n", "--------------------------------------------------\n"]}], "source": ["POSTGRES_HOST = \"database-1.cw7tvjq91kjw.us-east-1.rds.amazonaws.com\"\n", "POSTGRES_PASSWORD = \"afri-Sam22*3\"\n", "POSTGRES_PORT = 5432\n", "POSTGRES_USER = \"afriex_samuel\"\n", "\n", "import psycopg2\n", "import psycopg2.extras\n", "\n", "# Create connection\n", "conn = psycopg2.connect(\n", "    host=POSTGRES_HOST,\n", "    database=\"ai_copilot\",  # Default database\n", "    user=POSTGRES_USER,\n", "    password=POSTGRES_PASSWORD,\n", "    port=POSTGRES_PORT\n", ")\n", "\n", "# Create cursor\n", "cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)\n", "\n", "# Get all schemas\n", "cur.execute(\"\"\"\n", "    SELECT schema_name \n", "    FROM information_schema.schemata \n", "    WHERE schema_name NOT IN ('information_schema', 'pg_catalog')\n", "    ORDER BY schema_name;\n", "\"\"\")\n", "schemas = cur.fetchall()\n", "\n", "# Print schemas and their tables\n", "for schema in schemas:\n", "    schema_name = schema['schema_name']\n", "    print(f\"\\nSchema: {schema_name}\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Get tables in this schema\n", "    cur.execute(\"\"\"\n", "        SELECT table_name \n", "        FROM information_schema.tables \n", "        WHERE table_schema = %s\n", "        ORDER BY table_name;\n", "    \"\"\", (schema_name,))\n", "    \n", "    tables = cur.fetchall()\n", "    for table in tables:\n", "        print(f\"- {table['table_name']}\")\n", "\n", "# Close cursor and connection\n", "cur.close()\n", "conn.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "asql", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}