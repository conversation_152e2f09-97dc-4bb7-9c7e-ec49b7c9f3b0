{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mongodb+srv://afriex:<EMAIL>/dev?retryWrites=true&w=majority\n", "\n", "Collections in the database (alphabetically ordered):\n", "- accountings\n", "- accounts\n", "- afriexpaymentmethods\n", "- alltransactions\n", "- apprates\n", "- assets\n", "- bankaccounts\n", "- bankinfos\n", "- bankrates\n", "- binanceorders\n", "- blockeddevices\n", "- blockedpaymentmethods\n", "- bonus\n", "- bulkuploadpayments\n", "- businesses\n", "- bvns\n", "- capabilities\n", "- card_bins\n", "- cellulant_transactions\n", "- client-notifications\n", "- contacts\n", "- countries\n", "- cryptotxes\n", "- currencies\n", "- disputes\n", "- dynamicratepromos\n", "- dynamicratepromousers\n", "- entities\n", "- entityverifications\n", "- escrows\n", "- feature-flags\n", "- high-frequency-receivers\n", "- highfrequencyreceivers\n", "- kycs\n", "- logs\n", "- medici_balances\n", "- medici_journals\n", "- medici_locks\n", "- medici_transactions\n", "- messages\n", "- mobilemoneyinfos\n", "- onboardings\n", "- otc-volumes\n", "- paymentmethods\n", "- payments\n", "- payouts\n", "- pendingpayouts\n", "- pendingtransactions\n", "- people\n", "- plaidtransactions\n", "- pushnotifications\n", "- rate-logs\n", "- rates\n", "- rates-batches\n", "- rates-otcs\n", "- rates-series\n", "- referral-invites\n", "- referral-tiers\n", "- referralcodes\n", "- referrals\n", "- referrals-tiers\n", "- reviews\n", "- rewards\n", "- rewards-cards\n", "- rewards-epoches\n", "- savedcards\n", "- scheduled-transactions\n", "- securitynotes\n", "- selltransactions\n", "- solidcardaccounts\n", "- solidcards\n", "- solidwebhooksecrets\n", "- striperefunds\n", "- telemetries\n", "- testtransactions\n", "- tiers\n", "- tokens\n", "- transaction-fees\n", "- transactions\n", "- transferfiats\n", "- users\n", "- users-devices\n", "- users-referrals\n", "- waitlists\n"]}], "source": ["from pymongo import MongoClient\n", "def construct_mongo_connection_string(username, password, cluster_url, database, options=\"retryWrites=true&w=majority\"):\n", "    return f\"mongodb+srv://{username}:{password}@{cluster_url}/{database}?{options}\"\n", "\n", "# Example usage\n", "username = \"afriex\"\n", "password = \"9zpYf5qVILceF09C\"\n", "cluster_url = \"cluster0.uyahz.mongodb.net\"\n", "database = \"dev\"\n", "\n", "connection_string = construct_mongo_connection_string(username, password, cluster_url, database)\n", "print(connection_string)\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(connection_string)\n", "db = client[database]\n", "\n", "# Get all collection names and sort them alphabetically\n", "collections = sorted(db.list_collection_names())\n", "print(\"\\nCollections in the database (alphabetically ordered):\")\n", "for collection in collections:\n", "    print(f\"- {collection}\")\n", "\n", "# Close the connection\n", "client.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["client = MongoClient(connection_string)\n", "db = client[database]\n", "count = db.bankaccounts.count_documents({\"country\": \"NG\"})"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["x = db.transactions.count_documents({ \"currency\": \"EUR\" })\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "string indices must be integers, not 'str'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[5], line 9\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mrandom\u001b[39;00m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# Randomly select 7 items from the collections\u001b[39;00m\n\u001b[0;32m----> 9\u001b[0m random_collections \u001b[38;5;241m=\u001b[39m random\u001b[38;5;241m.\u001b[39msample(\u001b[43mdatabase_structure\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcollections\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m, \u001b[38;5;241m7\u001b[39m)\n\u001b[1;32m     10\u001b[0m names \u001b[38;5;241m=\u001b[39m [x[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcollection_name\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m random_collections]\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSelected names: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnames\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: string indices must be integers, not 'str'"]}], "source": ["path = \"/Users/<USER>/Desktop/blessing_ai/afriex/sql-copilot/mongodb_schema_analysis.json\"\n", "\n", "with open(path, 'r') as file:\n", "    database_structure = file.read()\n", "\n", "import random\n", "\n", "# Randomly select 7 items from the collections\n", "random_collections = random.sample(database_structure[\"collections\"], 7)\n", "names = [x[\"collection_name\"] for x in random_collections]\n", "print(f\"Selected names: {names}\")\n", "random_collections"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import json\n", "database_structure = json.loads(database_structure)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Selected names: ['bulkuploadpayments', 'high-frequency-receivers', 'medici_journals', 'solidcards', 'selltransactions', 'securitynotes', 'referrals']\n"]}, {"data": {"text/plain": ["[{'database_name': 'dev',\n", "  'collection_name': 'bulkuploadpayments',\n", "  'description': 'This collection stores records of bulk upload payment transactions, tracking their status and associated metadata for processing and reporting purposes.',\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Unique identifier for each payment record.'},\n", "   {'name': 'url',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The URL where the payment data was uploaded from.'},\n", "   {'name': 'successCount',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The number of successful payment transactions processed.'},\n", "   {'name': 'processingCount',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The number of payment transactions currently being processed.',\n", "    'categorical_values': [{'value': '1', 'count': 42},\n", "     {'value': '2', 'count': 19},\n", "     {'value': '5', 'count': 3},\n", "     {'value': '4', 'count': 2},\n", "     {'value': '22', 'count': 1},\n", "     {'value': '3', 'count': 1}]},\n", "   {'name': 'failedCount',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The number of payment transactions that failed during processing.',\n", "    'categorical_values': [{'value': '0', 'count': 59},\n", "     {'value': '1', 'count': 9}]},\n", "   {'name': 'user',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'Identifier of the user who initiated the bulk upload.',\n", "    'categorical_values': [{'value': '620cb979eba0391b49975e5c', 'count': 56},\n", "     {'value': '622b244cc772b1120bd60141', 'count': 11},\n", "     {'value': '6430443eced0e906755eb2d5', 'count': 1}]},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the payment record was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the payment record was last updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Version key for managing document revisions.',\n", "    'categorical_values': [{'value': '0', 'count': 68}]},\n", "   {'name': 'comment',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Optional comments or notes related to the bulk upload.'},\n", "   {'name': 'type',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'Type of payment transaction (e.g., credit, debit, etc.).',\n", "    'categorical_values': [{'value': 'debit', 'count': 24},\n", "     {'value': 'None', 'count': 24},\n", "     {'value': 'credit', 'count': 19},\n", "     {'value': 'cscdscdd', 'count': 1}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"5f3b2c4d7e8f9a0b1c2d3e4f\",\\n  \"url\": \"https://example-bulk-upload-dev.s3.amazonaws.com/5f3b2c4d7e8f9a0b1c2d3e4f-2023-10-01-15-30-45.csv\",\\n  \"successCount\": 5,\\n  \"processingCount\": 3,\\n  \"failedCount\": 1,\\n  \"user\": \"5f3b2c4d7e8f9a0b1c2d3e4f\",\\n  \"createdAt\": \"2023-10-01T14:30:56.911000\",\\n  \"updatedAt\": \"2023-10-01T14:30:56.911000\",\\n  \"__v\": 0\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'high-frequency-receivers',\n", "  'description': '',\n", "  'fields': [],\n", "  'sample_documents': [],\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'medici_journals',\n", "  'description': \"The 'medici_journals' collection stores financial transaction records related to the <PERSON> family's banking activities, including details about each transaction, associated books, and any voided entries.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A unique identifier for each journal entry.'},\n", "   {'name': 'memo',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'A brief note or description of the transaction.',\n", "    'categorical_values': [{'value': 'withdraw', 'count': 258},\n", "     {'value': 'swap', 'count': 239},\n", "     {'value': 'deposit', 'count': 184},\n", "     {'value': 'transfer', 'count': 176},\n", "     {'value': 'withdrawal', 'count': 106},\n", "     {'value': 'Admin Ledger Action', 'count': 31},\n", "     {'value': 'Withdrawal for 60fecadec52fef0dea2e2ad2', 'count': 1},\n", "     {'value': 'Swapping for 6421cc3a5765f9e0ab97c55c from USD to NGN',\n", "      'count': 1},\n", "     {'value': 'Transaction 63c1913babec535ea7c8bd4e Reversed', 'count': 1},\n", "     {'value': 'Withdraw failed', 'count': 1},\n", "     {'value': 'Update wallet balance after payment for 6421cc3a5765f9e0ab97c55c',\n", "      'count': 1},\n", "     {'value': 'Deposit for 6421cc3a5765f9e0ab97c55c', 'count': 1}]},\n", "   {'name': '_transactions',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'An array of transaction details associated with the journal entry.'},\n", "   {'name': 'datetime',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The date and time when the transaction was recorded.'},\n", "   {'name': 'book',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'The name of the financial book or ledger where the transaction is recorded.',\n", "    'categorical_values': [{'value': 'AfriexBook', 'count': 1000}]},\n", "   {'name': 'memo',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'A brief note or description of the transaction.',\n", "    'categorical_values': [{'value': 'withdraw', 'count': 265},\n", "     {'value': 'transfer', 'count': 213},\n", "     {'value': 'swap', 'count': 200},\n", "     {'value': 'deposit', 'count': 176},\n", "     {'value': 'withdrawal', 'count': 116},\n", "     {'value': 'Admin Ledger Action', 'count': 23},\n", "     {'value': '<PERSON><PERSON><PERSON> failed', 'count': 2},\n", "     {'value': 'Update wallet balance after payment for 6421ccba5765f9e0ab97c59b',\n", "      'count': 1},\n", "     {'value': 'Update wallet balance after payment for 643009a0675f8d607d0a129f',\n", "      'count': 1},\n", "     {'value': 'Admin Credit User', 'count': 1},\n", "     {'value': 'Swapping for 6421cc3a5765f9e0ab97c55c from USD to NGN',\n", "      'count': 1},\n", "     {'value': 'Update wallet balance after payment for 6430443eced0e906755eb2d5',\n", "      'count': 1}]},\n", "   {'name': 'void_reason',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'The reason for voiding the transaction, if applicable.',\n", "    'categorical_values': [{'value': 'None', 'count': 998},\n", "     {'value': '<PERSON><PERSON><PERSON> failed', 'count': 2}]},\n", "   {'name': 'voided',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'A boolean indicating whether the transaction has been voided.',\n", "    'categorical_values': [{'value': 'None', 'count': 998},\n", "     {'value': 'True', 'count': 2}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"74d9f1a2d3e4f5678a9b0c1d2\",\\n  \"memo\": \"Withdrawal for 12ab34cd56ef78gh90ij12kl\",\\n  \"_transactions\": [\\n    \"74d9f1a2d3e4f5678a9b0c1d3\",\\n    \"74d9f1a2d3e4f5678a9b0c1d4\"\\n  ],\\n  \"datetime\": \"2023-02-25T09:15:30.123000\",\\n  \"book\": \"DummyBook\"\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'solidcards',\n", "  'description': '',\n", "  'fields': [],\n", "  'sample_documents': [],\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'selltransactions',\n", "  'description': '',\n", "  'fields': [],\n", "  'sample_documents': [],\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'securitynotes',\n", "  'description': \"The 'securitynotes' collection stores user-generated notes related to security incidents, observations, or reminders. It allows users to keep track of important security-related information in a structured format.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The unique identifier for each note document, automatically generated by MongoDB.'},\n", "   {'name': 'user',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The identifier of the user who created the note, linking it to the corresponding user account.'},\n", "   {'name': 'note',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The content of the security note, detailing the specific incident or observation.'},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The timestamp indicating when the note was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The timestamp indicating the last time the note was updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The version key used by MongoDB to manage document revisions.',\n", "    'categorical_values': [{'value': '0', 'count': 1000}]},\n", "   {'name': 'note',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'The content of the security note, detailing the specific incident or observation.',\n", "    'categorical_values': [{'value': 'KYC verified by <PERSON><PERSON><PERSON><PERSON>',\n", "      'count': 590},\n", "     {'value': 'KYC unverified by <PERSON><PERSON><PERSON><PERSON>', 'count': 274},\n", "     {'value': 'KYC documents requested by <PERSON><PERSON><PERSON><PERSON>',\n", "      'count': 11},\n", "     {'value': 'Too many password attempts', 'count': 11},\n", "     {'value': 'Duplicated phone unique ID found: AE57EB3F-6D47-40D8-9BA3-4CE553BF6DF8',\n", "      'count': 10},\n", "     {'value': 'bad kyc', 'count': 9},\n", "     {'value': 'this user was flaged because of bad kyc', 'count': 9},\n", "     {'value': \"email provider 'afriex' is not allowed\", 'count': 7},\n", "     {'value': 'Updated  for user 670f82980d935e1966da2f37 by <PERSON><PERSON><PERSON><PERSON>',\n", "      'count': 7},\n", "     {'value': 'User has been activated by admin', 'count': 6},\n", "     {'value': 'Card Name KYC Name Mismatch', 'count': 5},\n", "     {'value': 'kyc verifed', 'count': 5},\n", "     {'value': 'blocked because of kyc', 'count': 4},\n", "     {'value': 'Duplicated phone unique ID found: BDD40F14-BEA2-4D82-9FBA-232C924E11FE',\n", "      'count': 4},\n", "     {'value': \"User's transaction pin attempts exceeded 3\", 'count': 4},\n", "     {'value': 'Duplicated phone unique ID found: DE849C78-E523-4E49-915A-D80B2ABCC138',\n", "      'count': 4},\n", "     {'value': \"email provider 'example' is not allowed\", 'count': 3},\n", "     {'value': 'test', 'count': 3},\n", "     {'value': 'User has been deactivated by admin', 'count': 3},\n", "     {'value': 'Bad KYC details', 'count': 3},\n", "     {'value': 'Duplicated phone unique ID found: 6B83CE1D-0840-40EE-ADB5-67D545BBF7A2',\n", "      'count': 2},\n", "     {'value': 'Duplicated phone unique ID found: A1CB6103-20CB-41EC-B026-ABF593804FCC',\n", "      'count': 2},\n", "     {'value': 'This card has been reported lost. Please use a different card',\n", "      'count': 2},\n", "     {'value': 'Duplicated phone unique ID found: 4a1e6916b460e9c8',\n", "      'count': 1},\n", "     {'value': 'this user was flaged for having no kyc', 'count': 1},\n", "     {'value': 'Updated name for user 67e658d8f008b01322c4c616 by <PERSON>yebu<PERSON> Valentine Ahiwe',\n", "      'count': 1},\n", "     {'value': 'User role has been updated to user by <PERSON><PERSON><PERSON><PERSON>',\n", "      'count': 1},\n", "     {'value': 'Your provided details may have been incorrect, please check and try again',\n", "      'count': 1},\n", "     {'value': 'pending/failed ACH payment but successful on admin-Reach out to A<PERSON><PERSON>.',\n", "      'count': 1},\n", "     {'value': \"This card doesn't appear to be a debit card.\", 'count': 1},\n", "     {'value': 'adasd', 'count': 1},\n", "     {'value': 'Your card was declined.', 'count': 1},\n", "     {'value': 'Something sus', 'count': 1},\n", "     {'value': 'Updated username for user 67b859d29b3dd612a0f75e58 by <PERSON><PERSON><PERSON><PERSON>',\n", "      'count': 1},\n", "     {'value': 'Transaction is not from user ( to Us test), transactionId: 6736fe417677047931854ad3',\n", "      'count': 1},\n", "     {'value': 'Card location greater than distance threshold: 2567',\n", "      'count': 1},\n", "     {'value': 'this user was blocked because of kyc', 'count': 1},\n", "     {'value': \"email provider 'yopmail' is not allowed\", 'count': 1},\n", "     {'value': 'Fixtures are a great way to mock.', 'count': 1},\n", "     {'value': \"User's card has reached max verification attempts\",\n", "      'count': 1},\n", "     {'value': \"Card location doesn't match\", 'count': 1},\n", "     {'value': 'Updated username for user 66757bb0708af40cac6fc291 by <PERSON><PERSON><PERSON><PERSON>iwe',\n", "      'count': 1},\n", "     {'value': 'test acount', 'count': 1},\n", "     {'value': 'ehm okay', 'count': 1},\n", "     {'value': 'Your card has insufficient funds.', 'count': 1}]},\n", "   {'name': 'type',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'The category or type of the note, which helps in organizing and filtering notes.',\n", "    'categorical_values': [{'value': 'None', 'count': 989},\n", "     {'value': 'card-error', 'count': 8},\n", "     {'value': 'transactional', 'count': 3}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"5f8d0c3e1a2b3c4d5e6f7g8h\",\\n  \"user\": \"7a8b9c0d1e2f3g4h5i6j7k8l\",\\n  \"note\": \"Card location greater than distance threshold: 987\",\\n  \"createdAt\": \"2022-09-15T14:32:45.456000\",\\n  \"updatedAt\": \"2022-09-15T14:32:45.456000\",\\n  \"__v\": 0\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'referrals',\n", "  'description': \"The 'referrals' collection stores information about referral transactions between users, including details about the users involved, the amounts referred, and the status of the referral.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Unique identifier for each referral record.'},\n", "   {'name': 'fromUserName',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The username of the user who made the referral.'},\n", "   {'name': 'to<PERSON><PERSON><PERSON><PERSON>',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The username of the user who is the recipient of the referral.'},\n", "   {'name': 'to<PERSON><PERSON>',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The full name of the recipient user.'},\n", "   {'name': 'to<PERSON><PERSON>',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The phone number of the recipient user.'},\n", "   {'name': 'toUserId',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The unique identifier of the recipient user.'},\n", "   {'name': 'fromUserId',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The unique identifier of the user who made the referral.'},\n", "   {'name': 'cumulativeTransactionAmount',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The total amount of transactions associated with this referral.'},\n", "   {'name': 'isFromStaff',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Indicates whether the referral was made by a staff member.',\n", "    'categorical_values': [{'value': 'False', 'count': 73},\n", "     {'value': 'True', 'count': 16}]},\n", "   {'name': 'status',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'The current status of the referral (e.g., pending, completed, cancelled).',\n", "    'categorical_values': [{'value': 'joined', 'count': 74},\n", "     {'value': 'resolved', 'count': 9},\n", "     {'value': 'transacted', 'count': 5},\n", "     {'value': 'qualified', 'count': 1}]},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the referral was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating the last time the referral record was updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Version key for the document, used by Mongoose for versioning.',\n", "    'categorical_values': [{'value': '0', 'count': 89}]},\n", "   {'name': 'fromAmount',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The amount referred by the user who made the referral.',\n", "    'categorical_values': [{'value': '5', 'count': 74},\n", "     {'value': 'None', 'count': 15}]},\n", "   {'name': 'to<PERSON><PERSON>',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The amount that the recipient is expected to receive.',\n", "    'categorical_values': [{'value': '5', 'count': 73},\n", "     {'value': 'None', 'count': 15},\n", "     {'value': '20', 'count': 1}]},\n", "   {'name': 'isToFulfilled',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Indicates whether the referral has been fulfilled by the recipient.',\n", "    'categorical_values': [{'value': 'None', 'count': 80},\n", "     {'value': 'True', 'count': 9}]},\n", "   {'name': 'yearFulfilled',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'The year in which the referral was fulfilled.',\n", "    'categorical_values': [{'value': 'None', 'count': 81},\n", "     {'value': '2023', 'count': 7},\n", "     {'value': '2024', 'count': 1}]},\n", "   {'name': 'isFromFulfilled',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Indicates whether the referral has been fulfilled by the referring user.',\n", "    'categorical_values': [{'value': 'None', 'count': 80},\n", "     {'value': 'True', 'count': 9}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\\n  \"fromUserName\": \"user12345\",\\n  \"toUserName\": \"user67890\",\\n  \"toName\": \"Sample Company\",\\n  \"toPhone\": \"+2349012345678\",\\n  \"toUserId\": \"a1b2c3d4e5f67890abcdef13\",\\n  \"fromUserId\": \"a1b2c3d4e5f67890abcdef14\",\\n  \"cumulativeTransactionAmount\": 0,\\n  \"isFromStaff\": false,\\n  \"status\": \"joined\",\\n  \"createdAt\": \"2023-11-01T10:00:00.000000\",\\n  \"updatedAt\": \"2023-11-01T10:00:00.000000\",\\n  \"__v\": 0\\n}',\n", "  'status': 'deactivated'}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Randomly select 7 items from the collections\n", "random_collections = random.sample(database_structure[\"collections\"], 7)\n", "names = [x[\"collection_name\"] for x in random_collections]\n", "print(f\"Selected names: {names}\")\n", "random_collections"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import json\n", "database_structure = json.loads(database_structure)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Selected names: ['businesses', 'dynamicratepromousers', 'rates-batches', 'referral-tiers', 'people', 'contacts', 'highfrequencyreceivers']\n"]}, {"data": {"text/plain": ["[{'database_name': 'dev',\n", "  'collection_name': 'businesses',\n", "  'description': 'This collection stores information about various businesses, including their identification, status, and metadata for management and verification purposes.',\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A unique identifier for each business document, automatically generated by MongoDB.'},\n", "   {'name': 'name',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The name of the business, representing its brand or trade name.'},\n", "   {'name': 'countryCode',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A code representing the country where the business is located, typically following ISO 3166-1 alpha-2 standards.'},\n", "   {'name': 'isDeactivated',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A boolean flag indicating whether the business is currently inactive or deactivated.'},\n", "   {'name': 'isVerified',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A boolean flag indicating whether the business has been verified by the platform or authority.'},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A timestamp indicating when the business record was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A timestamp indicating the last time the business record was updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A version key used by MongoDB to track document revisions.'}],\n", "  'sample_documents': '{\\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\\n  \"name\": \"ExampleCorp, llc\",\\n  \"countryCode\": \"US\",\\n  \"isDeactivated\": true,\\n  \"isVerified\": true,\\n  \"createdAt\": \"2025-02-15T09:30:00.000000\",\\n  \"updatedAt\": \"2025-02-15T09:30:00.000000\",\\n  \"__v\": 1\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'dynamicratepromousers',\n", "  'description': 'This collection stores information about users who are eligible for dynamic rate promotions, including their identifiers and timestamps for record management.',\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The unique identifier for each document in the collection, automatically generated by MongoDB.'},\n", "   {'name': 'userId',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The unique identifier of the user associated with the dynamic rate promotion.'},\n", "   {'name': 'dynamicRatePromoId',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The identifier for the specific dynamic rate promotion that the user is eligible for.'},\n", "   {'name': '<PERSON><PERSON><PERSON>',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The identifier of the user or system that created the record.'},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The timestamp indicating when the record was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The timestamp indicating when the record was last updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The version key used by MongoDB to manage document revisions.'}],\n", "  'sample_documents': '{\\n  \"_id\": \"5f6e7f9300d37e707bf12345\",\\n  \"userId\": \"5f6e3ff38c77025ef255c678\",\\n  \"dynamicRatePromoId\": \"5f6e2e0c00d37e707bf98765\",\\n  \"createdBy\": \"5eb2bd50718246038e2d00ab\",\\n  \"createdAt\": \"2023-06-15T10:20:45.123000\",\\n  \"updatedAt\": \"2023-06-15T10:20:45.123000\",\\n  \"__v\": 0\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'rates-batches',\n", "  'description': \"The 'rates-batches' collection stores information about batches of rates used for financial transactions, including details about tolerance levels, links to rates data, and administrative oversight.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Unique identifier for each document in the collection.'},\n", "   {'name': 'admin',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Identifier for the administrator responsible for the batch.',\n", "    'nested_fields': [{'name': 'admin.id',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '622b244cc772b1120bd60141',\n", "        'count': 359},\n", "       {'value': '6430443eced0e906755eb2d5', 'count': 2}]},\n", "     {'name': 'admin.name',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '<PERSON><PERSON><PERSON><PERSON>', 'count': 359},\n", "       {'value': 'Us', 'count': 2}]},\n", "     {'name': 'admin.role',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'finance.manager', 'count': 333},\n", "       {'value': 'operations.manager', 'count': 15},\n", "       {'value': 'finance.member', 'count': 7},\n", "       {'value': 'engineering.manager', 'count': 5},\n", "       {'value': 'growth.manager', 'count': 1}]}]},\n", "   {'name': 'tolerance',\n", "    'data_type': 'float',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The acceptable tolerance level for the rates in this batch.'},\n", "   {'name': 'ratesLink',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'URL or reference link to the source of the rates data.'},\n", "   {'name': 'arbitrageInfo',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Details regarding any arbitrage opportunities related to the rates in this batch.',\n", "    'nested_fields': [{'name': 'arbitrageInfo.amounts',\n", "      'data_type': 'array',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'arbitrageInfo.currencies',\n", "      'data_type': 'array',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'arbitrageInfo.totalAmount',\n", "      'data_type': 'float',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'arbitrageInfo.profit',\n", "      'data_type': 'float',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'arbitrageInfo.profitPercentage',\n", "      'data_type': 'float',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''}]},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the batch was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating the last time the batch was updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Version key used by <PERSON><PERSON><PERSON> to track document revisions.',\n", "    'categorical_values': [{'value': '0', 'count': 361}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\\n  \"admin\": {\\n    \"id\": \"f1e2d3c4b5a6789012345678\",\\n    \"name\": \"<PERSON>\",\\n    \"role\": \"marketing.specialist\"\\n  },\\n  \"tolerance\": 0.45,\\n  \"ratesLink\": \"rates-2024-01-15T09:30:00.000Z.csv\",\\n  \"arbitrageInfo\": {\\n    \"amounts\": [\\n      1234.5678901,\\n      0.987654321,\\n      0.123456789012,\\n      0.4567\\n    ],\\n    \"currencies\": [\\n      \"EUR\",\\n      \"JPY\",\\n      \"CAD\",\\n      \"AUD\",\\n      \"EUR\"\\n    ],\\n    \"totalAmount\": 1.2345678901234567,\\n    \"profit\": 0.0345678901234567,\\n    \"profitPercentage\": 0.34567890123456789\\n  },\\n  \"createdAt\": \"2024-01-15T09:30:00.000000\",\\n  \"updatedAt\": \"2024-01-15T09:30:00.000000\",\\n  \"__v\": 0\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'referral-tiers',\n", "  'description': '',\n", "  'fields': [],\n", "  'sample_documents': [],\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'people',\n", "  'description': \"The 'people' collection stores information about individuals, including their personal details, contact information, and security status. It is used for managing user profiles and facilitating communication and transactions within the application.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Unique identifier for each document in the collection.'},\n", "   {'name': 'name',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Full name of the individual.',\n", "    'nested_fields': [{'name': 'name.firstName',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'name.lastName',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'name.fullName',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'name.userName',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''}]},\n", "   {'name': 'emails',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of email addresses associated with the individual.',\n", "    'array_items': [{'name': 'emails[].emailAddress',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'emails[].emailType',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'emails[].isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'emails[].isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]}]},\n", "   {'name': 'phones',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of phone numbers associated with the individual.',\n", "    'array_items': [{'name': 'phones[].phoneNumber',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'phones[].phoneType',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'phones[].isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'phones[].isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'phones[].status',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]}]},\n", "   {'name': 'devices',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of devices used by the individual.',\n", "    'array_items': [{'name': 'devices[].isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].createdAt',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].updatedAt',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]}]},\n", "   {'name': 'locations',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of geographical locations associated with the individual.',\n", "    'array_items': [{'name': 'locations[].ipAddress',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'locations[].timestamp',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]}]},\n", "   {'name': 'currentCountry',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'Country where the individual is currently located.',\n", "    'categorical_values': [{'value': 'ng', 'count': 273},\n", "     {'value': 'NG', 'count': 189},\n", "     {'value': 'US', 'count': 145},\n", "     {'value': 'us', 'count': 92},\n", "     {'value': 'KE', 'count': 54},\n", "     {'value': 'GB', 'count': 52},\n", "     {'value': 'CA', 'count': 51},\n", "     {'value': 'GH', 'count': 39},\n", "     {'value': 'UG', 'count': 39},\n", "     {'value': 'FR', 'count': 14},\n", "     {'value': 'gb', 'count': 11},\n", "     {'value': 'gh', 'count': 8},\n", "     {'value': 'CI', 'count': 5},\n", "     {'value': 'ET', 'count': 3},\n", "     {'value': 'BE', 'count': 3},\n", "     {'value': 'ca', 'count': 3},\n", "     {'value': 'RW', 'count': 3},\n", "     {'value': 'DE', 'count': 3},\n", "     {'value': 'IN', 'count': 2},\n", "     {'value': 'ke', 'count': 2},\n", "     {'value': 'EG', 'count': 2},\n", "     {'value': 'HT', 'count': 1},\n", "     {'value': 'cm', 'count': 1},\n", "     {'value': 'MG', 'count': 1},\n", "     {'value': 'ug', 'count': 1},\n", "     {'value': 'ZA', 'count': 1},\n", "     {'value': 'MZ', 'count': 1},\n", "     {'value': 'MW', 'count': 1}]},\n", "   {'name': 'currentEmail',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The primary email address currently in use by the individual.'},\n", "   {'name': 'currentP<PERSON>',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'The primary phone number currently in use by the individual.'},\n", "   {'name': 'currentDeviceInfo',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Information about the device currently being used by the individual.',\n", "    'nested_fields': [{'name': 'currentDeviceInfo.isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'True', 'count': 767},\n", "       {'value': 'None', 'count': 233}]},\n", "     {'name': 'currentDeviceInfo.isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'False', 'count': 736},\n", "       {'value': 'None', 'count': 264}]},\n", "     {'name': 'currentDeviceInfo.createdAt',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'currentDeviceInfo.updatedAt',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''}]},\n", "   {'name': 'isSecurityFlagged',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Boolean indicating if the individual has been flagged for security reasons.',\n", "    'categorical_values': [{'value': 'False', 'count': 921},\n", "     {'value': 'True', 'count': 79}]},\n", "   {'name': 'idDocuments',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of identification documents submitted by the individual.'},\n", "   {'name': 'addresses',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of physical addresses associated with the individual.'},\n", "   {'name': 'paymentMethods',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': \"Array of payment methods linked to the individual's account.\"},\n", "   {'name': 'flags',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of flags indicating various statuses or attributes of the individual.'},\n", "   {'name': 'createdAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the document was created.'},\n", "   {'name': 'updatedAt',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Timestamp indicating when the document was last updated.'},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Version key for document revision control.',\n", "    'categorical_values': [{'value': '0', 'count': 999},\n", "     {'value': 'None', 'count': 1}]},\n", "   {'name': 'kyc',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Know Your Customer information related to the individual.',\n", "    'nested_fields': [{'name': 'kyc.dateOfBirth',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'kyc.status',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'notStarted', 'count': 675},\n", "       {'value': 'pending', 'count': 167},\n", "       {'value': 'success', 'count': 68},\n", "       {'value': 'started', 'count': 41},\n", "       {'value': 'submitted', 'count': 34},\n", "       {'value': 'failure', 'count': 8},\n", "       {'value': 'rejected', 'count': 7}]},\n", "     {'name': 'kyc.country',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 431},\n", "       {'value': 'NG', 'count': 339},\n", "       {'value': 'US', 'count': 138},\n", "       {'value': 'GB', 'count': 28},\n", "       {'value': 'GH', 'count': 19},\n", "       {'value': 'KE', 'count': 19},\n", "       {'value': 'CA', 'count': 11},\n", "       {'value': 'UG', 'count': 10},\n", "       {'value': 'CY', 'count': 1},\n", "       {'value': 'HT', 'count': 1},\n", "       {'value': 'FR', 'count': 1},\n", "       {'value': 'IT', 'count': 1},\n", "       {'value': 'ZA', 'count': 1}]}]},\n", "   {'name': 'id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A secondary identifier for the individual.'},\n", "   {'name': 'searchId',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Identifier used for searching the individual in the system.'},\n", "   {'name': 'tierInfo',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Information about the tier or level of service associated with the individual.',\n", "    'nested_fields': [{'name': 'tierInfo.id',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '64f48c7f2fe5f0cadb400a5d',\n", "        'count': 611},\n", "       {'value': 'None', 'count': 282},\n", "       {'value': '66a364f60e43d1eb8d76f4d0', 'count': 55},\n", "       {'value': '66a367980e43d1eb8d76f4f8', 'count': 36},\n", "       {'value': '64f710f05906287b1add9cd5', 'count': 4},\n", "       {'value': '652f920b3ee37bd2aad5fa3d', 'count': 4},\n", "       {'value': '6572375ca1021c763b660771', 'count': 3},\n", "       {'value': '64f48d4d1137aa9518b00e91', 'count': 3},\n", "       {'value': '654b539b928b15fb8abacf07', 'count': 1},\n", "       {'value': '67d1a26bbc36bc4c28522dd2', 'count': 1}]},\n", "     {'name': 'tierInfo.transactionCount',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '0', 'count': 633},\n", "       {'value': 'None', 'count': 265},\n", "       {'value': '3', 'count': 93},\n", "       {'value': '2', 'count': 4},\n", "       {'value': '1000', 'count': 2},\n", "       {'value': '4', 'count': 1},\n", "       {'value': '987', 'count': 1},\n", "       {'value': '948', 'count': 1}]}]},\n", "   {'name': 'searchEmail',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Email address used for searching the individual.'},\n", "   {'name': 'devices',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of devices used by the individual.',\n", "    'array_items': [{'name': 'devices[].deviceId',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].deviceType',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].deviceToken',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].createdAt',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]},\n", "     {'name': 'devices[].updatedAt',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 1000}]}]},\n", "   {'name': 'currentCountry',\n", "    'data_type': 'string',\n", "    'is_categorical': True,\n", "    'description': 'Country where the individual is currently located.',\n", "    'categorical_values': [{'value': 'ng', 'count': 284},\n", "     {'value': 'NG', 'count': 180},\n", "     {'value': 'US', 'count': 153},\n", "     {'value': 'us', 'count': 99},\n", "     {'value': 'KE', 'count': 51},\n", "     {'value': 'CA', 'count': 50},\n", "     {'value': 'UG', 'count': 44},\n", "     {'value': 'GB', 'count': 39},\n", "     {'value': 'GH', 'count': 35},\n", "     {'value': 'FR', 'count': 18},\n", "     {'value': 'gb', 'count': 13},\n", "     {'value': 'gh', 'count': 7},\n", "     {'value': 'CM', 'count': 5},\n", "     {'value': 'ET', 'count': 5},\n", "     {'value': 'CI', 'count': 3},\n", "     {'value': 'DE', 'count': 3},\n", "     {'value': 'BE', 'count': 2},\n", "     {'value': 'ca', 'count': 1},\n", "     {'value': 'ug', 'count': 1},\n", "     {'value': 'ZA', 'count': 1},\n", "     {'value': 'ke', 'count': 1},\n", "     {'value': 'RO', 'count': 1},\n", "     {'value': 'TZ', 'count': 1},\n", "     {'value': 'MW', 'count': 1},\n", "     {'value': 'HT', 'count': 1},\n", "     {'value': 'MZ', 'count': 1}]},\n", "   {'name': 'currentDeviceInfo',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Information about the device currently being used by the individual.',\n", "    'nested_fields': [{'name': 'currentDeviceInfo.deviceId',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'currentDeviceInfo.deviceType',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 494},\n", "       {'value': 'ios', 'count': 413},\n", "       {'value': 'android', 'count': 86},\n", "       {'value': '', 'count': 7}]},\n", "     {'name': 'currentDeviceInfo.deviceToken',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'currentDeviceInfo.isDefault',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'True', 'count': 762},\n", "       {'value': 'None', 'count': 238}]},\n", "     {'name': 'currentDeviceInfo.isDeactivated',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'False', 'count': 758},\n", "       {'value': 'None', 'count': 242}]},\n", "     {'name': 'currentDeviceInfo.createdAt',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'currentDeviceInfo.updatedAt',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''}]},\n", "   {'name': 'limits',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': \"Limits associated with the individual's account, such as transaction limits.\",\n", "    'nested_fields': [{'name': 'limits.perDailyLimit',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '3000', 'count': 738},\n", "       {'value': 'None', 'count': 76},\n", "       {'value': '0', 'count': 52},\n", "       {'value': '5000', 'count': 50},\n", "       {'value': '1000', 'count': 38},\n", "       {'value': '1200', 'count': 37},\n", "       {'value': '100', 'count': 3},\n", "       {'value': '*********', 'count': 2},\n", "       {'value': '50', 'count': 2},\n", "       {'value': '50000', 'count': 1},\n", "       {'value': '300000', 'count': 1}]},\n", "     {'name': 'limits.perTransactionLimit',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '1000', 'count': 786},\n", "       {'value': 'None', 'count': 86},\n", "       {'value': '400', 'count': 79},\n", "       {'value': '0', 'count': 45},\n", "       {'value': '10', 'count': 1},\n", "       {'value': '100000', 'count': 1},\n", "       {'value': '5', 'count': 1},\n", "       {'value': '1000000', 'count': 1}]}]},\n", "   {'name': 'externalAccounts',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Array of external accounts linked to the individual.',\n", "    'nested_fields': [{'name': 'externalAccounts.stripe',\n", "      'data_type': 'object',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': '',\n", "      'nested_fields': [{'name': 'externalAccounts.stripe.customerId',\n", "        'data_type': 'string',\n", "        'is_categorical': <PERSON><PERSON><PERSON>,\n", "        'description': ''},\n", "       {'name': 'externalAccounts.stripe.accountId',\n", "        'data_type': 'string',\n", "        'is_categorical': <PERSON><PERSON><PERSON>,\n", "        'description': ''}]}]},\n", "   {'name': 'isSecurityFlagged',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Boolean indicating if the individual has been flagged for security reasons.',\n", "    'categorical_values': [{'value': 'False', 'count': 934},\n", "     {'value': 'True', 'count': 66}]},\n", "   {'name': '__v',\n", "    'data_type': 'integer',\n", "    'is_categorical': True,\n", "    'description': 'Version key for document revision control.',\n", "    'categorical_values': [{'value': '0', 'count': 1000}]},\n", "   {'name': 'kyc',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Know Your Customer information related to the individual.',\n", "    'nested_fields': [{'name': 'kyc.dateOfBirth',\n", "      'data_type': 'string',\n", "      'is_categorical': <PERSON><PERSON><PERSON>,\n", "      'description': ''},\n", "     {'name': 'kyc.status',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'notStarted', 'count': 661},\n", "       {'value': 'pending', 'count': 177},\n", "       {'value': 'success', 'count': 65},\n", "       {'value': 'started', 'count': 39},\n", "       {'value': 'submitted', 'count': 37},\n", "       {'value': 'rejected', 'count': 9},\n", "       {'value': 'failure', 'count': 7},\n", "       {'value': 'reuploadRequested', 'count': 2},\n", "       {'value': 'verified', 'count': 1},\n", "       {'value': 'None', 'count': 1},\n", "       {'value': 'reuploaded', 'count': 1}]},\n", "     {'name': 'kyc.country',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': 'None', 'count': 415},\n", "       {'value': 'NG', 'count': 346},\n", "       {'value': 'US', 'count': 149},\n", "       {'value': 'GB', 'count': 25},\n", "       {'value': 'KE', 'count': 20},\n", "       {'value': 'CA', 'count': 20},\n", "       {'value': 'GH', 'count': 11},\n", "       {'value': 'UG', 'count': 10},\n", "       {'value': 'HT', 'count': 1},\n", "       {'value': 'ZA', 'count': 1},\n", "       {'value': 'CM', 'count': 1},\n", "       {'value': 'FR', 'count': 1}]}]},\n", "   {'name': 'tierInfo',\n", "    'data_type': 'object',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'Information about the tier or level of service associated with the individual.',\n", "    'nested_fields': [{'name': 'tierInfo.id',\n", "      'data_type': 'string',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '64f48c7f2fe5f0cadb400a5d',\n", "        'count': 637},\n", "       {'value': 'None', 'count': 270},\n", "       {'value': '66a364f60e43d1eb8d76f4d0', 'count': 67},\n", "       {'value': '66a367980e43d1eb8d76f4f8', 'count': 19},\n", "       {'value': '652f920b3ee37bd2aad5fa3d', 'count': 2},\n", "       {'value': '64f48d4d1137aa9518b00e91', 'count': 2},\n", "       {'value': '654b539b928b15fb8abacf07', 'count': 2},\n", "       {'value': '67d1a26bbc36bc4c28522dd2', 'count': 1}]},\n", "     {'name': 'tierInfo.transactionCount',\n", "      'data_type': 'integer',\n", "      'is_categorical': True,\n", "      'description': '',\n", "      'categorical_values': [{'value': '0', 'count': 646},\n", "       {'value': 'None', 'count': 257},\n", "       {'value': '3', 'count': 92},\n", "       {'value': '2', 'count': 3},\n", "       {'value': '1000', 'count': 1},\n", "       {'value': '999', 'count': 1}]}]}],\n", "  'sample_documents': '{\\n  \"_id\": \"a1b2c3d4e5f6789012345678\",\\n  \"name\": {\\n    \"firstName\": \"<PERSON>\",\\n    \"lastName\": \"<PERSON><PERSON>\",\\n    \"fullName\": \"<PERSON>\",\\n    \"userName\": \"user-1234\"\\n  },\\n  \"emails\": [\\n    {\\n      \"emailAddress\": \"<EMAIL>\",\\n      \"emailType\": \"personal\",\\n      \"isDefault\": true,\\n      \"isDeactivated\": false\\n    }\\n  ],\\n  \"phones\": [\\n    {\\n      \"phoneNumber\": \"+12345678901\",\\n      \"phoneType\": \"mobile\",\\n      \"isDefault\": true,\\n      \"isDeactivated\": false,\\n      \"status\": \"active\"\\n    }\\n  ],\\n  \"devices\": [\\n    {\\n      \"isDefault\": true,\\n      \"isDeactivated\": false,\\n      \"createdAt\": \"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\",\\n      \"updatedAt\": \"2023-04-25T13:30:00\"\\n    }\\n  ],\\n  \"locations\": [\\n    {\\n      \"ipAddress\": \"***********\",\\n      \"timestamp\": \"2023-06-15T10:00:00.000000\"\\n    }\\n  ],\\n  \"currentCountry\": \"us\",\\n  \"currentEmail\": \"<EMAIL>\",\\n  \"currentPhone\": \"+12345678901\",\\n  \"currentDeviceInfo\": {\\n    \"isDefault\": true,\\n    \"isDeactivated\": false,\\n    \"createdAt\": \"Tue Apr 25 2023 14:30:00 GMT+0100 (West Africa Standard Time)\",\\n    \"updatedAt\": \"2023-04-25T13:30:00\"\\n  },\\n  \"isSecurityFlagged\": false,\\n  \"idDocuments\": [],\\n  \"addresses\": [],\\n  \"paymentMethods\": [],\\n  \"flags\": [],\\n  \"createdAt\": \"2023-04-25T13:30:00.131000\",\\n  \"updatedAt\": \"2023-09-15T11:00:00.018000\",\\n  \"__v\": 0,\\n  \"kyc\": {\\n    \"dateOfBirth\": \"null\",\\n    \"status\": \"notStarted\",\\n    \"country\": \"US\"\\n  },\\n  \"id\": \"a1b2c3d4e5f6789012345678\",\\n  \"searchId\": \"a1b2c3d4e5f6789012345678\",\\n  \"tierInfo\": {\\n    \"id\": \"abcdef1234567890abcdef12\",\\n    \"transactionCount\": 0\\n  },\\n  \"searchEmail\": \"<EMAIL>\"\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'contacts',\n", "  'description': \"The 'contacts' collection stores information about individuals or organizations that users may want to keep track of, including their contact details and associated metadata.\",\n", "  'fields': [{'name': '_id',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A unique identifier for each contact record, automatically generated by MongoDB.'},\n", "   {'name': 'hash',\n", "    'data_type': 'string',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'A hash value used for quick lookup or verification of contact data, ensuring data integrity.'},\n", "   {'name': 'contacts',\n", "    'data_type': 'array',\n", "    'is_categorical': <PERSON><PERSON><PERSON>,\n", "    'description': 'An array of contact objects, each containing details such as name, phone number, email address, and other relevant information.'}],\n", "  'sample_documents': '{\\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\\n  \"hash\": \"f1e2d3c4b5a69788abcdef1234567890abcdef1234567890abcdef12345678\",\\n  \"contacts\": [\\n    \"1234567890abcdef12345678\",\\n    \"abcdef1234567890abcdef12\",\\n    \"fedcba0987654321abcdef12\"\\n  ]\\n}',\n", "  'status': 'deactivated'},\n", " {'database_name': 'dev',\n", "  'collection_name': 'highfrequencyreceivers',\n", "  'description': '',\n", "  'fields': [],\n", "  'sample_documents': [],\n", "  'status': 'deactivated'}]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "\n", "# Randomly select 7 items from the collections\n", "random_collections = random.sample(database_structure[\"collections\"], 7)\n", "names = [x[\"collection_name\"] for x in random_collections]\n", "print(f\"Selected names: {names}\")\n", "random_collections"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "# Get current date and time in UTC\n", "utc_now = datetime.now(pytz.utc)\n", "# Format the current date and time in words\n", "current_date = utc_now.strftime(\"%A, %B %d, %Y at %I:%M %p UTC\")\n", "\n", "def get_query_generator_prompt():\n", "    return f\"\"\"\n", "     You are SQL-COPILOT SPECIFICALLY BUILT FOR AFRIEX DATABASE IN AN AGENTIC multi-agent system designed to process database queries. Your primary responsibility is to generate MongoDB queries in Python syntax based on the fully interpreted user question.\n", "    \n", "    \n", "    2. **Syntax Formatting:**\n", "       - Use PyMongo syntax for MongoDB queries\n", "       - For field references, use dot notation for nested documents\n", "       - Example:\n", "         ```python\n", "         db.collection_name.find(\n", "             {\"field\": \"value\"},\n", "             {\"_id\": 0, \"name\": 1, \"email\": 1}\n", "         )\n", "         ```\n", "         \n", "         Or for aggregation:\n", "         ```python\n", "         db.collection_name.aggregate([\n", "             {\"$match\": {\"field\": \"value\"}},\n", "             {\"$project\": {\"_id\": 0, \"name\": 1, \"email\": 1}}\n", "         ])\n", "         ```\n", "          RETURN THE FULL MONGO-DB- QUERY STRING AND ENSURE IT IS CORRECT\n", "    3. **Query Restrictions:**\n", "       - Only generate queries for data retrieval (find, aggregate, etc.). Avoid delete, update, or insert operations or accessing sensitive information like user passwords.\n", "       - Validate that referenced fields exist in the database schema.\n", "\n", "    4. **Database Structure:**\n", "       - Carefully analyze the structure of the database, including collections, fields, and embedded documents.\n", "       - Use $lookup in aggregation pipelines if necessary to join collections for accurate results.\n", "\n", "    5 **Document Data Handling:**\n", "       - If querying nested documents or arrays, use proper dot notation and array operators.\n", "       - Properly handle different data types to ensure accurate querying.\n", "\n", "    6. **Response Modes:**\n", "       - **Direct Response Mode:** Provide a concise numerical or textual answer directly.\n", "       - **File Mode:** Generate a file for large datasets. Include a message notifying users about potentially long processing times.\n", "       - **Analytics Mode:** Generate plots using Python (pandas, matplotlib) when analysis is required. The function must always be named `plot_data`.\n", "\n", "    7. **Output Format:**\n", "       - Return a dictionary with the following keys:\n", "        \n", "         - `mode`: `\"direct\"`, `\"file\"`, or `\"analytics\"` depending on the response type. Do not set to file mode when you are still asking questions, follow strictly.\n", "         - `reason`: A reason for disallowance (empty string if allowed).\n", "         - `generated_query`: The generated MongoDB Python query.\n", "         - `filemodecols`: Field names for **both File Mode and Analytics Mode** outputs in correct order. These will be used to load the data into a dataframe.\n", "         - `plot_code`: Python code for generating plots (only in analytics mode).\n", "         - `is_account_statement`: True or False: if question has to do with user account statement\n", "      \n", "    8. **Additional Notes:**\n", "       - Avoid code blocks (```) in responses.\n", "       - Always round numerical values to 2 decimal places unless specified otherwise.\n", "       - Ensure the queried data matches the `filemodecols` for proper dataframe loading in both File Mode and Analytics Mode.\n", "       - For time-related queries, use the current date as a reference and avoid assumptions about specific dates or periods.\n", "       - Creatively generate queries by analyzing unique values and mixed data types (e.g., nested documents and arrays), adhering strictly to the provided database structure.\n", "       - In analytics mode, keep plots simple, insightful, and free from unnecessary complexity.\n", "       - Only use available collections; do not create new ones.\n", "\n", "    9. **Plot Code Requirements (For Analytics Mode):**\n", "       - Use pandas and matplotlib to process the dataframe and generate a plot.\n", "       - Save the plot in the `static/plots/` directory with a unique filename using `uuid`.\n", "       - Return the path to the saved plot file.\n", "       \n", "    10. **MongoDB Specific Considerations:**\n", "       - Use proper MongoDB operators ($eq, $gt, $lt, $in, etc.)\n", "       - Convert date strings to proper MongoDB date objects when needed\n", "       - Use proper indexing references for arrays\n", "       - <PERSON><PERSON><PERSON><PERSON><PERSON> LOOK AT THE EXAMPLES QUERY AND USE THEM WHEN NECESSARY AS THEY ARE ACCURATE QUERY FOR THOSE QUESTIONS SEEN\n", "\n", "    **Examples:**\n", "    - **Direct Response Mode:** \n", "      User asks, \"How many customers were registered today?\" → Provide a direct numerical answer.\n", "    - **File Mode:** \n", "      User requests a list of customers → Generate a file with appropriate field names. Include `filemodecols` for dataframe compatibility.\n", "    - **Analytics Mode:** \n", "      User asks for a trend analysis → Generate a plot using the `plot_data` function. Include `filemodecols` for dataframe compatibility.\n", "\n", "    11. Ensure consistency in the response of your query especially if user is asking the same question, make sure you are consistent with your answer and MongoDB query you generate.\n", "    \n", "    **Final Note:**\n", "    Ensure all queries are valid, efficient, and aligned with the user's request. Always prioritize clarity and accuracy in both queries and responses. Remember, `filemodecols` must be included for both **File Mode** and **Analytics Mode** to ensure proper dataframe generation.\n", "    \n", "    NOTE: YOU MAY BE INTEGRATED ON OTHER PLATFORMS LIKE SLACK ETC, IF THAT IS THE CASE, YOU WILL BE PROVIDED WITH USER QUERIES HISTORY TO THE BOT FROM OLDEST TO LATEST AND USER CURRENT QUESTION FOR HELPFUL CONVERSATIONAL FLOW WITHIN THE SAME THREAD OR CHAT PAGE.\n", "    IF PLATFORM IS SLACK: THE CONVERSATION HISTORY IS REFERRING TO CURRENT THREAD.\n", "    \n", "    NOTE: ONLY SELECT FILE MODE WHEN NECESSARY, FOR EXAMPLE SOME DATA CAN BE EASILY SHOWN TO THE USER INSTEAD OF SENDING FILE.\n", "    \n", "    NOTE: WHEN DEALING WITH CATEGORICAL FIELDS, ONLY USE THE UNIQUE VALUES PRESENT IN THE DATABASE STRUCTURE PROVIDED, DO NOT FORMULATE VALUES, <PERSON><PERSON><PERSON><PERSON> THIS STRICTLY.\n", "    \n", "    MORE INFORMATION ABOUT THE DATABASE:\n", "         ----START------\n", "            {{CONTEXT}}\n", "         ----END--------\n", "\n", "DATABASE STRUCTURE:\n", "\n", "{{database_structure}}\n", "\n", "EXAMPLES OF QUESTION AND QUERY:\n", "\n", "{{training_examples}}\n", "\n", "NOTE: QUESTION THAT HAS TO DO WITH GENERATING ACCOUNT STATEMENT FOR A SPECIFIC USER IS FILE MODE AND ALWAYS USE THE EXAMPLE PROVIDED FOR GENERATING ACCOUNT STATEMENT and only do this if user explicitly asks to generate account statement for a specific user email.\n", "\n", "NOTE: IF QUESTION REQUESTED FOR ACCOUNT STATEMENT, FORGET ABOUT THE DATABASE STRUCTURE PASSED AND USE THE EXAMPLE QUERY FOR ACCOUNT STATEMENT, THAT ALWAYS WORKS.\n", "\n", "CURRENT DATE (HELPFUL IN DATE RELATED QUERIES, USE THIS PLEASE):{current_date}\n", "\n", "USER QUESTION TO GENERATE QUERY FOR:\n", "\n", "{{user_question}}\n", "\n", "\n", "NOTE: THE EXAMPLE QUESTION AND CORRESPONDING QUERY ARE VERY CORRECT AND WRITTEN BY THE DATABASE ADMINISTRATOR, USE THEM TO LEARN, THEY ALWAYS WORK.\n", "\n", "\n", "🚩 **IMPORTANT: READ CAREFULLY — CRITICAL INSTRUCTION** 🚩  \n", "\n", "⚠️ **DO NOT** under any circumstances filter or use any field that is not explicitly provided in the collection you want to use in the database structure.  \n", "⚠️ **NEVER** apply any unique value that is not present in a specific categorical field.  \n", "\n", "🔎 This is **absolutely crucial** for maintaining accuracy. Strictly adhere to the database structure with **ZERO ASSUMPTIONS.**  \n", "\n", "❗ Failure to follow this instruction will **severely impact accuracy and reliability.** Compliance is **MANDATORY.**\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["queries = {\n", "    \"incorrect\": \"db.bulkuploadpayments.find(}, {'_id': 0, 'comment': 1}).sort({'createdAt': -1}).limit(5)\",\n", "    \"correct\": \"db.bulkuploadpayments.find({}, {'_id': 0, 'comment': 1}).sort({'createdAt': -1}).limit(5).to_list\"\n", "}"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "# Get current date and time in UTC\n", "utc_now = datetime.now(pytz.utc)\n", "# Format the current date and time in words\n", "current_date = utc_now.strftime(\"%A, %B %d, %Y at %I:%M %p UTC\")\n", "\n", "def get_query_generator_prompt(database_structure,user_question,training_examples=None,CONTEXT=None):\n", "    return f\"\"\"\n", "     You are SQL-COPILOT SPECIFICALLY BUILT FOR AFRIEX DATABASE IN AN AGENTIC multi-agent system designed to process database queries. Your primary responsibility is to generate MongoDB queries in Python syntax based on the fully interpreted user question.\n", "    \n", "    \n", "    2. **Syntax Formatting:**\n", "       - Use PyMongo syntax(PYTHON  ) for MongoDB queries\n", "       - For field references, use dot notation for nested documents\n", "       - Example:\n", "         \n", "         db.collection_name.find(\n", "             {{\"field\": \"value\"}},\n", "             {{\"_id\": 0, \"name\": 1, \"email\": 1}}\n", "         )\n", "         \n", "         \n", "         Or for aggregation:\n", "         \n", "         \n", "         \n", "          RETURN THE FULL MONGO-DB- QUERY STRING AND ENSURE IT IS CORRECT\n", "    3. **Query Restrictions:**\n", "       - Only generate queries for data retrieval (find, aggregate, etc.). Avoid delete, update, or insert operations or accessing sensitive information like user passwords.\n", "       - Validate that referenced fields exist in the database schema.\n", "\n", "    4. **Database Structure:**\n", "       - Carefully analyze the structure of the database, including collections, fields, and embedded documents.\n", "       - Use $lookup in aggregation pipelines if necessary to join collections for accurate results.\n", "\n", "    5 **Document Data Handling:**\n", "       - If querying nested documents or arrays, use proper dot notation and array operators.\n", "       - Properly handle different data types to ensure accurate querying.\n", "\n", "    6. **Response Modes:**\n", "       - **Direct Response Mode:** Provide a concise numerical or textual answer directly.\n", "       - **File Mode:** Generate a file for large datasets. Include a message notifying users about potentially long processing times.\n", "       - **Analytics Mode:** Generate plots using Python (pandas, matplotlib) when analysis is required. The function must always be named `plot_data`.\n", "\n", "    7. **Output Format:**\n", "       - Return a dictionary with the following keys:\n", "        \n", "         - `mode`: `\"direct\"`, `\"file\"`, or `\"analytics\"` depending on the response type. Do not set to file mode when you are still asking questions, follow strictly.\n", "         - `reason`: A reason for disallowance (empty string if allowed).\n", "         - `generated_query`: The generated MongoDB Python query.\n", "         - `filemodecols`: Field names for **both File Mode and Analytics Mode** outputs in correct order. These will be used to load the data into a dataframe.\n", "         - `plot_code`: Python code for generating plots (only in analytics mode).\n", "         - `is_account_statement`: True or False: if question has to do with user account statement\n", "      \n", "    8. **Additional Notes:**\n", "       - Avoid code blocks (```) in responses.\n", "       - Always round numerical values to 2 decimal places unless specified otherwise.\n", "       - Ensure the queried data matches the `filemodecols` for proper dataframe loading in both File Mode and Analytics Mode.\n", "       - For time-related queries, use the current date as a reference and avoid assumptions about specific dates or periods.\n", "       - Creatively generate queries by analyzing unique values and mixed data types (e.g., nested documents and arrays), adhering strictly to the provided database structure.\n", "       - In analytics mode, keep plots simple, insightful, and free from unnecessary complexity.\n", "       - Only use available collections; do not create new ones.\n", "\n", "    9. **Plot Code Requirements (For Analytics Mode):**\n", "       - Use pandas and matplotlib to process the dataframe and generate a plot.\n", "       - Save the plot in the `static/plots/` directory with a unique filename using `uuid`.\n", "       - Return the path to the saved plot file.\n", "       \n", "    10. **MongoDB Specific Considerations:**\n", "       - Use proper MongoDB operators ($eq, $gt, $lt, $in, etc.)\n", "       - Convert date strings to proper MongoDB date objects when needed\n", "       - Use proper indexing references for arrays\n", "       - <PERSON><PERSON><PERSON><PERSON><PERSON> LOOK AT THE EXAMPLES QUERY AND USE THEM WHEN NECESSARY AS THEY ARE ACCURATE QUERY FOR THOSE QUESTIONS SEEN\n", "\n", "    **Examples:**\n", "    - **Direct Response Mode:** \n", "      User asks, \"How many customers were registered today?\" → Provide a direct numerical answer.\n", "    - **File Mode:** \n", "      User requests a list of customers → Generate a file with appropriate field names. Include `filemodecols` for dataframe compatibility.\n", "    - **Analytics Mode:** \n", "      User asks for a trend analysis → Generate a plot using the `plot_data` function. Include `filemodecols` for dataframe compatibility.\n", "\n", "    11. Ensure consistency in the response of your query especially if user is asking the same question, make sure you are consistent with your answer and MongoDB query you generate.\n", "    \n", "    **Final Note:**\n", "    Ensure all queries are valid, efficient, and aligned with the user's request. Always prioritize clarity and accuracy in both queries and responses. Remember, `filemodecols` must be included for both **File Mode** and **Analytics Mode** to ensure proper dataframe generation.\n", "    \n", "    NOTE: YOU MAY BE INTEGRATED ON OTHER PLATFORMS LIKE SLACK ETC, IF THAT IS THE CASE, YOU WILL BE PROVIDED WITH USER QUERIES HISTORY TO THE BOT FROM OLDEST TO LATEST AND USER CURRENT QUESTION FOR HELPFUL CONVERSATIONAL FLOW WITHIN THE SAME THREAD OR CHAT PAGE.\n", "    IF PLATFORM IS SLACK: THE CONVERSATION HISTORY IS REFERRING TO CURRENT THREAD.\n", "    \n", "    NOTE: ONLY SELECT FILE MODE WHEN NECESSARY, FOR EXAMPLE SOME DATA CAN BE EASILY SHOWN TO THE USER INSTEAD OF SENDING FILE.\n", "    \n", "    NOTE: WHEN DEALING WITH CATEGORICAL FIELDS, ONLY USE THE UNIQUE VALUES PRESENT IN THE DATABASE STRUCTURE PROVIDED, DO NOT FORMULATE VALUES, <PERSON><PERSON><PERSON><PERSON> THIS STRICTLY.\n", "    \n", "    MORE INFORMATION ABOUT THE DATABASE:\n", "         ----START------\n", "            {CONTEXT}\n", "         ----END--------\n", "\n", "DATABASE STRUCTURE:\n", "\n", "{database_structure}\n", "\n", "EXAMPLES OF QUESTION AND QUERY:\n", "\n", "{training_examples}\n", "\n", "NOTE: QUESTION THAT HAS TO DO WITH GENERATING ACCOUNT STATEMENT FOR A SPECIFIC USER IS FILE MODE AND ALWAYS USE THE EXAMPLE PROVIDED FOR GENERATING ACCOUNT STATEMENT and only do this if user explicitly asks to generate account statement for a specific user email.\n", "\n", "NOTE: IF QUESTION REQUESTED FOR ACCOUNT STATEMENT, FORGET ABOUT THE DATABASE STRUCTURE PASSED AND USE THE EXAMPLE QUERY FOR ACCOUNT STATEMENT, THAT ALWAYS WORKS.\n", "\n", "CURRENT DATE (HELPFUL IN DATE RELATED QUERIES, USE THIS PLEASE):{current_date}\n", "\n", "USER QUESTION TO GENERATE QUERY FOR:\n", "\n", "{user_question}\n", "\n", "\n", "NOTE: THE EXAMPLE QUESTION AND CORRESPONDING QUERY ARE VERY CORRECT AND WRITTEN BY THE DATABASE ADMINISTRATOR, USE THEM TO LEARN, THEY ALWAYS WORK.\n", "\n", "\n", "🚩 **IMPORTANT: READ CAREFULLY — CRITICAL INSTRUCTION** 🚩  \n", "\n", "⚠️ **DO NOT** under any circumstances filter or use any field that is not explicitly provided in the collection you want to use in the database structure.  \n", "⚠️ **NEVER** apply any unique value that is not present in a specific highly categorical field but can infer categorical fields based on context  since the categorica field ae the found ones and the filed may not be entire categorical\n", "\n", "🔎 This is **absolutely crucial** for maintaining accuracy. Strictly adhere to the database structure with **ZERO ASSUMPTIONS.**  \n", "\n", "❗ Failure to follow this instruction will **severely impact accuracy and reliability.** Compliance is **MANDATORY.**\n", "# IMPORTANT: USE PYTHON-BASED MONGODB FOR ALL QUERIES.\n", "# - FOR DATE AND TIME RELATED QUERIES, ALWAYS UTILIZE PYTHON'S DATETIME MODULE.\n", "# - WHEN FORMULATING QUERIES, ENSURE THEY RETURN ACTUAL DATA INSTEAD OF QUERY OBJECTS; AVOID INCOMPLETE IMPLEMENTATIONS.\n", "# - REFER TO THE EXAMPLES PROVIDED BELOW FOR GUI<PERSON>NC<PERSON>:\n", "#   {queries}\n", "\n", "# - BASED ON THE USER'S QUESTION, IF IT IS LIKELY TO GENERATE A LARGE DATASET, DEFAULT TO FILE MODE AND BE PROACTIVE.\n", "# - ALWAYS CONVERT LIST-BASED QUERIES USING .to_list() TO RETURN A LIST, RATHER THAN A QUERY OBJECT.\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# - ALWAYS CONVERT LIST-BASED QUERIES USING .to_list() TO RETURN A LIST, RATHER THAN A QUERY OBJECT."]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "from openai import OpenAI\n", "from typing import Dict\n", "\n", "\n", "class Config:\n", "    OPENAI_API_KEY = api_key = os.getenv(\"OPENAI_API_KEY\")  or \"********************************************************************************************************************************************************************\"\n", "    REDIS_URL= os.getenv(\"REDIS_URL\") or \"redis://localhost:6379/0\"\n", "    RABBITMQ_URL = os.getenv(\"RABBITMQ_URL\") or \"guest:guest@localhost:5672/\"\n", "\n", "def generate_query(question: Dict) -> Dict:\n", "    \"\"\"Generate a dummy version of a data sample in JSON format for security purposes.\"\"\"\n", "    client = OpenAI(\n", "        api_key=Config.OPENAI_API_KEY\n", "    )\n", "\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"\n", "     {get_query_generator_prompt(database_structure=random_collections,user_question=question)}\n", "\"\"\"\n", "        }\n", "    ]\n", "    \n", "    response = client.chat.completions.create(\n", "        model=\"gpt-4o-mini\",  # or your preferred model\n", "        messages=messages,\n", "        temperature=0.0,\n", "        max_tokens=4096\n", "    )\n", "    \n", "    return response.choices[0].message.content\n", "\n", "query = \"How mn\"\n", "response = generate_query(question=query)"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '_id',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'Unique identifier for each referral record.'},\n", " {'name': 'fromUserName',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The username of the user who made the referral.'},\n", " {'name': 'to<PERSON><PERSON><PERSON><PERSON>',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The username of the user who is the recipient of the referral.'},\n", " {'name': 'to<PERSON><PERSON>',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The full name of the recipient user.'},\n", " {'name': 'to<PERSON><PERSON>',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The phone number of the recipient user.'},\n", " {'name': 'toUserId',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The unique identifier of the recipient user.'},\n", " {'name': 'fromUserId',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The unique identifier of the user who made the referral.'},\n", " {'name': 'cumulativeTransactionAmount',\n", "  'data_type': 'integer',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'The total amount of transactions associated with this referral.'},\n", " {'name': 'isFromStaff',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'Indicates whether the referral was made by a staff member.',\n", "  'categorical_values': [{'value': 'False', 'count': 73},\n", "   {'value': 'True', 'count': 16}]},\n", " {'name': 'status',\n", "  'data_type': 'string',\n", "  'is_categorical': True,\n", "  'description': 'The current status of the referral (e.g., pending, completed, cancelled).',\n", "  'categorical_values': [{'value': 'joined', 'count': 74},\n", "   {'value': 'resolved', 'count': 9},\n", "   {'value': 'transacted', 'count': 5},\n", "   {'value': 'qualified', 'count': 1}]},\n", " {'name': 'createdAt',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'Timestamp indicating when the referral was created.'},\n", " {'name': 'updatedAt',\n", "  'data_type': 'string',\n", "  'is_categorical': <PERSON><PERSON><PERSON>,\n", "  'description': 'Timestamp indicating the last time the referral record was updated.'},\n", " {'name': '__v',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'Version key for the document, used by Mongoose for versioning.',\n", "  'categorical_values': [{'value': '0', 'count': 89}]},\n", " {'name': 'fromAmount',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'The amount referred by the user who made the referral.',\n", "  'categorical_values': [{'value': '5', 'count': 74},\n", "   {'value': 'None', 'count': 15}]},\n", " {'name': 'to<PERSON><PERSON>',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'The amount that the recipient is expected to receive.',\n", "  'categorical_values': [{'value': '5', 'count': 73},\n", "   {'value': 'None', 'count': 15},\n", "   {'value': '20', 'count': 1}]},\n", " {'name': 'isToFulfilled',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'Indicates whether the referral has been fulfilled by the recipient.',\n", "  'categorical_values': [{'value': 'None', 'count': 80},\n", "   {'value': 'True', 'count': 9}]},\n", " {'name': 'yearFulfilled',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'The year in which the referral was fulfilled.',\n", "  'categorical_values': [{'value': 'None', 'count': 81},\n", "   {'value': '2023', 'count': 7},\n", "   {'value': '2024', 'count': 1}]},\n", " {'name': 'isFromFulfilled',\n", "  'data_type': 'integer',\n", "  'is_categorical': True,\n", "  'description': 'Indicates whether the referral has been fulfilled by the referring user.',\n", "  'categorical_values': [{'value': 'None', 'count': 80},\n", "   {'value': 'True', 'count': 9}]}]"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["random_collections[6][\"fields\"]"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\\n    \"mode\": \"file\",\\n    \"reason\": \"\",\\n    \"generated_query\": \"db.bulkuploadpayments.find({}, {\\'_id\\': 0, \\'createdAt\\': 1, \\'comment\\': 1}).sort({\\'createdAt\\': -1}).limit(5)\",\\n    \"filemodecols\": [\"createdAt\", \"comment\"],\\n    \"plot_code\": \"\",\\n    \"is_account_statement\": false\\n}'"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["response = json.loads(response)\n"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [], "source": ["query = response[\"generated_query\"]"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"db.bulkuploadpayments.find({}, {'_id': 0, 'createdAt': 1, 'comment': 1}).sort({'createdAt': -1}).limit(5)\""]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [], "source": ["query = \"How many people have refereed  at least 2 people so far,username and count of refereals,  \"\n", "response = generate_query(question=query)\n", "response = json.loads(response)\n", "query = response[\"generated_query\"]"]}, {"cell_type": "code", "execution_count": 191, "metadata": {}, "outputs": [{"data": {"text/plain": ["'db.referrals.count_documents({})'"]}, "execution_count": 191, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'mode': 'file',\n", " 'reason': '',\n", " 'generated_query': \"db.referrals.aggregate([ { '$group': { '_id': '$fromUserName', 'referralCount': { '$sum': 1 } } }, { '$match': { 'referralCount': { '$gte': 2 } } }, { '$project': { '_id': 0, 'username': '$_id', 'count_of_referrals': '$referralCount' } } ])\",\n", " 'filemodecols': ['username', 'count_of_referrals'],\n", " 'plot_code': '',\n", " 'is_account_statement': False}"]}, "execution_count": 196, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Result is a CommandCursor\n", "[{'username': 'da<PERSON><PERSON><PERSON><PERSON><PERSON>', 'count_of_referrals': 3}, {'username': 'users<PERSON><PERSON><PERSON>', 'count_of_referrals': 2}, {'username': 'iuuu', 'count_of_referrals': 6}, {'username': 'aniek174', 'count_of_referrals': 4}, {'username': 'pasca745', 'count_of_referrals': 2}, {'username': 'iran0191', 'count_of_referrals': 3}, {'username': 'cole423', 'count_of_referrals': 2}, {'username': 'tests550', 'count_of_referrals': 9}, {'username': 'mamafavour', 'count_of_referrals': 11}, {'username': 'omoni386', 'count_of_referrals': 9}, {'username': 'danie<PERSON><PERSON>i<PERSON>', 'count_of_referrals': 4}, {'username': 'virtu120', 'count_of_referrals': 3}, {'username': 'card5296', 'count_of_referrals': 3}, {'username': 'tester', 'count_of_referrals': 7}, {'username': 'danie745', 'count_of_referrals': 4}]\n"]}], "source": ["from pymongo import MongoClient\n", "from pymongo.command_cursor import CommandCursor\n", "from typing import Any, Union, List\n", "\n", "def execute_query(db: MongoClient, query: str) -> Union[List[Any], str]:\n", "    # Execute the query safely using eval()\n", "    data = eval(query, {\"db\": db})\n", "    if isinstance(data, CommandCursor):\n", "        data = data.to_list()\n", "        print(\"Result is a CommandCursor\")\n", "        print(data)\n", "    return data\n"]}, {"cell_type": "code", "execution_count": 198, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pymongo.synchronous.command_cursor.CommandCursor at 0x13644b390>"]}, "execution_count": 198, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "from pymongo.command_cursor import CommandCursor\n", "import ast\n", "\n", "# Setup MongoDB connection\n", "client = MongoClient('mongodb://localhost:27017/')  # Replace with your connection string\n", "db = client['your_database_name']  # Replace with your database name\n", "\n", "# Safely execute the query from AI\n", "def execute_ai_query(query_str):\n", "    # Define a restricted namespace with only allowed operations\n", "    restricted_globals = {\n", "        'db': db,\n", "        'find': lambda collection, query: db[collection].find(query),\n", "        'aggregate': lambda collection, pipeline: db[collection].aggregate(pipeline),\n", "        'find_one': lambda collection, query: db[collection].find_one(query),\n", "        # Add other safe MongoDB operations as needed\n", "    }\n", "    \n", "    try:\n", "        # Use a safer alternative to eval\n", "        # Either parse and validate the query or use restricted execution\n", "        result = eval(query_str, {\"__builtins__\": {}}, restricted_globals)\n", "        \n", "        # Check if result is a CommandCursor\n", "        if isinstance(result, CommandCursor):\n", "            print(\"Result is a CommandCursor\")\n", "            return {\"type\": \"CommandCursor\", \"data\": list(result)}\n", "        else:\n", "            print(f\"Result is type: {type(result)}\")\n", "            return {\"type\": str(type(result)), \"data\": result}\n", "            \n", "    except Exception as e:\n", "        print(f\"Error executing query: {e}\")\n", "        return {\"error\": str(e)}\n", "\n", "# Example usage\n", "ai_query = \"find('users', {'active': True})\"  # This would come from your AI\n", "result = execute_ai_query(ai_query)"]}, {"cell_type": "code", "execution_count": 189, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["           username  count_of_referrals\n", "0          danie745                   4\n", "1            tester                   7\n", "2     danielobirije                   4\n", "3          virtu120                   3\n", "4          card5296                   3\n", "5          omoni386                   9\n", "6          tests550                   9\n", "7        mamafavour                  11\n", "8           cole423                   2\n", "9          pasca745                   2\n", "10         iran0191                   3\n", "11         aniek174                   4\n", "12   usersendonline                   2\n", "13             iuuu                   6\n", "14  danielchukumela                   3\n"]}], "source": ["import pandas as pd\n", "\n", "# List of dictionaries\n", "data = [\n", "    {'username': 'danie745', 'count_of_referrals': 4},\n", "    {'username': 'tester', 'count_of_referrals': 7},\n", "    {'username': 'da<PERSON><PERSON><PERSON><PERSON><PERSON>', 'count_of_referrals': 4},\n", "    {'username': 'virtu120', 'count_of_referrals': 3},\n", "    {'username': 'card5296', 'count_of_referrals': 3},\n", "    {'username': 'omoni386', 'count_of_referrals': 9},\n", "    {'username': 'tests550', 'count_of_referrals': 9},\n", "    {'username': 'mamafavour', 'count_of_referrals': 11},\n", "    {'username': 'cole423', 'count_of_referrals': 2},\n", "    {'username': 'pasca745', 'count_of_referrals': 2},\n", "    {'username': 'iran0191', 'count_of_referrals': 3},\n", "    {'username': 'aniek174', 'count_of_referrals': 4},\n", "    {'username': 'usersendonline', 'count_of_referrals': 2},\n", "    {'username': 'iuuu', 'count_of_referrals': 6},\n", "    {'username': 'da<PERSON><PERSON><PERSON><PERSON><PERSON>', 'count_of_referrals': 3}\n", "]\n", "\n", "# List of column names\n", "columns = ['username', 'count_of_referrals']\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(data, columns=columns)\n", "\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'list' object has no attribute 'to_list'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[162], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_list\u001b[49m()\n", "\u001b[0;31mAttributeError\u001b[0m: 'list' object has no attribute 'to_list'"]}], "source": ["data.to_list()"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'list' object has no attribute 'to_list'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[140], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mresult\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_list\u001b[49m()\n", "\u001b[0;31mAttributeError\u001b[0m: 'list' object has no attribute 'to_list'"]}], "source": ["result.to_list()"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["data = result.to_list()"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'comment': 'test update',\n", "  'createdAt': datetime.datetime(2023, 6, 8, 8, 43, 59, 388000)},\n", " {'comment': 'fff',\n", "  'createdAt': datetime.datetime(2022, 11, 29, 7, 48, 9, 224000)},\n", " {'comment': 'ed',\n", "  'createdAt': datetime.datetime(2022, 11, 21, 13, 20, 23, 880000)},\n", " {'comment': 'eee',\n", "  'createdAt': datetime.datetime(2022, 11, 21, 13, 18, 0, 676000)},\n", " {'comment': 'ytfu',\n", "  'createdAt': datetime.datetime(2022, 10, 31, 11, 34, 13, 204000)}]"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["'db.bulkuploadpayments.count_documents({\"createdAt\": {\"$gte\": \"2022-01-01T00:00:00.000Z\", \"$lt\": \"2023-01-01T00:00:00.000Z\"}})'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["result.to_list()"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["result.to_list()"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'userName': 'mamafavour'}]"]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'mode': 'direct',\n", " 'reason': '',\n", " 'generated_query': \"db.referrals.aggregate([{'$group': {'_id': '$fromUserName', 'referralCount': {'$sum': 1}}}, {'$match': {'referralCount': {'$gte': 10}}}, {'$project': {'_id': 0, 'userName': '$_id'}}]).to_list()\",\n", " 'filemodecols': [],\n", " 'plot_code': '',\n", " 'is_account_statement': False}"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>userName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>mamafavour</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     userName\n", "0  mamafavour"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "asql", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}