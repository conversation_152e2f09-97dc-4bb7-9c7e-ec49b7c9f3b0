["let me find out what the issue might be", "<@U0529D38TL3> file is not opening. Please send it again", "Hi <@U05604ETTJT>, I can't see anything wrong with the alpay thing.... It probably was not live and when it was set to live, it had no balance.\n\nFrom the slack channel, it is reporting normally", "Hi <@URML7BNBY> <@UEKGJ9CN9> Please can you share the data request you made here one more time. I am trying to get the data but have totally forgotten what the request was.", "related to?", "<@UEKGJ9CN9> you mentioned it at all hands yesterday", "Getting all cards added through scan card. <@URML7BNBY>", "Thank you,\n\n<@URML7BNBY> can you please expand on how this can be gotten from the db?", "```info.verificationType: scan```", "Okay\n\n<@U0529D38TL3> do you have the bandwidth to help with this?\n\npaymentmethod table", "On it", "Are there specific information of interest or should I include all the columns?", "<@URML7BNBY> <@UEKGJ9CN9>", "This is good, thanks <@U0529D38TL3>!", "Hi <@U0529D38TL3> what is the average age of UK customers vs US customers ?", "Good afternoon Boss, <@UEK5H2WG4>\nThe average age of our users in the UK is 38 years, while the average age of our US users is 44 years. Please note that, we do not have age data of almost 66% of our users.", "I suspected so", "thanks", "Can I see what canada looks like?", "Canada is also 38", "Hi <@U039Y5S1ZM2> <@U0529D38TL3> .\nPlease can I get the data on the last time the users in this csv file transacted on the app.", "On it <@U05MZ12RH41>", "thanks Chief", "<@U0529D38TL3> please let me know when you have something for me", "<@U05MZ12RH41>", "<@U02A83Y9C05> this csv ", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> please assist in spooling data of withdrawals to NBT so we can get a sample of the users withdrawing to this bank.", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> please help with info on the percentage change of new senders this quarter (April, May, June) compared to last quarter (Jan, Feb, Mar). Also, what’s the txn volume for March and June?\n\nThanks", "• First Quarter new senders (Jan,Feb,Mar) - `5,781`\n• Second Quarter new senders (Apr, May, Jun) - `8,549`\nThis results to a  `47.88%` increase in new senders from from first quarter to the second quarter.\n\n• March Transaction Summary - approx. $22.02m total transaction value  with total volume of 143,516.\n• June Transaction Summary - approx. $27.28m total transaction value  with total volume of 178,188. Note that this value is expected to increase as the month is yet to end.\n<@U071286E8T1>", "Thanks very much <@U0529D38TL3>", "There were no withdrawals to nbt bank, however there were ach transactions from NBT bank. <@U03RL5Z70B0>", "Thanks Frank. cc <@U034CN7L5R7> ", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in June?  (Deposits and withdrawals)", "Hello Data Team,\n\nPlease can you help us with this data? What is the percentage of of users who signed up to users who added their card? The reason behind this is we are analyzing our marketing funnel to identify where we believe is one the drop offs that impacts conversion.", "Gentle reminder\ncc <@U039Y5S1ZM2>", "<@U03DTMEDDJQ>", "Thank you:pray::skin-tone-5:", "When do we classify a user as churned <@U0529D38TL3>", "I believe there's a tag for it now, but in the past we've used the condition that when a user doesn't transact for an entire month then that user has churned.\n<@U039Y5S1ZM2> please confirm", "<@U077YG045P0>\nI'm on it.", "• users that have added card atleast once: 80532\n• All signups ( excluded blocked users): 394121\nPerecentage of users that added card = 20.43%", "<@U077YG045P0> Here", "Thank you Frank. 20% is low. Hopefully once we start pushing the Global account, it can be some form of alternative method for users to fund their Afriex wallet to make transfers", "Hi <@U0529D38TL3>, <@U039Y5S1ZM2>, please I need a monthly breakdown of referral bonuses that have been paid since the beginning of 2024.", "cc <@U0529D38TL3>", "<@U0529D38TL3> can you help spool all current in review transactions.\n\ncc <@U025X332NC9> <@U02B98GJQQ0> <@U02HW9596F3>", "<@U025X332NC9> <@U02B98GJQQ0> <@U02HW9596F3>", "<@U0529D38TL3> , this is pretty urgent. Any update yet? ", "<@U039Y5S1ZM2> ", "<@U05604ETTJT> This is an estimate of the payouts to referrals each month. However there  some manual payouts that we're not included because they're rather hard to track based on the transaction type tagging. Also a few cases of overpayment may not be reflected in this estimate.", "Please can you include a column that shows the number of referrals as well?", "<@U025X332NC9> <@U02B98GJQQ0>", "<@U0529D38TL3> Priorities\n\n1. *Proper calculation of referral bonuses*. Please write out a clear explanation of the problem with the data structure so that <@URML7BNBY> can have a clear understanding of what needs to happen. Also any estimate of what you think it might have been fore each month in 2024 would be helpful\n2. *Card types that are most likely to dispute.* The main question I am trying to answer here is wether we can safely allow customers with regulated debit cards to add their card without a small charge. A regulated debit card is a card that is issued by one of the larger US banks, unregulated debit cards are the ones issued by smaller banks... Pulling all disputes from stripe for 2024 and looking at only the card disputes. We should be able to query our database and/or stripe to get the issuing bank for the card. Once we have that we can easily compare that to a list of regulated banks to figure out how many card disputes are related to these banks \n", "<@U0529D38TL3>, I am already progressing with with number 2 request. I'll let you know when I need some input from you", "Hello Team,\n\nDo we have any report that has a breakdown of our churn rate after each transaction. Especially what is the churn rate looking like after the third transaction?", "Yes, I think we have a chart that shows percent dropoff\n\nMight need a bit of work to get it running properly", "Hi, <@U0529D38TL3>, please what's the update with 1?", "<@U0529D38TL3> <mailto:<EMAIL>|<EMAIL>>  Please provide transaction statement for recipient o<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> from 2022 to 2024", "<@U02B98GJQQ0>", "<@U0529D38TL3> please provide all transaction statement for <mailto:<EMAIL>|<EMAIL>>. Thanks", "<@U02B98GJQQ0>", "<@U0529D38TL3> kindly provide transaction statement of this cx <mailto:<EMAIL>|<EMAIL>> for January to April 2024", "<@U02A83Y9C05> <@U0529D38TL3> do we have any data on how many people use recurring send?", "We should, I just need to know how to identify recurring send, and get the users that are involved. \n<@U02A83Y9C05>", "<@URML7BNBY> Please help guide Frank", "<@U0529D38TL3> provide cx transaction statement <mailto:<EMAIL>|<EMAIL>> from Jan to May 2024", "<@U0529D38TL3> `processor_response_meta-&gt;&gt;'transactionType' = 'SCHEDULED'`", "Thanks <@U039Y5S1ZM2>", "<@UEK5H2WG4> We currently have 72 Users using the recurring send feature.", "Thanks", "<@U041YKB0B3L>", "<@U02B98GJQQ0>", "What of all-time usage <@U0529D38TL3>? Can I get a list these users too", "The 72 users I mentioned earlier is for all time.", "I'm currently curating the list for you <@U03DTMEDDJQ>", "Thanks ", "<@U03DTMEDDJQ> Here's the list of all users that have used recurring send - all time. user details included", "Thank you :pray::skin-tone-5:", "The \"first and last transaction at\" is referring to their first and last recurring send transactions. I forgot to mention this, please bear in mind. <@U03DTMEDDJQ>", "Okay", "Hello <@U0529D38TL3> and <@U039Y5S1ZM2> Please can you share the latest numbers regarding Global account sign up?", "For the month of July (1 to date)", "Got it <@U077YG045P0>", "There has been a total count of 3,683 new signups since july 1st 2024 <@U077YG045P0>", "thank you", "thank you", "thank you", "Are we able to tell who signed up for the Global USD account or who signed up to use the product for Remittance?", "Yes, of course. Do you need the list or the count of those whom have - created usd account/transacted", "Exactly", "Hi <@U0529D38TL3> <@U039Y5S1ZM2>,\n\nI need to know if these users transacted from these cards. Like I want the transaction to originate from the card in this list. I also need to know count of transactions they have done and the date of their last transaction. Can you assist with this <@U0529D38TL3>?", "<@U0529D38TL3> <@U039Y5S1ZM2>\n\nPlease share the following:\n1. Total deposit volume/value in June\n2. Total withdrawals volume/value in June\n3. Total of assets sitting on the platform as at the last day of the month of June\n", "chiefs , any progress here ?", "<@U0529D38TL3> please I need data on the users that had the kyc done within the last 10hours, looking for occurrences of the '`isNotAfricanDescent`' in the kyc object.", "<@U0529D38TL3> how far", "I couldn't find it, though I'm going to sync with <PERSON> see if I can narrow it down", "okay thanks", "keep me updated.", "<@U077YG045P0>", "<@U0529D38TL3> been able to sync ??", "Hi <@U0529D38TL3> and <@U039Y5S1ZM2> Could you please spool data of users who have transacted over $200 and have no KYC from Jan 2024 to date", "<@U034CN7L5R7>", "<@U0529D38TL3> Help me recheck on this by 3pm today.", "I will send a reminder.", "Got it", "<@U039Y5S1ZM2> <@U077YG045P0> is seeing more signups than downloads in the month of June ... can you check to confirm that all users that signed up in June have a 'deviceType' thanks", "On it", "<@U039Y5S1ZM2> you can compare it to what i am seeing on Amplitude: <https://app.amplitude.com/analytics/afriex/chart/pzzsaeab>", "Let <@U0529D38TL3> share the data on users who signed up in june and how many of them have `deviceType`", "<@UEK5H2WG4> <@U077YG045P0> <@U039Y5S1ZM2>", "What is null? Could it be organic?", "The device os could not be ascertained... Hence it is stored as empty in db.\nThe reason why this could happen,  I'm not entirely sure.\n<@U039Y5S1ZM2>  Can explain further", "`15, 249` users don't have deviceType", "Sorry, hold on", "How did you get the numbers <@U0529D38TL3>?", "I can confirm that they all have `deviceTypes`\n\ncc <@U077YG045P0> <@UEK5H2WG4>", "<@U039Y5S1ZM2> what about device ID ... I'm trying to see if these are bots or not", "Let me check", "```SELECT device_os,\n  COUNT(\"id\") user_count\nFROM transformed.users\nWHERE date &gt;= '2024-06-01'\n  AND date &lt;= '2024-06-30'\nGROUP BY device_os```", "<@U039Y5S1ZM2> This is the Query I used", "They all have device ids and tokens", "Hmmm... <@U0529D38TL3> check `public.people`", "Understood <@U039Y5S1ZM2>. I'll take note of this from now on", "There is a `devices` array that, my understanding, would be that it contains all the devices the user has used. And their is a `currentDevice` that should be the current device the user is using.\n\n<PERSON> query was looking at the currentDevice which is sometimes null (not sure why)&gt;\n\nBut I checked the devices array, and all the users have at some point, had a deviceType, deviceToken and deviceId.\n\ncc <@UEK5H2WG4> <@U077YG045P0>", "if we had 15k downloads, how do we have 19k sign up", "This is what I am trying to figure out", "How do you see the download numbers?", "Can you help <@U0529D38TL3> \n\nCc <@U06U154SYTY>", "Please include there countries as well", "Is the referral code a single string: \"(TACOS/LSW)\" or two referral codes \"TACOS\" or \"LSW\"?", "Should be single \n\nUse LIKE", "Got it", "<@U06U154SYTY>", "Thanks <@U0529D38TL3> \n\nHow about LSW?", "There was no LSW on the db. There was one on record close to it which was LILSWT or something like that.", "And it wasn't yesterday", "Okay, thanks", "Can you assist <@U0529D38TL3> \n\nCc <@UEKGJ9CN9>", "How can I identify deposit to USD bank accounts.\n<@URML7BNBY> <@U039Y5S1ZM2>", "<@URML7BNBY>?", "{channel: \"VIRTUAL_BANK_ACCOUNT\", processor: \"STRIPE\"}", "<@UEKGJ9CN9> <@U039Y5S1ZM2> <@URML7BNBY>", "Thank you <@U0529D38TL3>!", "<@U0529D38TL3> You did not add the sender’s account name ?", "<@UEKGJ9CN9> \n<PERSON>afternoon Chief, \nI did add the full name column. It's the second row just after user_id", "<@U0529D38TL3> No, not account name of the recipient, I meant for the sender, where the deposit is coming from.\n<@URML7BNBY> can you point that field to <PERSON> please.", "Okay, I understand now. I misinterpreted you as you said \"*sender's* account name\" I know the field. I can fetch it for you now. Please gimme a few minutes", "<@UEKGJ9CN9> I've added the source and destination account names.\nplease note there are duplicates due to the different account names a particular user might have sent or recieved to.", "Hello <@U0529D38TL3> please share Account statement for all transactions for <mailto:<EMAIL>|<EMAIL>>", "<@U0529D38TL3> on the kyc task , Please I need to get the kyc.selfie data for the last thousand users that we have who have completed kyc.", "preferrably just two fields, the selfieImage url and the users full name.", "Not all of them have a values in the kyc.images.selfie so I picked only those that aren't null.", "<@U041YKB0B3L>", "<@U0529D38TL3> please provide account statement for the customers\n\n<mailto:<EMAIL>|<EMAIL>> statement of account from inception\n<mailto:<EMAIL>|<EMAIL>> account statement for 2022-2024\nthanks", "<@U041YKB0B3L> No transaction records found for user with associated email - <mailto:<EMAIL>|<EMAIL>>.\n\nThe csv attached below is the transaction statement of the user with the email - <mailto:<EMAIL>|<EMAIL>> for the period of Jan 2022 till date", "<@U039Y5S1ZM2> <@U0529D38TL3> I need dispute data per corridor ...\nWhat was dispute rate for users sending from USD -&gt; NGN, USD -&gt; GHS, USD-&gt;KeS, CAD-&gt;NGN, GBP-&gt;GHS .... etc...", "Goodmorning Chief, \nI'm on it...", "Stripe Dispute Rate by corridor for the 2024", "I also curated the stripe dispute rate for all time. <@UEK5H2WG4>", "<@U039Y5S1ZM2> <@U0529D38TL3>, please can we get some data on referrals? We need the details of users who joined the referral program each month since the program started (like a monthly cohort), and how much these (referred) users have transacted each month since joining the app.\n\ncc: <@UEK5H2WG4>", "Got it,\nThough I'm unavailable at the moment. But when I get home, I'll push this to you. :pray:", "<@U0529D38TL3>, wassup?", "<@U05604ETTJT> Sorry for the delay chief...", "<@U0529D38TL3> please help with the request below\n\n<mailto:<EMAIL>|<EMAIL>> inception till date\n<mailto:<EMAIL>|<EMAIL>> inception till date\n<mailto:<EMAIL>|<EMAIL>> june statement", "<mailto:<EMAIL>|<EMAIL>>", "<mailto:<EMAIL>|<EMAIL>>", "<@U0529D38TL3> <@U039Y5S1ZM2> data not loading on account review for me", "<@UEKGJ9CN9> Did you encounter this issue today, for the first time?", "Today", "I can see the filter is set to today, I think it's probably because new applications haven't coming today, or atleast haven't migrated to the table yet. \nI'll run a query to confirm", "Oh alright", "Got it, There was new data coming in, It appears one or more records have characters exceeded the 255 character limit set on  one of the columns. I'll sort it out now", "```Nice to meet you. Here is Official Staff. Tips to Earn Money! 1. Reply messages. 2. Finish Task.(Me-Task Center) 3. Receive gifts. 4. Answer calls.How to withdraw money?(20 Points=1$; 5$ can be withdrawal) Me---Income---Withdraw---Paypal Any requirements please contact here!```", "I've fixed the isssue, kindly refresh the app. The new applications should appear now <@UEKGJ9CN9>", "Hello <@U039Y5S1ZM2> and <@U0529D38TL3> I am looking into return on ad spend. I don't know if there is a way to know if this revenue correlates with the revenue generated in the same week", "<@U02HW9596F3> can you help with this?", "We don't have the most recent otc numbers on the retool app", "Hi <@U039Y5S1ZM2>, as mentioned during All hands, I want to know if the 1st transactions of new users are pre-kyc or post kyc. So i need a combined line chart showing both values over the last few months. Thanks", "Okay", "<@U0529D38TL3> do you think you can handle this?\n\nReach out so we can walk through it", "Hi <@U039Y5S1ZM2> <@U0529D38TL3> we are trying to determine which customers should get a limit increase can you reveiw transaction data for the last 90 days and make a recommendation?", "Hi <@U0529D38TL3>, Please can you get a csv of the users that have their `public,people.kyc.isPossibleSocialEngineering` set to true.\n\ncc <@U05MZ12RH41>", "On it", "<@U05MZ12RH41>", "<@UEK5H2WG4> Please take a look.", "Hello <@U039Y5S1ZM2> and <@U0529D38TL3> please do you know how many NGN to GHS transactions we have since July 1st to date?", "I'm on it", "<@U077YG045P0>", "NGN to GHS transaction count by transaction type", "<@U0529D38TL3> hey chief, please I need a data on the users who have their security flag reason  as `Name check failed, possible social engineering`", "<@U0529D38TL3> How does this compare to June 1 to 30th?", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in July?  (Deposits and withdrawals)", "Thank you", "<@U0529D38TL3> can I get a sense of what percent of new signed users are put under review each day for 'social engineering'", "Yes, I can fetch that now. Since the launch of the feature", "<@UEK5H2WG4>  I've uploaded the breakdown by date since the launch of the `possible social engineering` feature to this google sheet.\n<https://docs.google.com/spreadsheets/d/1sT6yITuk9bcJIo2zuc_hraG6eC0fwmRwjrAaj2uRhJ8/edit?usp=sharing>", "Wasn't able to find this exact tag. but I did see `name mismatch` and `possible social engineering`. <@U05MZ12RH41>", "Hi <@U0529D38TL3> and <@U039Y5S1ZM2> Retool is not loading, please can you help look into it?", "Hello <@U077YG045P0> \nwe are aware of the issue, it's a general issue across all apps on retool due to an issue with our postgres database. Please bear with us as we are actively working to resolve the issue. :pray:", "Thank you :pray::skin-tone-4:", "Hello <@U0529D38TL3> and <@U039Y5S1ZM2> We recently have some users who lost their rewards points. We want to send an email to them explaining what happened. Can you help pull a list of affected customer's emails?", "Hi <@U077YG045P0> \nDo you have any pointers on how we can identify the affected users? \n<@U039Y5S1ZM2>", "Hello <@U0529D38TL3> I am seeing a two day data gap in retool. How does this impact reporting", "Please can you share the link to this app <@U077YG045P0>?", "<https://afriex.retool.com/apps/4ded75e2-ba46-11ec-825b-330f3f6f732b/Growth/afriex_company_metrics>", "Thanks", "Should be fine now <@U077YG045P0>", "Thank you <@U039Y5S1ZM2>", "ANy update on this <@U039Y5S1ZM2> <@U0529D38TL3>", "Can you respond to <@U0529D38TL3> question... He'll need a bit more info to assist with this", "<@U02A83Y9C05> Please can you help here?", "<@U039Y5S1ZM2> <PERSON> is requesting for the details of all the customers who had rewards points that we recently cleared out. I'm assuming you should be able to assist <PERSON> identify these customers, right?", "When were these reward points cleared out?", "<https://afriex.slack.com/archives/C04RUT5AT2T/p1721210925423359?thread_ts=**********.525359&amp;cid=C04RUT5AT2T>", "<@U0529D38TL3> can you help add phone number and email to this csv", "Got it <@U039Y5S1ZM2>", "Thank you both :pray::skin-tone-4:", "Hello <@U0529D38TL3> Please can i get a list of everyone who have signed up in the US since August 5th to date", "<@U077YG045P0>", "Is it possible to know if they created a global account?", "Yes... I can include a column that signifies if a user has created a global account", "Yes please do that.", "<@U077YG045P0>", "Thank you <@U0529D38TL3>", "<@U0529D38TL3> can you please share the same data for previous week", "<@U077YG045P0> i.e from July 29th to August 4th?", "Hi <@U0529D38TL3> is there a way to know what percentage of volume last month came in from Global Accts?", "Certainly. <@U071286E8T1>\n\nVALUE USD - TOTAL AMOUNT\n• The total inflow Value from Global Accounts - `$205,810.83`\n• The total deposits value in July 2024 - `$19,215,012.51`\n• percentage from Global Account - `1.07%`\nVOLUME - COUNT OF TRANSACTIONS\n• The inflow  volume from Global Accounts - `689`\n• The total deposits in July 2024 - `132,474`\n• percentage from Global Account - `0.52%`\n", "Hello <@U0529D38TL3> Please can you help me with the number of new senders for these corridors\nUK and US from Aug 1st to Aug 13th", "<@U077YG045P0> There are a total of `374` new senders from UK &amp; US since 1st Aug 2024 to date (13 Aug 2024)", "Can we split it", "Not combined", "UK - `133`\nUS - `241`", "Hi <@U039Y5S1ZM2> <@U0529D38TL3>, please have a look at retool", "hi <@U025X332NC9>, can you check now?", "Thank you", "<@U0529D38TL3> Please trace the profile link to this account Afriex UK Limited. ********. Sort code 23-68-02", "<@U02B98GJQQ0>", "thank you", "Hi <@U039Y5S1ZM2>,\n\nWe're getting alerts for 0.00 balance on Alpay, when we have over GHC1.5M on the account.. and this has been recurring. Please take a look.\n\ncc: <@U025X332NC9>", "Maybe you just funded", "Funding wasg was done since 1:29PM", "Plus this is a recurring issue. I think I've mentioned it before. It's pec<PERSON><PERSON> to <PERSON><PERSON><PERSON>", "And the notification always shows the balance as 0.00, which is almost impossible. The combination of withdrawals that would give a net of 0.00 on any random day is extremely unlikely", "<@U0529D38TL3> please share Transaction statement for <mailto:<EMAIL>|<EMAIL>>\n2021-2022", "<@U041YKB0B3L>", "Hello <@U0529D38TL3>\n\nI trust you are well.\n\nPlease can you help me do an analysis of how many NG users who signed up in July created a global USD account, attempted to created a global USD account", "Thank you ", "Thank you <@U0529D38TL3> This is for the month of July correct?", "Yes, for users that signed up in July <@U077YG045P0>", "Can we see how it compares to August?", "August 1 to date", "<@U0529D38TL3> this person got blocked because they added a middle name to their card. Can you pull all the people that have this security note so we can see how it has been impacting user experience", "Hello <@U0529D38TL3> please help <mailto:osakpam<PERSON><PERSON><PERSON><PERSON>@gmail.com|osakpamwanig<PERSON><EMAIL>> Transaction statement for 2024 ", "Got it. <@UEK5H2WG4>", "<@UEK5H2WG4>", "<@U041YKB0B3L>", "Hi <@U0529D38TL3> please assist to spool all NGN users (both verified and unverified) for the period 19th to 25th August with reasons for blockage for the unverified ones.Thanks", "Goodmorning <@U034CN7L5R7>,\nTo be clear you want a list of all users blocked within 19th to 25th August, along with the note.", "Yes please", "Hello <@U039Y5S1ZM2>\nPlease what data format is under the attribute last_txn_date and last_transacted_at?", "This is what I see for last_transacted_at", "Seems like a text field (Might be from one of the custom data imports)", "Oh i see. No worries then. Thats why it keeps returning 0", "last_txn_date\n\nText as well", "Hi <@U0529D38TL3> can I get a list of reasons why an account goes into review?", "Hello <@U0529D38TL3> please help with Statement of account for 2019- 2020 for <mailto:<EMAIL>|<EMAIL>>", "User did not transact in 2019 and 2020. The user first transacted in June 30th 2021", "<@U03DTMEDDJQ> The full list, for all time?", "Yes, please", "Reasons not customers oh", "I understand. Coming up shortly", "Majority of the security notes  (reason) are not uniform,  since done manually there are often descriptive, even adding ids in some cases. There are over 5000 notes as a result\nHowever I've filter the top 200 cases. But the system notes and most prevailing reasons are captured here.", "Cool. Thank you", "Thank you, please can you respond to your dm ", "Hi <@U0529D38TL3> please help spool all users flagged with the EDD request note.Include a column on whether they are verified or not", "<@U0529D38TL3> a reminder on this", "<@U034CN7L5R7> This contains all users flagged for edd. both currently blocked users and those not blocked.", "CURRENTLY BLOCKED USERS FLAGGED FOR EDD <@U034CN7L5R7>", "<@U0529D38TL3> please help with the account details linked to this virtual account <tel:********|********>", "Hi <@U0529D38TL3> please help spool data for blocked users between 22nd August and 28th August with reasons they got blocked.", "<@U0529D38TL3> please assist with this.Take the date 19th to date", "<@U034CN7L5R7> Here's the update", "<@U03RL5Z70B0>", "thank you", "Reminder <@U0529D38TL3> ", "user ID : `66ca38345b24b4c6a7434298`\nFull Name: <PERSON><PERSON>\nemail: <mailto:<EMAIL>|<EMAIL>>", "Hi <@U0529D38TL3> please help spool blocked users for the period 26th August to 1st September with top reasons why the are blocked.", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in August?  (Deposits and withdrawals)", "<@U03DTMEDDJQ>", "Thank you :pray::skin-tone-5:", "Good day <@U0529D38TL3> please help with the account linked to this virtual Account Number: ******** Sort Code: 236802", "Also the account linked to virtual account ********", "Got it", "Hi <@U0529D38TL3> any update on this?", "I want to excluded the flaggings that do not initiate a block, I'm awaiting feedback from engineering. I'll try contacting again", "<@U041YKB0B3L>", "<@U034CN7L5R7>", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> is it possible to get the cumulative count of users who have given us access to their contacts?", "Hi <@U039Y5S1ZM2> <@U0529D38TL3>, I urgently need a list of virtual account users who have done more than 5 transactions in a day.\n\nI need for UK users, their emails, count of trasactions and volume. Please treat as urgent", "<@U039Y5S1ZM2> Is the something on the people table that I can use to identify these users?", "Is there a particular period to limit this to? say in 2024, past 6 months or all time.\nAnd just to be clear, for only UK users?", "<@U025X332NC9>", "all time.  Specific to virtual accounts, UK users", "Copy that", "you know what, I also want to see their all time transaction count", "so I want to see these coloumns\n\n• User name \n• email \n• Total transaction count \n• Volume \n• is more than 5 in a day = True or False ", "add their phone number please", "thank you", "<https://afriex-org.freshchat.com/a/***************/open/conversation/***************|https://afriex-org.freshchat.com/a/***************/open/conversation/***************>", "<https://afriex-org.freshchat.com/a/***************/open/conversation/***************|https://afriex-org.freshchat.com/a/***************/open/conversation/***************>", "<@U041X4TRE91> I don't believe everyone has access to Freshchat. You can help by posting the bank account details involved with these complaints", "There is a link to the customers who reported <@U0529D38TL3> one dropped his contact on the chat for more information about how he was scammed. ", "This is content of the conversation", "This too", "The accounts reported at the moment are just these two for now ", "The first account was reported by one person while the other account was two people making it three in total. ", "<@U025X332NC9>", "Hi <@U0529D38TL3>  please help spool data for blocked users between 29th August and 4th September, with reasons they got blocked.", "hi <@U0529D38TL3> any update on this,kindly assist", "Hi <@U0529D38TL3>\n\nCan you help spool a csv of users referred by <mailto:<EMAIL>|<EMAIL>> username: `chor<PERSON><PERSON> , who have at some time made a single `deposit`  above $30.\n\nUser_id, Count or transactions above $30, Total value of transactions above 30 (Is all that is needed)\n\ncc <@U03M0KF92V6> <@U06GJ5NUZUN>", "<@U03RL5Z70B0>", "thank you :+1:", "<@U03M0KF92V6> <@U06GJ5NUZUN>\nThe transaction count and Transaction Value account for only single deposit transactions that a user makes worth atleast $30.", "Thanks", "<@U03M0KF92V6> Did you mention you want to look at a particular time frame? or all time?", "This is all time", "<@U03M0KF92V6>", "Thanks Frank", "<@U0529D38TL3> <@U02A83Y9C05> Please assist with this list of GBP virtual account that a cx reached out to have been used to scam people.", "<@U0529D38TL3> please fetch the information of  the user linked to this account Sort code 23-66-02 Account number ********", "<@U02B98GJQQ0>", "<@U045AJUKZB2>", "Thanks <@U0529D38TL3>", "good day <@U0529D38TL3> please help with the account linked to virtual account ******** and ********", "<@U041YKB0B3L>", "thank you", "please can you get this account too ********", "********", "<tel:********|********>", "<@U0529D38TL3> ", "<@U041YKB0B3L>", "<@U0529D38TL3> please help with statement of account for April for <mailto:<EMAIL>|<EMAIL>>", "<@U0529D38TL3> Please assist with the account linked to this virtual account number.", "hello <@U0529D38TL3> please help with the account linked to Virtual account ********", "reminder on this <@U0529D38TL3>", "Good morning <@U0529D38TL3>  please help spool data for blocked users between 5th September and 11th September, with reasons they got blocked.", "<@U03RL5Z70B0>", "thank you :pray:", "<@U0529D38TL3> ", "Good day <@U0529D38TL3> kindly also share statement of <mailto:<EMAIL>|<EMAIL>> from Jan 1 till sep 15 2024.", "<@U041YKB0B3L>", "<@U039Y5S1ZM2>, <@U0529D38TL3>, Is there a way to extract signup/active user data from sales team efforts? I believe referral codes were created for our sales team. We need number of sign ups, total number of active users from those signups, and total transactions (value and count) by customers who signed up. Pls do this for the last three months.", "Yes, we can track activity of users that were brought in by the sales team.\nIt would help to get the list of referral codes as it'll streamline the process, but if you don't have that available, I can use data from the sales allies app.", "I don’t have them <@U0529D38TL3> ", "Hi <@U0529D38TL3> please help spool data for blocked users between 12th September and 18th September, with reasons they got blocked.", "<@U03RL5Z70B0>", "Thank you :pray:  ", "<@U06GJ5NUZUN> please confirm if all the sales team referrals are on the sales allies table", "Hello <@U0529D38TL3> Is there a way to check how many global account creation was done in Kenya in the last 2 days?", "Yes. I'll run a check now", "<@U077YG045P0> No Global account was created by  Kenyan Users in the past 2 days. The last date an account was created by a Kenyan user was on the 15th of september - i.e 4 days ago.", "Hi <@U0529D38TL3> please provide Account statement for <mailto:<EMAIL>|<EMAIL>> ", "Also Account statement for <mailto:u<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com|ugo<PERSON><PERSON><PERSON><EMAIL>> for only transactions sent to NJOKU ISAAC OKANI and OKANI ANUJURU EUNICE", "<mailto:<EMAIL>|<EMAIL>> All time transaction records <@U041YKB0B3L>", "Account statement for <mailto:u<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com|ugo<PERSON><PERSON><PERSON><EMAIL>> for only transactions sent to NJOKU ISAAC OKANI and OKANI ANUJURU EUNICE", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> urgently assist in spooling the transaction ids,amount and email addresses of the below stripe ids", "<@U034CN7L5R7>", "thank you <@U0529D38TL3>", "Hello <@U0529D38TL3> <@U039Y5S1ZM2> Please can you confirm the under_review attribute is a boolean attribute i.e. (true/false)?", "<@U077YG045P0>", "<@U0529D38TL3> I have an attribute on customer io called under_review. I want to know if we are sending just true or false to customer io regarding this attribute", "Under Review and Security enabled is the same thing. It tells if the user is blocked or not.\n\n\"I want to know if we sending just true or false....\" this part I'm not sure I follow", "hello <@U0529D38TL3> please assist with account statement for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>", "Hello <@U039Y5S1ZM2> and <@U0529D38TL3>\n\nPlease can you give me access to this app on retool?\n<https://afriex.retool.com/apps/0f413db6-50cb-11ee-af34-3f488c7ca5b2/Product/referral_tracker>", "Check if you have access now", "Checking...", "Yes i do. thank you Mike", "Okay", "Hello <@U039Y5S1ZM2> and <@U0529D38TL3> Please do you know where the data for the \"How did you hear about us\" and \"What is the transaction for\" we ask users on the app are being stored? PS: these might not be the exact words", "I might be able to get the reason for transaction, however the \"`where did you hear about us?`\" I'm not sure where that data is stored. <@U039Y5S1ZM2> do we have that data on postgres?", "Hi <@U0529D38TL3> please assist in spooling the transaction ids,amount and email addresses of the below stripe ids <https://docs.google.com/spreadsheets/d/1XfyT5Mtmn-YrjnqwxH2b1NBQdjQrDeQopTkpBrp-FNo/edit?gid=0#gid=0>", "Hi <@U04D7ET6E03> <@UJ8KXEJCT> <@U05MZ12RH41> do we store this somewhere?", "<@U03RL5Z70B0>", "Thank you", "<@U0529D38TL3> please assist with sheet 2 as well cc <@U034CN7L5R7> <https://docs.google.com/spreadsheets/d/1XfyT5Mtmn-YrjnqwxH2b1NBQdjQrDeQopTkpBrp-FNo/edit?gid=469940758#gid=469940758>", "No idea, on my end. <PERSON><PERSON><PERSON>?", "<@U03RL5Z70B0>", "Good morning <@U0529D38TL3> please help spool data for blocked users between 19th September and 25th September, with reasons they got blocked.", "<@U03RL5Z70B0>", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in September?  (Deposits and withdrawals)", "<@U03DTMEDDJQ>", "Thank you Frank :pray::skin-tone-5:", "Hi <@U0529D38TL3> please help spool data for blocked users between 26th September and 2nd October, with reasons they got blocked", "<@U03RL5Z70B0>", "thank you", "<@U0529D38TL3> please share data on accounts that got *unblocked*,with reasons they got unblocked for the period 26th September and 2nd October", "Unblocked users", "thank you", "Good day <@U0529D38TL3> please provide Statement of account for <mailto:<EMAIL>|<EMAIL>>", "<@U077Y6H8KLG> :point_up::skin-tone-5: this should be available on admin", "<@UEK5H2WG4> It's not yet, has been put on hold as there's some support lacking from the backend.\ncc <@U02A83Y9C05>", "<@U077Y6H8KLG> <@U02A83Y9C05> <@UEK5H2WG4> please note we have customers requesting for a more official statement\n\"I need this in afriex letter head similar to the statements you’e been giving . This is for immigration purposes I can’t submit an excel listing!\"", "reminder <@U0529D38TL3>", "<@U041YKB0B3L> The backend team seem to have a lot of high priority tasks being sorted out at the moment, once we can get someone to clear some queue, we can get the work around this done and admin can implement it.", "Noted ", "<PERSON><PERSON><PERSON> had made request for this user. I assumed it was same request. I have fowarded the pdf to you on your DM.", "<@U0529D38TL3> <@U039Y5S1ZM2> please check if this Account Number: ******** was credited with £150 on Oct 5th. The deposit is not on admin and her bank claims it was successfully sent to us", "Hi <@U0529D38TL3>, could you please provide a list of new users who haven’t transacted yet, segmented by those who have added cards and those who haven’t? (From June to date)", "From what I can see on the db, there users made no transaction on that date.\ncc:<@U039Y5S1ZM2>", "By new users do you mean those who signed up in June till date?", "Yes", "The users that have added card", "NEVER ADDED CARD", "Thank you Frank", "These are new users who haven't transacted since June yes? <@U0529D38TL3>\n\n<https://afriex.slack.com/archives/C053CF68G8Y/p1728478606398269?thread_ts=**********.544009&cid=C053CF68G8Y>", "HAS ADDED CARD", "NEVER ADDED CARD", "I've added the `activation_status` column. `activated` is for those that have transacted, while `dormant` is for those that have never transacted", "Thanks :pray::skin-tone-5:", "good day <@U0529D38TL3> please help with account statement for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>", "Hello <@U0529D38TL3> please provide account statement for this customer <mailto:<EMAIL>|<EMAIL>>", "Hello <@U0529D38TL3>\n\nPlease can you help me with this data set of users with the attributes that have the high lifetime value in the first year", "Hello <@U0529D38TL3>, please share a list of US users that have added card but haven't transacted in the last 6 months", "Hi Wura you can export it from that retool app", "oh, checking now", "got it, thanks boss", "This user segment (June - August) is part of the group I've been sending surveys to over the past two weeks", "ohh, can you share the survey with me?", "Sure, but only one person has responded so far.\n\n<https://docs.google.com/forms/d/e/1FAIpQLSc7JedH_MDP_h9y624GG3jaLP9F0O2XatiWjIFN3wBmZ8iUsQ/viewform?usp=sharing>", "Would be interesting to know what percentage of them attempted payments on stripe and got blocked or rejected. Or what percent are under review\n\n<@U025X332NC9>  …", "yes i want to know the attempted too, the list i have doesn't seem to have anyone blocked", "We can run the IDs on your list to check if they are blocked. <@U0529D38TL3>  should be able to do it asap", "<https://docs.google.com/spreadsheets/d/1n_YhCftQBH62Jilz4nzV3SztL70mKrdl/edit?gid=1810930492#gid=1810930492>", "22 users from this segment have transacted since I started the survey.", "<@U039Y5S1ZM2> can you help with this? since frank isn't available", "<@U0529D38TL3> has confirmed that his is on it", "<@U0529D38TL3> can you confirm here", "The `security enabled` column is correct. That tells the users that are blocked or not. false signifies that users that are not blocked, and true for users that are blocked.", "how about this? Would be interesting to know what percentage of them attempted payments on stripe and got blocked or rejected. Or", "<@U0529D38TL3> please help with Statement of account for the year <mailto:<EMAIL>|<EMAIL>>", "Also Transaction statement for month of June 2024 <mailto:<EMAIL>|<EMAIL>>", "2024 Transactions for <mailto:<EMAIL>|<EMAIL>>", "June 2024 for <mailto:<EMAIL>|<EMAIL>>", "Hello <@U0529D38TL3> \nPlease can you help with GHS transactions that are in review", "<@U0529D38TL3> <@U039Y5S1ZM2> please can i get a list of all users in California?", "<@U04H2N7CYNA>", "<@U041YKB0B3L>", "thank youu", "Approximately 12% of these users had attempted to transact and were rejected by stripe. 594 out of 4,958.", "<@U04H2N7CYNA> These are the users that made attempts to transact but were rejected by stripe", "oh nice, do we have reasons for the rejections?", "unfortunately, no. not in the data shared with me.", "boss <@U025X332NC9> this is the thread", "<@U0529D38TL3> please provide Statement of account for <mailto:<EMAIL>|<EMAIL>> from Sept till date in pdf format. ", "<@U041YKB0B3L>", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in October?  (Deposits and withdrawals)", "Hello <@U0529D38TL3> please help with account statement for <mailto:<EMAIL>|<EMAIL>> in pdf", "cc <@U039Y5S1ZM2> kindly assist", "<@U0529D38TL3> should be available today", ":+1:", "Account statement of <mailto:<EMAIL>|<EMAIL>> on all money sent to <PERSON><PERSON> n<PERSON> from 2023 in pdf", "<@U03DTMEDDJQ>", "Thank you :pray::skin-tone-5:", "<@U041YKB0B3L> For <mailto:<EMAIL>|<EMAIL>>", "For <mailto:<EMAIL>|<EMAIL>>", "thank you", "Good day <@U0529D38TL3> please provide account statement for <mailto:<EMAIL>|<EMAIL>> for 2024 in pdf", "<@U041YKB0B3L>", "<@U0529D38TL3> can i get a list of all unregulated card users? all time", "<@U039Y5S1ZM2> ", "cc <@U02A83Y9C05>", "<@URML7BNBY> could you please help confirm if bonus values are currently set for referrer and referred on the parameter store?", "<@U0327N9U7BP> any thoughts on above?", "I thought there was a time we disabled one leg", "What are the current values in Parameter store <@URML7BNBY>?", "<@U0327N9U7BP> what are the keys?", "\"bonusAmountPerReferrer\": \"REFERRAL_PARAMS_BONUS_AMOUNT_PER_REFERRER\",\n    \"bonusAmountPerReferee\": \"REFERRAL_PARAMS_BONUS_AMOUNT_PER_REFEREE\",", "<@U0529D38TL3> please provide transaction statement for <mailto:<EMAIL>|<EMAIL>> in pdf", "```REFERRAL_PARAMS_BONUS_AMOUNT_PER_REFERRER: 15\nREFERRAL_PARAMS_BONUS_AMOUNT_PER_REFEREE: 15```", "<@U0327N9U7BP> The instances where we paid out on one leg of the referral, I believe those were managed on the parameter store when the value was set to 0. Please let's treat this as a bug.", "I'm investigating...Pls add the user ids affected to the task", "<@U041X4TRE91> The system is not going to pay out referral bonus to a user that doesn't have full KYC, please advice these customers accordingly\n\n<https://admin.afx-server.com/users/671cca64d7694ab5e8efa789>\n<https://admin.afx-server.com/users/671ca5c6c745323ef2f976d2>", "Noted <@U02A83Y9C05> But others who are verified will receive, right? ", "<@U02A83Y9C05> are we now paying bonuses to both referred and referrer?", "<@U041X4TRE91> That is correct!\n<@U039Y5S1ZM2> Yes, we are. Do you need to make any update to the schedule?", "<@U02A83Y9C05> <@U039Y5S1ZM2> what about the issue of Nigerian users getting 7500 as referral bonus instead of 15000, when will it be resolved? And will the affected ones get paid back the remaining balance.", "That's not an issue. The referral bonus that was paid out was based on the settings we have but we understand the customer's complaint and we have made some changes", "Okay <@U02A83Y9C05>\n\nYes I will have to make changes", "Hmm, that's odd. When the referral bonus was $20, we never got any complaints about either side not getting their bonus, we were paying out to both referrer and referee", "Sorry, I don't get this <@U02A83Y9C05>", "This cx got 7500 instead of 15000 which marketing said should be the bonus. \nobior205\n\nIs the bonus for Nigerians actually 7500 or 15000? <@U02A83Y9C05> ", "7500", "<@U039Y5S1ZM2> I'll reach out", "Thanks for the information <@U02A83Y9C05> ", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> \nWe’re currently reviewing KYC performance and need to access failure reasons specific to regions. Could you kindly provide a way to extract reports with failure reasons broken down by country?\nCurrently, we can only see the KYC failure reports as an aggregate for all countries on veriff", "Hello <@U03RL5Z70B0>,\nIn order to better assist you with the data you need, I need more clarity on the requirements.\nPlease do you have a moment for a brief huddle?", "yes i am available", "<@U041YKB0B3L>", "<@U0529D38TL3> kindly urgently avail me with the list of all GHS  processing transactions ", "<@U041YKB0B3L>", "Thanks ", "Hi <@U0529D38TL3> please assist in extracting a report for virtual account applications similar to the KYC completion rate report we receive on Retool.\n\nSpecifically, the following metrics for the week of 7th to 14th November so we can add it to our  all hands report:\n\t•\tInitiation Count: Total count of virtual account applications initiated.\n\t•\tSuccess Count: Total count of successful virtual account applications.\n\t•\tSuccess Rate: Percentage of successful applications compared to total initiations.\n\t•\tAverage Duration to Success: Average time taken from initiation to successful completion.", "<@U0529D38TL3> please share all GHS transactions in review ", "<@U041YKB0B3L>", "*Virtual Account Application Summary: Nov 7th - Nov 14th 2024*\n• _*Total Applications:*_ _*`144`*_\n• _*Approved Applications: `96`*_\n• _*Rejected Applications: `18`*_\n• _*On Hold:*_ _*`30`*_\n• _*Pending:*_ _*`0`*_\n• _*Average Duration:*_ _*`21 Hours`*_", "thank you", "<@U039Y5S1ZM2> <NAME_EMAIL> receives a referral bonus for just inviting people to join without them transacting, He referred 9 people to use the app, only 5 made a transaction and 4 didn't but he got the bonuses for the 9 of them, sadly some of them are Nigerian users but show UK users on the cx end. ", "Hello <@U0529D38TL3> please share all GHS transactions in review", "<@U041YKB0B3L>", "Hello <@U0529D38TL3>\n\nPlease can you help with this:\nA csv list for email + country\n\nFor the following segments.\n\nUK - app Users (Signups)\nUK- app Purchases (Transactions)\nUK - app VALUE (customers with high value transactions)", "I need more clarity on the 2nd and 3rd segments.\nFor second is it all uk users that have transacted all time?\n For 3rd what is the threshold that defines a user as high value? and what time frame to consider?", "For the first, is it all GB users - all time?", "We can do last 1 year", "<@U077YG045P0> UK signups past 365 days", "UK transacted user (active in past 365 days)", "<@U077YG045P0> Top Value (top 10 percentile)", "Thank you Frank", "Hi <@U0529D38TL3> <@U039Y5S1ZM2> Please help spool data for Virtual Account Application Summary: Nov 15th to Nov 21st", "<@U0529D38TL3> <@U039Y5S1ZM2> please help me with the percentage of KYC attempts for the month of October, that came from the Diaspora (US, UK, Canada, EU) versus Africa", "Hello <@U0529D38TL3> please help with Account statement for <mailto:<EMAIL>|<EMAIL>> in pdf ", "<@U0529D38TL3> kindly provide all GHS transaction In review ", "<@U039Y5S1ZM2> please assist", "<@U039Y5S1ZM2> please assist", "Is this not on retool?", "No its not", "actually i can get Approved Applications,Rejected Applications, On Hold and Pending, but I cant get the average duration to success", "So you want to get the duration from application to approval", "yes please", "Average time to approval is `2 hours, 59 minutes, and 16.27 seconds`", "thank you", "diaspora - ('US', 'GB', 'CA')\nafrica - ('NG', 'UG', 'KE', 'GH')", "is it possible to include EU for diaspora?", "<@U041YKB0B3L>", "hello <@U0529D38TL3> please provide account statement for <mailto:<EMAIL>|<EMAIL>> pdf", "<@U041YKB0B3L>", "hello <@U0529D38TL3> please share KES users with global accounts", "here <@U04H2N7CYNA>", "thank you", "Hi <@U0529D38TL3> please help users who were not KYC verified in the past 1 week (18th-25th NNov)", "Users that signed up within the stated period (18th - 25th Nov 2024) but didn't get KYC verified were `5,089`", "Can you spool please", "<@U034CN7L5R7>", "<@U0529D38TL3> please help with Account statement request for <mailto:<EMAIL>|<EMAIL>> from Jan 2024 pdf ", "<@U0529D38TL3> Also Account statement for <mailto:<EMAIL>|<EMAIL>>", "Hi <@U0529D38TL3> please share the average duration till success for virtual account applications for Nov 22nd to Nov 28th", "<@U0529D38TL3> Account statement for <mailto:<EMAIL>|<EMAIL>> from December 2023 ", "<@U03RL5Z70B0>", "12 Hours", "Thank you ", "<@U0529D38TL3> please re-confirm the average time,because last week, it was :\nAverage time to approval is `2 hours, 59 minutes, and 16.27 seconds`", "It's an accurate representation of the time it takes after an application is created to when the application is approved &amp; bank account is created.\n<@U03RL5Z70B0>", "<@U041YKB0B3L>\nstatement request for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>\nAccount statement for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>\nAccount statement for <mailto:<EMAIL>|<EMAIL>> from December 2023", "hello <@U0529D38TL3> please provide account statement for <mailto:<EMAIL>|<EMAIL>> on transactions made to ADEMOKUN YEMISI ESTHER OR YEMISI ESTHER ADEMOKUN from October 2022 till date", "<@U041YKB0B3L>", "Good day <@U0529D38TL3> please share Account statement for <mailto:<EMAIL>|<EMAIL>> ", "<@U0529D38TL3> Account statement for <mailto:<EMAIL>|<EMAIL>> from June 2023 to December 2023", "<@U0529D38TL3> Account statement for <mailto:<EMAIL>|<EMAIL>>", "Hello <@U0529D38TL3> kindly work on these requests ", "<@U041YKB0B3L>  For <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L> for <mailto:<EMAIL>|<EMAIL>>", "<@U0529D38TL3> please provide Account statement for <mailto:<EMAIL>|<EMAIL>> showing only transactions to  Charity orajiaka", "The recipient `<PERSON> orajiaka` does not exist in the records, however there was a `ORAJAKA CHARITY NMA`,  this is the closet and only recipient named charity.", "<@U041YKB0B3L>", "Hi <@U0529D38TL3>, can I please get the list of users who transacted in November?  (Deposits and withdrawals)", "<@U03DTMEDDJQ>", "Thank you Frank :pray::skin-tone-5:", "<@U0529D38TL3> please share Account statement for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>", "Good day <@U0529D38TL3> kindly provide Account statement for <mailto:<EMAIL>|<EMAIL>> showing transactions to <PERSON><PERSON> from January 2021 to November 2023 ", "<@U0529D38TL3> Account statement for <mailto:<EMAIL>|<EMAIL>> showing transactions to Egbukichi <PERSON> from 2023 February to November 2023", "FOR <mailto:<EMAIL>|<EMAIL>> to <PERSON><PERSON>", "TO EGBUKICHI OGECHI ALICIA", "hello <@U0529D38TL3> please share a list of all customers with 500 and above points.", "500 rewards point?", "thank youu", "Hello <@U0529D38TL3> please share Account statement for <mailto:<EMAIL>|<EMAIL>> from March 2024 ", "Good morning <@U0529D38TL3> please share Account statement for <mailto:<EMAIL>|<EMAIL>> from January ", "<@U0529D38TL3> Statement of account for <mailto:<EMAIL>|<EMAIL>> from august ", "<@U041YKB0B3L> for <mailto:<EMAIL>|<EMAIL>>", "for <mailto:<EMAIL>|<EMAIL>>", "Good day <@U0529D38TL3> please provide Statement of account for <mailto:<EMAIL>|<EMAIL>> from January till date ", "<@U0529D38TL3> Statement of account for <mailto:<EMAIL>|<EMAIL>> for 2024 ", "<@U041YKB0B3L>", "<@U0529D38TL3> please share account statement for <mailto:<EMAIL>|<EMAIL>> from Jan 2024 till date", "<@U0529D38TL3> please share a list of DLocal processing transactions. Kindly treat as urgent ", "<@U041YKB0B3L>", "hello <@U0529D38TL3> please share account statement for <mailto:<EMAIL>|<EMAIL>>", "<@U041YKB0B3L>", "<@U0529D38TL3> account statement for <mailto:<EMAIL>|<EMAIL>> from jan 2024", "<@U041YKB0B3L>", "<@U0529D38TL3> account statement for <mailto:<EMAIL>|<EMAIL>> for 2024"]