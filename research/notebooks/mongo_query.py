from pymongo import MongoClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get MongoDB connection string from environment variable
MONGO_URI = os.getenv('MONGO_URI')

def count_ng_users():
    try:
        # Connect to MongoDB
        client = MongoClient(MONGO_URI)
        
        # Select the database (replace 'your_database' with actual database name)
        db = client['your_database']
        
        # Count documents where country is "NG"
        count = db.bank_accounts.count_documents({"country": "NG"})
        
        print(f"Number of users with country NG: {count}")
        
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    count_ng_users() 