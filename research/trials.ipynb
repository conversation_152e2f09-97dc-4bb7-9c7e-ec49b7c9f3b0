{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\"\\nfrom IPython.display import Markdown, display'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "import json\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "from pydantic import BaseModel\n", "from typing import List, Dict, Any\n", "from langchain_core.prompts import ChatPromptTemplate\n", "import os\n", "import pathlib\n", "import re\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "from pydantic import Field\n", "from typing import Literal\n", "from typing import Annotated\n", "\n", "from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "from langchain.schema import HumanMessage, AIMessage\n", "\"\"\"\"\n", "from IPython.display import Markdown, display\"\"\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "from pydantic import BaseModel\n", "from typing import List, Dict, Any\n", "from langchain_core.prompts import ChatPromptTemplate\n", "import os\n", "import pathlib\n", "import re\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import Field\n", "from typing import Literal\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "import json\n", "from typing import List, Dict, Optional, TypedDict, Sequence, Annotated\n", "from dataclasses import dataclass\n", "from pathlib import Path\n", "from datetime import datetime\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import HumanMessage, AIMessage, BaseMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "import requests\n", "import json\n", "from dotenv import load_dotenv\n", "from typing import List, Dict, Optional\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "from IPython.display import Markdown, display\n", "\n", "# Define your state\n", "class AgentState(TypedDict):\n", "    messages: List[HumanMessage | AIMessage]\n", "    question: str=\"sam\"\n", "\n", "    # User engager\n", "    engager_done: bool=False\n", "    interpreted_question: str\n", "    status: str\n", "    engager_question:str\n", "\n", "\n", "    #table filter\n", "    filtered_tables:List[str]=[]\n", "\n", "    # Query generator\n", "    generated_query: str\n", "    mode: str\n", "    has_analytics: bool=False\n", "    plot_code: str\n", "    generator_message: str\n", "    filemodecols: List[str]\n", "    is_account_statement:bool=False\n", "\n", "    # Query reviewer\n", "    query_approved: bool=False\n", "    reviewer_message: str=\"\"\n", "\n", "    # Query execution\n", "    data: list\n", "    execution_error: str=None\n", "\n", "    # Responder\n", "    final_response: str"]}, {"cell_type": "code", "execution_count": 278, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'_TypedDictMeta' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[278], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mAgentState\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mquestion\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myes\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "\u001b[0;31mTypeError\u001b[0m: '_TypedDictMeta' object does not support item assignment"]}], "source": ["AgentState[\"question\"] =\"yes\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(state_schema=MessagesState)\n", "memory = MemorySaver()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class Message:\n", "    role: str  # 'human' or 'ai'\n", "    content: str\n", "    "]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='share a breakdown of how many virtual account users we have by country, sorted from highest to lowest.', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='To ensure I provide the most accurate information, could you please clarify the following:  - Are you interested in all users with virtual accounts or only those who have been active recently? - Should the breakdown include only specific countries or all countries where we have users? - Do you want to include any additional filters, such as account status or creation date?  These details will help me create the most useful response for your needs.\\n\\n```sql\\nTo ensure I provide the most accurate information, could you please clarify the following:\\n\\n- Are you interested in all users with virtual accounts or only those who have been active recently?\\n- Should the breakdown include only specific countries or all countries where we have users?\\n- Do you want to include any additional filters, such as account status or creation date?\\n\\nThese details will help me create the most useful response for your needs.\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='all users, return ans slack message and limit to jus 20 of them ,name and email , consider all countries, no other filter', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='Here is a list of 20 users with their names and emails:  1. chesh142 - <mailto:<EMAIL>|<EMAIL>> 2. amarh990 - <mailto:<EMAIL>|<EMAIL>> 3. rosem753 - <mailto:<EMAIL>|<EMAIL>> 4. lawal596 - <mailto:<EMAIL>|<EMAIL>> 5. k482157 - <mailto:<EMAIL>|<EMAIL>> 6. soloc24 - <mailto:<EMAIL>|<EMAIL>> 7. mokwu82 - <mailto:<EMAIL>|<EMAIL>> 8. abdel984 - <mailto:<EMAIL>|<EMAIL>> 9. kibab971 - <mailto:kibabajo<PERSON>@gmail.com|kibaba<PERSON><EMAIL>> 10. markj678 - <mailto:<EMAIL>|<EMAIL>> 11. julieokpetu4807 - <mailto:<EMAIL>|<EMAIL>> 12. bunmi595 - <mailto:<EMAIL>|<EMAIL>> 13. updat749 - <mailto:<EMAIL>|<EMAIL>> 14. jamal618 - <mailto:<EMAIL>|<EMAIL>> 15. henry323 - <mailto:<EMAIL>|<EMAIL>> 16. rasaq560 - <mailto:<EMAIL>|<EMAIL>> 17. sunda500 - <mailto:<EMAIL>|<EMAIL>> 18. marya954 - <mailto:<EMAIL>|<EMAIL>> 19. neilo416 - <mailto:<EMAIL>|<EMAIL>> 20. ayoom662 - <mailto:<EMAIL>|<EMAIL>>  Let me know if you need any more information!\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT \"userName\" AS \"Name\", \"email\" AS \"Email\"\\nFROM \"ACCOUNTS\"\\nLIMIT 20;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='i asked for how many virtual account, forgottent?', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='It seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:  - A count of virtual account users per country. - The results sorted by the number of users in descending order. - The top 20 countries in terms of user count. - Names and emails of users from these countries.  Could you please confirm if this is what you need or if there are any changes?\\n\\n```sql\\nIt seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:\\n\\n- A count of virtual account users per country.\\n- The results sorted by the number of users in descending order.\\n- The top 20 countries in terms of user count.\\n- Names and emails of users from these countries.\\n\\nCould you please confirm if this is what you need or if there are any changes?\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='how many users singed up today', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='3,484 users signed up today.\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT COUNT(*) AS \"Number of Signups Today\" FROM \"ACCOUNTS\" WHERE DATE(\"createdAt\") = CURRENT_DATE;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='tthnaks', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='Processing your request...', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["history"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["history = [{'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'share a breakdown of how many virtual account users we have by country, sorted from highest to lowest.'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'To ensure I provide the most accurate information, could you please clarify the following:  - Are you interested in all users with virtual accounts or only those who have been active recently? - Should the breakdown include only specific countries or all countries where we have users? - Do you want to include any additional filters, such as account status or creation date?  These details will help me create the most useful response for your needs.\\n\\n```sql\\nTo ensure I provide the most accurate information, could you please clarify the following:\\n\\n- Are you interested in all users with virtual accounts or only those who have been active recently?\\n- Should the breakdown include only specific countries or all countries where we have users?\\n- Do you want to include any additional filters, such as account status or creation date?\\n\\nThese details will help me create the most useful response for your needs.\\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'all users, return ans slack message and limit to jus 20 of them ,name and email , consider all countries, no other filter'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'Here is a list of 20 users with their names and emails:  1. chesh142 - <mailto:<EMAIL>|<EMAIL>> 2. amarh990 - <mailto:<EMAIL>|<EMAIL>> 3. rosem753 - <mailto:<EMAIL>|<EMAIL>> 4. lawal596 - <mailto:<EMAIL>|<EMAIL>> 5. k482157 - <mailto:<EMAIL>|<EMAIL>> 6. soloc24 - <mailto:<EMAIL>|<EMAIL>> 7. mokwu82 - <mailto:<EMAIL>|<EMAIL>> 8. abdel984 - <mailto:<EMAIL>|<EMAIL>> 9. kibab971 - <mailto:<EMAIL>|<EMAIL>> 10. markj678 - <mailto:<EMAIL>|<EMAIL>> 11. julieokpetu4807 - <mailto:<EMAIL>|<EMAIL>> 12. bunmi595 - <mailto:<EMAIL>|<EMAIL>> 13. updat749 - <mailto:<EMAIL>|<EMAIL>> 14. jamal618 - <mailto:<EMAIL>|<EMAIL>> 15. henry323 - <mailto:<EMAIL>|<EMAIL>> 16. rasaq560 - <mailto:<EMAIL>|<EMAIL>> 17. sunda500 - <mailto:<EMAIL>|<EMAIL>> 18. marya954 - <mailto:<EMAIL>|<EMAIL>> 19. neilo416 - <mailto:<EMAIL>|<EMAIL>> 20. ayoom662 - <mailto:<EMAIL>|<EMAIL>>  Let me know if you need any more information!\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT \"userName\" AS \"Name\", \"email\" AS \"Email\"\\nFROM \"ACCOUNTS\"\\nLIMIT 20;\\n```\\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'i asked for how many virtual account, forgottent?'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'It seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:  - A count of virtual account users per country. - The results sorted by the number of users in descending order. - The top 20 countries in terms of user count. - Names and emails of users from these countries.  Could you please confirm if this is what you need or if there are any changes?\\n\\n```sql\\nIt seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:\\n\\n- A count of virtual account users per country.\\n- The results sorted by the number of users in descending order.\\n- The top 20 countries in terms of user count.\\n- Names and emails of users from these countries.\\n\\nCould you please confirm if this is what you need or if there are any changes?\\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'how many users singed up today'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': '3,484 users signed up today.\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT COUNT(*) AS \"Number of Signups Today\" FROM \"ACCOUNTS\" WHERE DATE(\"createdAt\") = CURRENT_DATE;\\n```\\n```'}, {'role': 'user', 'user_id': 'U085MBZHUHF', 'message': 'tthnaks'}, {'role': 'assistant', 'user_id': 'U08A575QQCX', 'message': 'Processing your request...'}]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def convert_to_langchain_messages(messages: List[Message]) -> List[HumanMessage | AIMessage]:\n", "    \"\"\"Convert our Message objects to LangChain message objects\"\"\"\n", "    return [\n", "        HumanMessage(content=msg[\"message\"]) if msg[\"role\"] == \"user\" else AIMessage(content=msg[\"message\"])\n", "        for msg in messages\n", "    ]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["history =convert_to_langchain_messages(history)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='share a breakdown of how many virtual account users we have by country, sorted from highest to lowest.', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='To ensure I provide the most accurate information, could you please clarify the following:  - Are you interested in all users with virtual accounts or only those who have been active recently? - Should the breakdown include only specific countries or all countries where we have users? - Do you want to include any additional filters, such as account status or creation date?  These details will help me create the most useful response for your needs.\\n\\n```sql\\nTo ensure I provide the most accurate information, could you please clarify the following:\\n\\n- Are you interested in all users with virtual accounts or only those who have been active recently?\\n- Should the breakdown include only specific countries or all countries where we have users?\\n- Do you want to include any additional filters, such as account status or creation date?\\n\\nThese details will help me create the most useful response for your needs.\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='all users, return ans slack message and limit to jus 20 of them ,name and email , consider all countries, no other filter', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='Here is a list of 20 users with their names and emails:  1. chesh142 - <mailto:<EMAIL>|<EMAIL>> 2. amarh990 - <mailto:<EMAIL>|<EMAIL>> 3. rosem753 - <mailto:<EMAIL>|<EMAIL>> 4. lawal596 - <mailto:<EMAIL>|<EMAIL>> 5. k482157 - <mailto:<EMAIL>|<EMAIL>> 6. soloc24 - <mailto:<EMAIL>|<EMAIL>> 7. mokwu82 - <mailto:<EMAIL>|<EMAIL>> 8. abdel984 - <mailto:<EMAIL>|<EMAIL>> 9. kibab971 - <mailto:kibabajo<PERSON>@gmail.com|kibaba<PERSON><EMAIL>> 10. markj678 - <mailto:<EMAIL>|<EMAIL>> 11. julieokpetu4807 - <mailto:<EMAIL>|<EMAIL>> 12. bunmi595 - <mailto:<EMAIL>|<EMAIL>> 13. updat749 - <mailto:<EMAIL>|<EMAIL>> 14. jamal618 - <mailto:<EMAIL>|<EMAIL>> 15. henry323 - <mailto:<EMAIL>|<EMAIL>> 16. rasaq560 - <mailto:<EMAIL>|<EMAIL>> 17. sunda500 - <mailto:<EMAIL>|<EMAIL>> 18. marya954 - <mailto:<EMAIL>|<EMAIL>> 19. neilo416 - <mailto:<EMAIL>|<EMAIL>> 20. ayoom662 - <mailto:<EMAIL>|<EMAIL>>  Let me know if you need any more information!\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT \"userName\" AS \"Name\", \"email\" AS \"Email\"\\nFROM \"ACCOUNTS\"\\nLIMIT 20;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='i asked for how many virtual account, forgottent?', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='It seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:  - A count of virtual account users per country. - The results sorted by the number of users in descending order. - The top 20 countries in terms of user count. - Names and emails of users from these countries.  Could you please confirm if this is what you need or if there are any changes?\\n\\n```sql\\nIt seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:\\n\\n- A count of virtual account users per country.\\n- The results sorted by the number of users in descending order.\\n- The top 20 countries in terms of user count.\\n- Names and emails of users from these countries.\\n\\nCould you please confirm if this is what you need or if there are any changes?\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='how many users singed up today', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='3,484 users signed up today.\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT COUNT(*) AS \"Number of Signups Today\" FROM \"ACCOUNTS\" WHERE DATE(\"createdAt\") = CURRENT_DATE;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='tthnaks', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='Processing your request...', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["CONTEXT ={\n", "    \"INFO\": \"Supported Countries CODES and Their Currencies CODES according to OUR DATABASE\",\n", "    \"data\": {\n", "      \"Nigeria (NG)\": \"NGN (Nigerian Naira)\",\n", "      \"United Kingdom (GB)\": \"GBP (British Pound)\",\n", "      \"United States (US)\": \"USD (United States Dollar)\",\n", "      \"Canada (CA)\": \"CAD (Canadian Dollar)\",\n", "      \"Kenya (KE)\": \"KES (Kenyan Shilling)\",\n", "      \"Ghana (GH)\": \"GHS (Ghanaian Cedi)\",\n", "      \"Uganda (UG)\": \"UGX (Ugandan Shilling)\",\n", "      \"Cameroon (CM)\": \"XAF (Central African CFA Franc)\",\n", "      \"Ethiopia (ET)\": \"ETB (Ethiopian Birr)\",\n", "      \"Haiti (HT)\": \"HTG (Haitian Gourde)\",\n", "      \"Austria (AT)\": \"EUR (Euro)\",\n", "      \"Belgium (BE)\": \"EUR (Euro)\",\n", "      \"Croatia (HR)\": \"EUR (Euro)\",\n", "      \"Cyprus (CY)\": \"EUR (Euro)\",\n", "      \"Estonia (EE)\": \"EUR (Euro)\",\n", "      \"Finland (FI)\": \"EUR (Euro)\",\n", "      \"France (FR)\": \"EUR (Euro)\",\n", "      \"Germany (DE)\": \"EUR (Euro)\",\n", "      \"Greece (GR)\": \"EUR (Euro)\",\n", "      \"Ireland (IE)\": \"EUR (Euro)\",\n", "      \"Italy (IT)\": \"EUR (Euro)\",\n", "      \"Latvia (LV)\": \"EUR (Euro)\",\n", "      \"Lithuania (LT)\": \"EUR (Euro)\",\n", "      \"Luxembourg (LU)\": \"EUR (Euro)\",\n", "      \"Malta (MT)\": \"EUR (Euro)\",\n", "      \"Netherlands (NL)\": \"EUR (Euro)\",\n", "      \"Portugal (PT)\": \"EUR (Euro)\",\n", "      \"Slovakia (SK)\": \"EUR (Euro)\",\n", "      \"Slovenia (SI)\": \"EUR (Euro)\",\n", "      \"Spain (ES)\": \"EUR (Euro)\",\n", "      \"Romania (RO)\": \"EUR (Euro)\",\n", "      \"Bulgaria (BG)\": \"EUR (Euro)\",\n", "      \"Czech Republic (CZ)\": \"EUR (Euro)\",\n", "      \"Denmark (DK)\": \"EUR (Euro)\",\n", "      \"Hungary (HU)\": \"EUR (Euro)\",\n", "      \"Poland (PL)\": \"EUR (Euro)\",\n", "      \"Sweden (SE)\": \"EUR (Euro)\",\n", "      \"Norway (NO)\": \"EUR (Euro)\",\n", "      \"Ukraine (UA)\": \"EUR (Euro)\",\n", "      \"Australia (AU)\": \"AUD (Australian Dollar)\",\n", "      \"Russia (RU)\": \"RUB (Russian Ruble)\",\n", "      \"Burkina Faso (BF)\": \"XOF (West African CFA Franc)\",\n", "      \"Mali (ML)\": \"XOF (West African CFA Franc)\",\n", "      \"Senegal (SN)\": \"XOF (West African CFA Franc)\",\n", "      \"Togo (TG)\": \"XOF (West African CFA Franc)\",\n", "      \"Guinea (GN)\": \"GNF (Guinean Franc)\",\n", "      \"Benin (BJ)\": \"XOF (West African CFA Franc)\",\n", "      \"Ivory Coast (CI)\": \"XOF (West African CFA Franc)\",\n", "      \"Egypt (EG)\": \"EGP (Egyptian Pound)\",\n", "      \"Mexico (MX)\": \"MXN (Mexican Peso)\",\n", "      \"China (CN)\": \"CNY (Chinese Yuan)\",\n", "      \"India (IN)\": \"INR (Indian Rupee)\",\n", "      \"Philippines (PH)\": \"PHP (Philippine Peso)\",\n", "      \"Brazil (BR)\": \"BRL (Brazilian Real)\",\n", "      \"Uruguay (UY)\": \"UYU (Uruguayan Peso)\",\n", "      \"Pakistan (PK)\": \"PKR (Pakistani Rupee)\",\n", "      \"Rwanda (RW)\": \"RWF (Rwandan Franc)\",\n", "      \"South Africa (ZA)\": \"ZAR (South African Rand)\"\n", "    }\n", "  }"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["DB_STRUCTURE_PATH = r\"/Users/<USER>/Desktop/blessing_ai/afriex/sql-copilot/data/processed/database_structure.json\"\n", "with open(DB_STRUCTURE_PATH, \"r\") as f:\n", "        DATABASE = json.load(f)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def get_user_engament_prompt():\n", "    return f\"\"\"\n", "    You are SQL-COPILOT SPECIFICALLY BUILT FOR AFRIEX DATABASE IN AN AGENTIC multi-agent system designed to process database queries. Your primary responsibility is to analyze user questions and determine if they contain sufficient information to be translated into database queries.\n", "\n", "    **Task:** Understand user questions by following these guidelines:\n", "    \n", "    INPUT:\n", "       - You will be provided with a user question and the AFRIEX DATABASE STRUCTURE.\n", "\n", "    NOTE: \n", "    **🚨 MANDATORY: Follow these steps STRICTLY before generating any SQL query.**\n", "    \n", "    - **First, deeply analyze the user's question and ask creative clarifying questions.**\n", "    - **PRIORITIZE THE DATABASE STRUCTURE:** Unless the user explicitly asks for something outside the database structure, limit your understanding of the query to what can be retrieved from the database schema. Do not assume data or fields that do not exist in the database.\n", "\n", "    ---\n", "    \n", "    ## Creative Understanding Phase (DO NOT SKIP)\n", "    \n", "    - AI **MUST** analyze the user's question from multiple angles.\n", "    - AI **MUST** ask thoughtful, specific questions that demonstrate knowledge of the database structure.\n", "    - AI **MUST** consider possible business contexts behind the query.\n", "    - AI **MUST** explore potential edge cases or limitations in the data based on the database schema.\n", "    \n", "    **Example Creative Questioning Approach:**\n", "    \n", "    - **User:** \"Show me customer purchases.\"\n", "    - **AI:** `\"I'd like to make sure I understand exactly what you need about customer purchases. Based on the database structure:\n", "      - Would you like to see total purchase amounts by customer or individual transaction details?\n", "      - The database contains transactions with timestamps. Would you like to filter by a specific time period (e.g., last month, quarter, year)?\n", "      - Should I include all product categories, or are you interested in specific ones? (The database includes a 'product_category' field.)\n", "      - Purchases above a certain amount threshold? (The database has a 'purchase_amount' field.)\n", "      - Information about payment methods or purchase channels? (The database includes 'payment_method' and 'channel' fields.)\n", "      - Any specific customer segments (new vs. returning, premium tier, etc.)? (The database includes a 'customer_tier' field.)\n", "      \n", "      Understanding these details will help me create the most useful query for your needs.\"` (`clear = \"no\"`)\n", "    \n", "    - **User:** \"I want to see total purchases by product category for premium customers in Q1.\"\n", "    - **AI:** `\"Thanks for clarifying! Just to confirm, based on the database structure:\n", "      - You want total purchase amounts (not counts) grouped by product category (using the 'product_category' field).\n", "      - Only for customers in the premium tier (using the 'customer_tier' field).\n", "      - Limited to Q1 (Jan-Mar) of the current year (using the 'transaction_date' field).\n", "      \n", "      A few more questions to ensure the most valuable results:\n", "      - Would you like to see this broken down by month within Q1? (Using the 'transaction_date' field.)\n", "      - Should I include or exclude returned items? (The database includes a 'return_status' field.)\n", "      - Do you need any specific sorting (highest to lowest value, alphabetical by category)?\"` (`clear = \"no\"`)\n", "    \n", "    - **User:** \"Yes, that's right. Let's exclude returns and sort by highest value.\"\n", "    - **AI:** `\"Perfect! Based on the database structure, I'll generate a SQL query to show total purchase amounts by product category for premium customers in Q1, excluding returns (using the 'return_status' field) and sorted by highest value first.\"` (`clear = \"yes\"`, then generates SQL)\n", "    \n", "    ## 💡 Creative Clarification Guidelines:\n", "    \n", "    - **Anticipate needs:** Think beyond the literal request to what might truly help the user, but always stay within the bounds of the database structure.\n", "    - **Consider business context:** Ask about relevant metrics, KPIs, or business objectives, ensuring they align with available fields in the database.\n", "    - **Explore dimensions:** Suggest potential groupings, filters, or segmentations based on the database schema.\n", "    - **Propose alternatives:** Offer different analytical approaches that leverage the database structure effectively.\n", "    - **Confirm precision:** Clarify exact definitions (e.g., \"active user\") and ensure they map to fields in the database.\n", "    - **Address data limitations:** Proactively ask about handling NULL values, duplicates, or other data quality issues based on the database schema.\n", "     \n", "\n", "    4. **Database Structure:**\n", "       - Carefully analyze the structure of the database, including schemas, tables, and columns.\n", "       - Use JOIN operations if necessary to ensure accurate results, but only if the relationships between tables are clearly defined in the database schema.\n", "\n", "    NOTE: only allow (set status) for data retrieval (SELECT statements). Avoid DELETE, UPDATE, or INSERT or acessing sensitive  information like user passwords operations.\n", "    7. **Output Format:**\n", "       - Return a dictionary with the following keys:\n", "         - `status`: `\"allowed\"` or `\"disallowed\"` based on query validy.\n", "         - engager_question: \"Your follow up question if engagement_done is not True yet\n", "         - `engagement_done`: `True` or `False`, meaning you are done with user engagement and have confirmed what the user wants exactly. When `False`, it means you are still engaging the user.\n", "         - `interpreted_question`: \"Interpretation of the question in full after confirmation. Write this very clearly, as if you are explaining to a 10-year-old, and ensure it aligns with the database structure.\"..start with \"Write an interpred version of the question like \"I want .....\n", "\n", "----START------\n", "    {{CONTEXT}}\n", "----END--------\n", "\n", "\n", "DATABASE STRUCTURE:\n", "\n", "---START___\n", "{{database_structure}}\n", "\n", "---END---\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["query = \"or users who signed up in the last 60 days with country 'us' and have performed at least one deposit transaction, what is the average amount of their first deposit transactions, do not ask me further questions pls, set engager done to yes\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["query  = \"please help with Account statement for 2024 for  <EMAIL> , set engager done to yes, no further questions\" "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["query = \"Hi\""]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["\n", "from datetime import datetime\n", "import pytz\n", "\n", "# Get current date and time in UTC\n", "utc_now = datetime.now(pytz.utc)\n", "# Format the current date and time in words\n", "current_date = utc_now.strftime(\"%A, %B %d, %Y at %I:%M %p UTC\")\n", "def get_query_generator_prompt():\n", "    return f\"\"\"\n", "     You are SQL-COPILOT SPECIFICALLY BUILT FOR AFRIEX DATABASE IN AN AGENTIC multi-agent system designed to process database queries. Your primary responsibily is to generate sql-query based on the full interpreted user question\n", "    \n", "    \n", "    2. **Syntax Formatting:**\n", "       - Enclose **column names**, **table names**, and **schema names** in double quotes (`\"`).\n", "       - Enclose **string values** in single quotes (`'`).\n", "       - Ensure case sensitivity is respected as per Snowflake's rules.\n", "       - Example:\n", "         ```sql\n", "         SELECT \"columnName\"\n", "         FROM \"schemaName\".\"tableName\"\n", "         WHERE \"columnName\" = 'value';\n", "         ```\n", "\n", "    3. **Query Restrictions:**\n", "       - Only generate queries for data retrieval (SELECT statements). Avoid DELETE, UPDATE, or INSERT or acessing information like user passwords operations.\n", "       - Validate that referenced fields exist in the database schema.\n", "\n", "    4. **Database Structure:**\n", "       - Carefully analyze the structure of the database, including schemas, tables, and columns.\n", "       - Use JOIN operations if necessary to ensure accurate results.\n", "\n", "    5 **Variant Data Handling:**\n", "       - If querying variant fields, extract data correctly without creating non-existent columns.\n", "       - Properly handle variant data types to ensure accurate querying.\n", "\n", "    6. **Response Modes:**\n", "       - **Direct Response Mode:** Provide a concise numerical or textual answer directly.\n", "       - **File Mode:** Generate a file for large datasets. Include a message notifying users about potentially long processing times.\n", "       - **Analytics Mode:** Generate plots using Python (pandas, matplotlib) when analysis is required. The function must always be named `plot_data`.\n", "\n", "    7. **Output Format:**\n", "       - Return a dictionary with the following keys:\n", "        \n", "         - `mode`: `\"direct\"`, `\"file\"`, or `\"analytics\"` depending on the response type., Do not set to file mode when you are still asking questions please, follow striclly\n", "         - `reason`: A reason for disallowance (empty string if allowed).\n", "         - `generated_query`: The generated SQL query.\n", "         - `filemodecols`: Column names for **both File Mode and Analytics Mode** outputs in correct order. These will be used to load the data into a dataframe.\n", "         - `plot_code`: Python code for generating plots (only in analytics mode).\n", "         is_account_statement: True or False: if question has to do with user account statement\n", "      \n", "    7. **Additional Notes:**\n", "       - Avoid SQL code blocks (```) in responses.\n", "       - Always round numerical values to 2 decimal places unless specified otherwise.\n", "       - Ensure the queried data matches the `filemodecols` for proper dataframe loading in both File Mode and Analytics Mode.\n", "       - For time-related queries, use the current date as a reference and avoid assumptions about specific dates or periods.\n", "       - Creatively generate queries by analyzing unique values and mixed data types (e.g., variant fields), adhering strictly to the provided database structure.\n", "       - In analytics mode, keep plots simple, insightful, and free from unnecessary complexity.\n", "       - Only use available tables; do not create new ones.\n", "\n", "    8. **Plot Code Requirements (For Analytics Mode):**\n", "       - Use pandas and matplotlib to process the dataframe and generate a plot.\n", "       - Save the plot in the `static/plots/` directory with a unique filename using `uuid`.\n", "       - Return the path to the saved plot file.\n", "    9.\n", "     - K<PERSON><PERSON><PERSON><PERSON> LOOK THE EXAMPLES QUERY AND USE THEM WHEN NECESSAYR AS THEY ARE ACCURATE QUERY FOR THOSE QUESTIONS SEEN\n", "    **Examples:**\n", "    - **Direct Response Mode:** \n", "      User asks, \"How many customers were paid today?\" → Provide a direct numerical answer.\n", "    - **File Mode:** \n", "      User requests a list of customers → Generate a file with appropriate column headers. Include `filemodecols` for dataframe compatibility.\n", "    - **Analytics Mode:** \n", "      User asks for a trend analysis → Generate a plot using the `plot_data` function. Include `filemodecols` for dataframe compatibility.\n", "\n", "    10:\n", "    Esnure consistency in the response of your query especially if user is askng the same question , make sure you are consistent with your answer and sql query you generate\n", "    **Final Note:**\n", "    Ensure all queries are valid, efficient, and aligned with the user's request. Always prioritize clarity and accuracy in both queries and responses. Remember, `filemodecols` must be included for both **File Mode** and **Analytics Mode** to ensure proper dataframe generation.\n", "    NOTE: YOU MAY BE INTEGRATED ON OTHER PLATFORMS LIKE SLACK ETC, IF THAT IS IT , YOU WILL BE PROVIDED WITH USER QUERIES HISTORY TO THE BOT  FROM OLDEST TO LATEST AND USER CURRENT QUESTION FOR HELPFUL CONVERSATIONAL FLOW WITHIN THE SAME THREAD OR CHAT PAGE\n", "            IF PLATFORM IS SLACK : THE CONVERSATION HISTORY IS REFRERING TO CURRENT THREAD\n", "    NOTE: ONLY SELECT FILE MODE WHEN NECESSARY , FOR EXAMPLE SOME DATA CAN BE EASILTY TO THE USER INSEAD OF SENDING FILE\n", "    NOTE: WHEN DEALING WITH CATEGORICAL COLUMN, ONLY USE THE UNIQUE VALUES PRESENT IN THE DATABASE STRUCTURE PROVIDED, DO NOT FORMULATE VALUES, <PERSON><PERSON><PERSON><PERSON> THIS STRITCLY\n", "    MORE INFORMATION ABOUT THE DATABASE:\n", "         ----START------\n", "            {{CONTEXT}}\n", "         ----END--------\n", "\n", "DATABASE STRUCTURE:\n", "\n", "{{database_structure}}\n", "\n", "EXAMPLES OF QUESTION AND QUERY\n", "\n", "{{training_examples}}\n", "NOTE: QUESTION THAT HAS TO DO WITH GENERATING ACCOUNT STATEMENT FOR A SPECIFIC USE IS FILE MODE AND ALWAYS USE THE EXAMPLE PROVIDED FOR GENERATING ACCOUNT STATEMENT and only do this id user explicitly ask to generate account statement for a specific user email\n", "NOTE: IF QUESTION REQUESTED FOR ACCOUNT STATEMENT , FORGET ABIOUT THE DATABASE STRUCTURE PASSED AND USE THE EXAMPLE QUERY FOR ACCOUNT STATEMENT , THAT ALWAYS WROKS\n", "\n", "\n", "\n", "CURRENT DATE (HELPFUL IN DATE RELATED QUERIES, USE THIS PLEASE):{current_date}\n", "USER QUESTION TO GENERATE QUERY FOR:\n", "\n", "{{user_question}}\n", "\n", "    \"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["\n", "def get_query_reviewer_prompt():\n", "    return f\"\"\"\n", "\n", "\n", "You are **SQL-COPILOT**, an AI agent specifically designed to review, optimize, and validate SQL queries for the **Afriex database** in Snowflake. multi-agent system  Your role is critical in ensuring that queries are syntactically correct, secure, optimized for performance, and aligned with the database schema and user requirements.\n", "\n", "---\n", "\n", "### Key Responsibilities:\n", "\n", "1. **Query Validation:**\n", "   - Ensure the query correctly translates the user's question.\n", "   - Verify that all necessary tables, columns, and joins are included.\n", "   - Confirm that filters, conditions, and output formats align with the user's intent.\n", "\n", "2. **Security Analysis:**\n", "   - Check for potential SQL injection risks.\n", "   - Ensure proper escaping of user inputs.\n", "   - Validate access permissions for requested tables.\n", "\n", "3. **Performance Optimization:**\n", "   - Identify bottlenecks such as missing indexes or inefficient joins.\n", "   - Avoid unnecessary operations or excessive data retrieval.\n", "   - Optimize query structure for faster execution.\n", "\n", "4. **Output Format Validation:**\n", "   - Ensure the query returns data in a format suitable for presentation (e.g., properly labeled columns).\n", "   - Round numerical values to **2 decimal places** unless specified otherwise.\n", "   - Match queried data to `filemodecols` for proper dataframe loading in both **File Mode** and **Analytics Mode**.\n", "\n", "5. **Special Cases:**\n", "   - For **account statement generation**, strictly follow the provided example query if the user explicitly requests it for a specific email.\n", "   - Use the current date as a reference for time-related queries unless otherwise specified.\n", "\n", "      \n", "    7. **Additional Notes:**\n", "       - Avoid SQL code blocks (```) in responses.\n", "       - Always round numerical values to 2 decimal places unless specified otherwise.\n", "       - Ensure the queried data matches the `filemodecols` for proper dataframe loading in both File Mode and Analytics Mode.\n", "       - For time-related queries, use the current date as a reference and avoid assumptions about specific dates or periods.\n", "       - Creatively generate queries by analyzing unique values and mixed data types (e.g., variant fields), adhering strictly to the provided database structure.\n", "       - In analytics mode, keep plots simple, insightful, and free from unnecessary complexity.\n", "       - Only use available tables; do not create new ones.\n", "\n", "**Syntax Formatting:**\n", "       - Enclose **column names**, **table names**, and **schema names** in double quotes (`\"`).\n", "       - Enclose **string values** in single quotes (`'`).\n", "       - Ensure case sensitivity is respected as per Snowflake's rules.\n", "       - Example:\n", "         ```sql\n", "         SELECT \"columnName\"\n", "         FROM \"schemaName\".\"tableName\"\n", "         WHERE \"columnName\" = 'value';\n", "         ```\n", "RESPONSIBILITIES:\n", "\n", "Evaluate query correctness:\n", "\n", "Does the query correctly translate the user's question?\n", "Are all necessary tables and joins included?\n", "Are filters and conditions properly applied?\n", "Will the query return the expected data format?\n", "\n", "\n", "Analyze for security vulnerabilities:\n", "\n", "Check for SQL injection risks (verify parameterized queries are used)\n", "Ensure proper escaping of user inputs\n", "Verify appropriate access permissions for requested tables\n", "\n", "\n", "Assess performance optimization:\n", "\n", "Identify potential performance bottlenecks\n", "Check for missing indexes on filtered or joined columns\n", "Review for unnecessary operations or excessive data retrieval\n", "Evaluate join efficiency and order\n", "\n", "\n", "Output format validation:\n", "\n", "Verify the query returns data in a format suitable for presenting to the user\n", "Check for proper labeling of output columns\n", "\n", "NOTE : DATABASE SPECIFICATION IS IN SNOWFLAKE\n", "\n", "DATABASE SPECIFICATIONS:\n", "\n", "Type: [Specify database type: PostgreSQL, MySQL, MongoDB, etc.]\n", "Schema: [Include relevant tables, fields, relationships, and constraints]\n", "Performance considerations: [Any specific details about database size, indexes, etc.]\n", "\n", "OUTPUT FORMAT:\n", "If the query is correct and optimal:\n", "\"query_approved:True or False\n", "\"reviewer_message\": [Detailed explanation of issues]\n", "RECOMMENDATIONS: [Specific suggestions for improvement]\"\n", "    MORE INFORMATION ABOUT THE DATABASE:\n", "         ----START------\n", "            {{CONTEXT}}\n", "         ----END--------\n", "\n", "DATABASE STRUCTURE:\n", "\n", "{{database_structure}}\n", "\n", "EXAMPLES OF QUESTION AND QUERY VETTED BY DATA BASE OWNER\n", "{{training_examples}}\n", "NOTE: QUESTION THAT HAS TO DO WITH GENERATING ACCOUNT STATEMENT FOR A SPECIFIC USE IS FILE MODE AND ALWAYS USE THE EXAMPLE PROVIDED FOR GENERATING ACCOUNT STATEMENT and only do this id user explicitly ask to generate account statement for a specific user email\n", "NOTE: IF QUESTION REQUESTED FOR ACCOUNT STATEMENT , FORGET ABIOUT THE DATABASE STRUCTURE PASSED AND USE THE EXAMPLE QUERY FOR ACCOUNT STATEMENT , THAT ALWAYS WORKS\n", "\n", "\n", "USER QUESTION FOR WHICH QUERY WAS GENERATED:\n", "--START--\n", "{{user_question}}\n", "\n", "--END----\n", "\n", "GENERATED QUERY FROM AI AGENT\n", "\n", "  --START---\n", "{{generated_query}}\n", "\n", "  --END----\n", "    \"\"\"\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def table_filter_prompt():\n", "\n", "    return f\"\"\"\n", "    You are a SQL query analyzer. Your task is to analyze user questions and identify the required tables from the database structure before any query is generated.\n", "\n", "    **ANALYSIS STEPS:**\n", "    1. Understand the user's question/requirement\n", "    2. Identify key information being requested\n", "    3. List all tables needed to fulfill this request\n", "    4. Explain why each table is necessary\n", "    5. Identify any relationships between tables that will be needed (joins/merges)\n", "    6.check if the query has to do with user asking for account statement for a specific user and generating a raw csv file\n", "     e.g \"I need account <NAME_EMAIL> for 2024\" , coly classify as account statemnt if the question looks like something like this account statment for a specific email\n", "    **Respo\n", "    NOTE - WHEN NOT <PERSON><PERSON><PERSON> WITH USER QUERYM YOU CAN ASK FURTHER QUESTIONS TO UNDERSTAND THE USER\n", "    NOTE: Only analyze and list required tables. DO NOT generate any SQL queries at this stage.\n", "\n", "    DATABASE STRUCTURE:\n", "--START----\n", "    {{database_structure}}\n", "---END----\n", "\n", "    MORE INFORMATION ABOUT THE DATABASE:\n", "         ----START------\n", "            {{CONTEXT}}\n", "         ----END--------\n", "\n", "    USER QUESTION:\n", "\n", "    \n", "    {{interpreted_question}}\n", "\n", "\n", "\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def response_formulation_prompt():\n", "    return f\"\"\"\n", "You are the Response Formulation Agent in a multi-agent database Q&A system. Your role is to transform technical database results into clear, natural language responses that directly answer the user's original question.\n", "\n", "---\n", "\n", "### RESPONSIBILITIES:\n", "1. Interpret raw query results in the context of the original user question.\n", "2. Format numerical data appropriately (e.g., percentages, currency, etc.).\n", "3. Prioritize the most relevant information in your response.\n", "4. Add necessary context and explanations when needed.\n", "5. Structure the response for maximum clarity and readability.\n", "6. <PERSON>le special cases like empty results, too many results, or partial results.\n", "\n", "---\n", "\n", "### INPUT:\n", "1. The original user question.\n", "2. The query results (from database query).\n", "3. Mode: either \"file\" or \"direct\".\n", "4. Execution error: Indicates whether an error occurred during execution.\n", "\n", "---\n", "\n", "### MODE-SPECIFIC INSTRUCTIONS:\n", "- **Direct Mode**: Summarize the data clearly, especially for multiple rows.\n", "- **File Mode**: If no execution error occurred, inform the user they can download their file. You will not be given the dataset, as it is large. Example response: \"Please find the generated file attached below.\", You can be more creative here based on user question, do not sound robotic\n", "\n", "If an execution error occurs, assume the user is non-technical. Simply tell them an error occurred and encourage them to try again without providing technical details.\n", "\n", "---\n", "\n", "### RESPONSE GUIDELINES:\n", "1. Always begin with a direct answer to the user's question.\n", "2. Use natural, conversational language rather than technical terms.\n", "3. Format numbers appropriately:\n", "   - Currency: Specify the currency and use proper formatting (e.g., $1,234.56).\n", "   - Percentages: Use one decimal place unless more precision is needed (e.g., 12.3%).\n", "   - Large numbers: Use appropriate units (e.g., 1.2M instead of 1,200,000) for readability.\n", "   - Dates: Format consistently based on the user's locale or use ISO format (YYYY-MM-DD).\n", "4. Include relevant quantitative context (e.g., percentages, comparisons, trends).\n", "5. For complex results, structure the information using bullet points or short paragraphs.\n", "6. Mention any limitations or caveats about the data if appropriate.\n", "7. For empty results, explain what was searched for and suggest possible reasons for no matches.\n", "8. When results are truncated, mention the total count and the criteria used to show the subset.\n", "\n", "---\n", "\n", "### FORMATTING SPECIFICS:\n", "- **Currency**: Always specify the currency and use proper formatting (e.g., $1,234.56).\n", "- **Percentages**: Use one decimal place unless more precision is contextually important (e.g., 12.3%).\n", "- **Large Numbers**: Use appropriate units (e.g., 1.2M instead of 1,200,000) when it improves readability.\n", "- **Dates**: Format consistently based on the user's locale or use ISO format (YYYY-MM-DD).\n", "- **Tables**: For result sets with 2-5 rows, consider a simple markdown table.\n", "- **Lists**: Use bullet points for 3-7 items; use numbered lists when sequence matters.\n", "\n", "---\n", "\n", "### EXAMPLES:\n", "\n", "#### Example 1:\n", "**User Question**: \"What were our top 3 selling products in January?\"\n", "**Query Result**: \n", "[\n", "  \"product_name\": \"Wireless Headphones\", \"units_sold\": 1254, \"revenue\": 125400,\n", "  \"product_name\": \"Smartphone Cases\", \"units_sold\": 978, \"revenue\": 29340,\n", "  \"product_name\": \"Bluetooth Speakers\", \"units_sold\": 842, \"revenue\": 84200\n", "]\n", "\n", "**Good Response**:\n", "\"In January, your top 3 selling products were:\n", "\n", "1. Wireless Headphones - 1,254 units sold ($125,400 in revenue)\n", "2. Smartphone Cases - 978 units sold ($29,340 in revenue)\n", "3. Bluetooth Speakers - 842 units sold ($84,200 in revenue)\n", "\n", "Wireless Headphones significantly outperformed other products, accounting for 23% of our total January revenue.\"\n", "\n", "**Bad Response**:\n", "\"Query returned 3 rows. Product_name: Wireless Headphones, units_sold: 1254, revenue: 125400. Product_name: Smartphone Cases, units_sold: 978, revenue: 29340. Product_name: Bluetooth Speakers, units_sold: 842, revenue: 84200.\"\n", "\n", "---\n", "\n", "### ERROR HANDLING PROTOCOL:\n", "- Always use a friendly, non-technical approach.\n", "- Do not explain technical details.\n", "- Simply and warmly encourage the user to try again.\n", "- Provide minimal, reassuring guidance.\n", "\n", "#### Error Response Template:\n", "\"Oops! Something went wrong while processing your request. \n", "\n", "No worries—this happens sometimes. Could you please try your request again? If the issue continues, our support team is ready to help.\"\n", "\n", "---\n", "\n", "### INPUT PARAMETERS:\n", "- Original User Question\n", "- Query Results\n", "- Mode: 'file' or 'direct'\n", "- Execution Error\n", "\n", "---\n", "\n", "### FUNDAMENTAL COMMUNICATION RULES:\n", "- Be human-like and approachable.\n", "- Prioritize user experience.\n", "- Keep responses concise and clear.\n", "- Never use technical jargon.\n", "\n", "---\n", "\n", "MODE: {{mode}}\n", "USER QUESTION: {{interpreted_question}}\n", "QUERY RESULT: {{data}}\n", "ANY EXECUTION ERROR: {{execution_error}}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["training_examples_path = r\"/Users/<USER>/Desktop/blessing_ai/afriex/sql-copilot/data/examples.json\"\n", "with open(training_examples_path, \"r\") as f:\n", "        training_examples = json.load(f)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "from pydantic import BaseModel\n", "from typing import List, Optional, Tuple, Any\n", "import json\n", "import snowflake.connector\n", "import os\n", "from dotenv import load_dotenv\n", "#load_dotenv()\n", "\n", "\n", "\n", "def execute_sql_with_retry(\n", "    initial_query: str,\n", "    schema= os.getenv('SNOWFLAKE_SCHEMA')\n", "):\n", "    \n", "    \"\"\"\n", "    Execute SQL query with AI-powered retry on failure\n", "    Returns: (data, error_message, final_query)\n", "    \"\"\"\n", "    \n", "    connection = snowflake.connector.connect(\n", "    user=os.getenv('SNOWFLAKE_USER'),\n", "    password=os.getenv('SNOWFLAKE_PASSWORD'),\n", "    account= os.getenv('SNOWFLAKE_ACCOUNT'),  # Corrected from 'account' to 'account_identifier'\n", "    role=os.getenv('ROLE'),\n", "    database=os.getenv('DATABASE'),\n", "    schema= schema,#os.getenv('SNOWFLAKE_SCHEMA'),  # Corrected from 'SNOW_FLAKE_SCHEMA' to 'SNOWFLAKE_SCHEMA'\n", "    warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),\n", "    session_parameters={'QUERY_TAG': 'py-copy-into'},)\n", "    \n", "    current_query = initial_query\n", "    \n", "    try:\n", "            cursor = connection.cursor()\n", "            result = cursor.execute(current_query)\n", "            return result.fetchall(), None, current_query\n", "            \n", "    \n", "    except Exception as error:\n", "            error_message = str(error)\n", "           \n", "            return None, error_message, current_query\n", "                \n", "          \n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 280, "metadata": {}, "outputs": [], "source": ["query = \"What are the top 10 countries by user count\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query  = \"please help with Account statement for 2024 for  <EMAIL> , set engager done to yes, no further questions\" "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query  = \"please help with Account statement for 2024 for  <EMAIL> , set engager done to yes, no further questions\" "]}, {"cell_type": "code", "execution_count": 301, "metadata": {}, "outputs": [], "source": ["query  = \"How many users signed up today , set engager done to yes, no further questions\" "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["## user engager agent\n", "\n", "TEMPERATURE = 0.092\n", "MODEL = \"gpt-4o-mini\"\n", "\n", "class UserEngagnerOutput(BaseModel):\n", "    engagement_done:bool\n", "    interpreted_question:str\n", "    status:str\n", "    engager_question:str\n", "\n", "class QueryGeneratorOutput(BaseModel):\n", "    generated_query: str\n", "    mode: str\n", "    has_analytics:bool\n", "    plot_code: str\n", "    filemodecols: List[str]\n", "    is_account_statement:bool\n", "\n", "class QueryReveiwerOutput(BaseModel):\n", "    query_approved: bool\n", "    reveiwer_message: str\n", "\n", "\n", "class TableFilterOuput(BaseModel):\n", "    tables: List[str]\n", "\n", "class Responderoutput(BaseModel):\n", "    message: List[str]\n", "\n", "def get_filtered_db(state):\n", "\n", "    database_structure = [\n", "            table for table in DATABASE if table[\"table_name\"] in state[\"filtered_tables\"]\n", "        ]\n", "    \n", "    return database_structure\n", "\n", "\n", "def user_engager(state):\n", "\n", "\n", "   prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", get_user_engament_prompt()),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "   \n", "   model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)\n", "   model = model.with_structured_output(UserEngagnerOutput)\n", "   prompt = prompt.invoke({\n", "            \"messages\": state[\"messages\"],\n", "            \"CONTEXT\":f\"{CONTEXT}\",\n", "            \"database_structure\":f\"{DATABASE}\"\n", "        })\n", "   \n", "   response = model.invoke(prompt)\n", "\n", "   return {\"engager_done\":response.engagement_done,\"interpreted_question\":response.interpreted_question,\"status\":response.status,\"engager_question\":response.engager_question}\n", "\n", "\n", "def tables_filter(state):\n", "   \n", "   prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", table_filter_prompt()),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "   \n", "   model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)\n", "   model = model.with_structured_output(TableFilterOuput)\n", "   prompt = prompt.invoke({\n", "            \"messages\": state[\"messages\"],\n", "            \"CONTEXT\":f\"{CONTEXT}\",\n", "            \"database_structure\":f\"{DATABASE}\",\n", "            \"interpreted_question\":state[\"interpreted_question\"]\n", "        })\n", "   \n", "   response = model.invoke(prompt)\n", "\n", "   return {\"filtered_tables\":response.tables}\n", "\n", "\n", "\n", "def query_generator(state):\n", "   \n", "\n", "   database_structure = get_filtered_db(state)\n", "   print(database_structure)\n", "   prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", get_query_generator_prompt()),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "   temperature = 0\n", "   model = ChatOpenAI(model=MODEL, temperature=temperature)\n", "   model = model.with_structured_output(QueryGeneratorOutput)\n", "   prompt = prompt.invoke({\n", "            \"messages\": state[\"messages\"],\n", "            \"CONTEXT\":f\"{CONTEXT}\",\n", "            \"database_structure\":f\"{database_structure}\",\n", "            \"training_examples\":f\"{training_examples}\",\n", "            \"user_question\":state[\"interpreted_question\"]\n", "        })\n", "   \n", "   response = model.invoke(prompt)\n", "\n", "   return {\n", "       \"generated_query\": response.generated_query,\n", "       \"mode\": response.mode,\n", "       \"has_analytics\": response.has_analytics,\n", "       \"plot_code\": response.plot_code,\n", "       \"filemodecols\": response.filemodecols,\n", "       \"is_account_statement\":response.is_account_statement\n", "   }\n", "\n", "\n", "\n", "\n", "def responder(state):\n", "   if state[\"mode\"] == \"file\":\n", "       data = None\n", "   else:\n", "       data = state[\"data\"]\n", "\n", "   print(f\"Data generated{data}\")\n", "   prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", response_formulation_prompt()),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "   temperature = 0.7\n", "   model = ChatOpenAI(model=MODEL, temperature=temperature)\n", "   model = model.with_structured_output(Responderoutput)\n", "   prompt = prompt.invoke({\n", "       \"messages\": state[\"messages\"],\n", "            \"interpreted_question\":state[\"interpreted_question\"],\n", "             \"data\":data,\n", "              \"execution_error\":state[\"execution_error\"],\n", "                \"mode\":state[\"mode\"],\n", "        })\n", "   \n", "   response = model.invoke(prompt)\n", "\n", "   return {\n", "       \"final_response\": response.message\n", "   }\n", "\n", "\n", "def query_reviewer(state):\n", "   \n", " \n", "\n", "   prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", get_query_reviewer_prompt()),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "   \n", "   model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)\n", "   model = model.with_structured_output(QueryReveiwerOutput)\n", "   prompt = prompt.invoke({\n", "            \"messages\": state[\"messages\"],\n", "            \"CONTEXT\":f\"{CONTEXT}\",\n", "            \"database_structure\":f\"{database_structure}\",\n", "            \"training_examples\":f\"{training_examples}\",\n", "            \"user_question\":state[\"interpreted_question\"],\n", "            \"generated_query\":state[\"generated_query\"]\n", "        })\n", "   \n", "   response = model.invoke(prompt)\n", "\n", "   return {\"query_approved\":response.query_approved,\"reveiwer_message\":response.reveiwer_message}\n", "\n", "\n", "\n", "def query_executor(state):\n", "    \n", "    \"\"\"\n", "    Execute SQL query with AI-powered retry on failure\n", "    Returns: (data, error_message, final_query)\n", "    \"\"\"\n", "\n", "    if state[\"is_account_statement\"]:\n", "        schema = \"TRANSFORMED\"\n", "    \n", "    connection = snowflake.connector.connect(\n", "    user=os.getenv('SNOWFLAKE_USER'),\n", "    password=os.getenv('SNOWFLAKE_PASSWORD'),\n", "    account= os.getenv('SNOWFLAKE_ACCOUNT'),  # Corrected from 'account' to 'account_identifier'\n", "    role=os.getenv('ROLE'),\n", "    database=os.getenv('DATABASE'),\n", "    schema= os.getenv('SNOWFLAKE_SCHEMA'),  # Corrected from 'SNOW_FLAKE_SCHEMA' to 'SNOWFLAKE_SCHEMA'\n", "    warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),\n", "    session_parameters={'QUERY_TAG': 'py-copy-into'},)\n", "    \n", "    current_query = state[\"generated_query\"]\n", "    error_message = None\n", "    data = None\n", "    \n", "    try:\n", "            cursor = connection.cursor()\n", "            result = cursor.execute(current_query)\n", "            data =  result.fetchall()\n", "\n", "    except Exception as error:\n", "            error_message = str(error)\n", "           \n", "    \n", "    return {\"data\":data,\"execution_error\":error_message}\n", "\n", "def conditional_edge_table_fitler(state: AgentState) -> Literal[\"tables_filter\", \"responder\"]:\n", "    if state[\"engager_done\"]:\n", "        return \"tables_filter\"\n", "    else:\n", "        return \"responder\"\n", "\n", "\n", "def conditional_edge_query_generator(state: AgentState) -> Literal[\"query_generator\"]:\n", "    if state[\"filtered_tables\"]:\n", "        return \"query_generator\"\n", "    else:\n", "        return \"query_generator\"\n", "\n", "\n", "def conditional_edge_query_executor(state: AgentState) -> Literal[\"tables_filter\", \"responder\"]:\n", "    if not state[\"engager_done\"]:\n", "        return \"responder\"\n", "    else:\n", "        return \"tables_filter\"\n", "    \n", "\n", "\"\"\"def conditional_edge_query_reveiwer(state: AgentState) -> Literal[\"query_generator\", \"query_executor\"]:\n", "    if  state[\"generated_query\"]:\n", "        return \"query_executor\"\n", "    else:\n", "         return \"generated_query\"\"\"\n", "    \n", "\n", "def conditional_edge_responder(state: AgentState) -> Literal[END, \"tables_filter\"]:\n", "    if not state[\"engager_done\"]:\n", "        return END\n", "    else:\n", "        return \"tables_filter\"\n", "\n", "\n", "workflow = StateGraph(state_schema=AgentState)\n", "workflow.add_edge(START, \"user_engager\")\n", "workflow.add_node(\"user_engager\", user_engager)\n", "workflow.add_node(\"query_generator\", query_generator)  \n", "workflow.add_node(\"query_executor\", query_executor)  \n", "workflow.add_node(\"responder\", responder)  \n", "workflow.add_node(\"tables_filter\", tables_filter)  \n", "#workflow.add_node(\"query_reviewer\", query_reviewer)  \n", "\n", "\n", "workflow.add_edge(\"tables_filter\",\"query_generator\")\n", "workflow.add_edge(\"query_generator\",\"query_executor\")\n", "workflow.add_edge(\"query_executor\",\"responder\")\n", "workflow.add_edge(\"responder\", END)\n", "\n", "workflow.add_conditional_edges(#\n", "    \"user_engager\",\n", "    conditional_edge_responder\n", ")\n", "\n", "\"\"\"workflow.add_conditional_edges(#\n", "    \"query_generator\",\n", "    conditional_edge_query_reveiwer\n", ")\n", "\n", "\"\"\"\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)\n", "    \n", "# Fetch conversation history from the API\n", "\n", "conversation_id = \"12345\"\n", "\n", "config = {\"configurable\": {\"thread_id\": conversation_id}}\n", "language = \"English\"\n", "    \n", "if not history:\n", "        # New conversation\n", "        input_messages = [HumanMessage(content=query)] if query else [HumanMessage(content=\"Let's get started\")]\n", "else:\n", "        # Existing conversation\n", "    #history = convert_to_langchain_messages(history)\n", "    input_messages = history + [HumanMessage(content=query)] if query else history\n", "    \n", "output = app.invoke(\n", "        {\"messages\": input_messages},\n", "        config\n", "    ) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[{'table_name': 'ACCOUNTS', 'description': 'Information about user accounts', 'structure': {'_id': {'type': 'VARCHAR(50)', 'description': 'Unique identifier for the account'}, 'updatedAt': {'type': 'TIMESTAMP_NTZ(9)', 'description': 'Timestamp when the account was last updated'}, 'createdAt': {'type': 'TIMESTAMP_NTZ(9)', 'description': 'Timestamp when the account was created'}, 'userId': {'type': 'VARCHAR(50)', 'description': 'Identifier of the user associated with the account'}, 'referrer': {'type': 'VARCHAR(********)', 'description': 'Identifier or name of the referrer for the account'}, 'email': {'type': 'VARCHAR(256)', 'description': 'Email address associated with the account'}, 'phone': {'type': 'VARCHAR(256)', 'description': 'Phone number linked to the account'}, 'country': {'type': 'VARCHAR(5)', 'description': \"Country code representing the account holder's location\"}, 'userName': {'type': 'VARCHAR(256)', 'description': 'Username for account login and identification'}, 'isAdmin': {'type': 'BOOLEAN', 'description': 'Indicates whether the account has administrative privileges'}, 'password': {'type': 'VARIANT', 'description': 'Encrypted or hashed password for account security'}, 'history': {'type': 'VARIANT', 'description': 'Historical data related to account activities'}, 'role': {'type': 'VARCHAR(256)', 'description': 'Role or permissions associated with the account'}, 'devices': {'type': 'VARIANT', 'description': 'Details of devices linked to the account'}, 'otp': {'type': 'VARIANT', 'description': 'One-time password or authentication information'}, 'permissions': {'type': 'VARIANT', 'description': 'List or metadata describing account permissions'}, 'attempts': {'type': 'NUMBER(38,0)', 'description': 'Number of login or access attempts'}}, 'categorical_field_description': [{'name': 'country', 'unique_values': ['US', 'CM', 'GB', 'NG', 'GH']}, {'name': 'isAdmin', 'unique_values': ['TRUE', 'FALSE']}, {'name': 'role', 'unique_values': ['compliance.member', 'growth.manager', 'operations.member', 'customer_care', 'customer.support.manager', 'engineering.manager', 'finance.manager', 'compliance.manager', 'user', 'finance.member', 'operations.manager', 'product.eng.qa.design.member', 'growth.member', 'customer.support.member', 'engineering.member', 'product.eng.qa.design.manager']}], 'example_row': {'_id': '12a3b4c5d6e7f8g9h0i1j2k3', 'updatedAt': '2023-05-15 12:34:56.789', 'createdAt': '2023-05-15 11:22:33.444', 'userId': '12a3b4c5d6e7f8g9h0i1j2k3', 'referrer': '<EMAIL>', 'phone': '**********', 'country': 'FR', 'userName': 'randomUser123', 'isAdmin': True, 'password': {'hash': '$2b$10$whfehgfjhrghu', 'isDeactivated': True, 'version': 2}, 'attempts': 5}}, {'table_name': 'TRANSACTIONS', 'description': 'Information about transactions', 'structure': {'_id': {'type': 'VARCHAR(50)', 'description': 'Unique identifier'}, 'updatedAt': {'type': 'TIMESTAMP_NTZ(9)', 'description': 'Last update timestamp'}, 'createdAt': {'type': 'TIMESTAMP_NTZ(9)', 'description': 'Creation timestamp'}, 'processor': {'type': 'VARCHAR(20)', 'description': 'Transaction processor'}, 'processorTransactionId': {'type': 'VARCHAR(256)', 'description': \"Processor's transaction identifier\"}, 'internalTransactionId': {'type': 'VARCHAR(256)', 'description': 'Internal transaction identifier'}, 'sourceUserId': {'type': 'VARCHAR(50)', 'description': 'Source user identifier'}, 'sourceAsset': {'type': 'VARCHAR(5)', 'description': 'Source asset symbol'}, 'sourceAmount': {'type': 'NUMBER(38,25)', 'description': 'Source amount'}, 'sourceCountry': {'type': 'VARCHAR(5)', 'description': 'Source country code'}, 'destinationUserId': {'type': 'VARCHAR(50)', 'description': 'Destination user identifier'}, 'destinationAsset': {'type': 'VARCHAR(5)', 'description': 'Destination asset symbol'}, 'destinationAmount': {'type': 'NUMBER(38,25)', 'description': 'Destination amount'}, 'destinationCountry': {'type': 'VARCHAR(5)', 'description': 'Destination country code'}, 'status': {'type': 'VARCHAR(20)', 'description': 'Transaction status'}, 'type': {'type': 'VARCHAR(20)', 'description': 'Transaction type'}, 'tierId': {'type': 'VARCHAR(50)', 'description': 'Tier identifier'}, 'sourceAccountDetails': {'type': 'VARIANT', 'description': 'Source account details'}, 'sourceAccountId': {'type': 'VARCHAR(50)', 'description': 'Source account identifier'}, 'destinationAccountDetails': {'type': 'VARIANT', 'description': 'Destination account details'}, 'destinationAccountId': {'type': 'VARCHAR(50)', 'description': 'Destination account identifier'}, 'channel': {'type': 'VARCHAR(20)', 'description': 'Transaction channel'}, 'processorResponseMeta': {'type': 'VARIANT', 'description': 'Processor response metadata'}, 'rates': {'type': 'VARIANT', 'description': 'Transaction rates'}, 'fee': {'type': 'NUMBER(38,25)', 'description': 'Transaction fee'}, 'initiatedBy': {'type': 'VARCHAR(50)', 'description': 'User who initiated the transaction'}, 'ledgerTransactionIds': {'type': 'VARCHAR(********)', 'description': 'Ledger transaction identifiers'}, 'voidTransactionIds': {'type': 'VARCHAR(********)', 'description': 'Voided transaction identifiers'}, 'ip': {'type': 'VARCHAR(256)', 'description': 'IP address'}, 'otcRate': {'type': 'VARIANT', 'description': 'OTC rate'}, 'depositTransactionReference': {'type': 'VARCHAR(********)', 'description': 'Deposit transaction reference'}, 'deviceInfo': {'type': 'VARIANT', 'description': 'Device information'}, 'narration': {'type': 'VARCHAR(********)', 'description': 'Transaction description'}, 'meta': {'type': 'VARIANT', 'description': 'Additional metadata'}}, 'example_row': {'_id': '*********0abcdef*********0abcdef', 'updatedAt': '2024-09-04 10:00:00.000', 'createdAt': '2023-03-24 10:00:00.000', 'processor': 'PAYPAL', 'processorTransactionId': 'JohnDoe-*********0abcdef-*************', 'internalTransactionId': 'JohnDoe-*********0abcdef-*************', 'sourceUserId': 'john_doe_user_id', 'sourceAsset': 'EUR', 'sourceAmount': 50.0, 'sourceCountry': 'DE', 'destinationUserId': 'abcdef*********0abcdef*********0', 'destinationAsset': 'USD', 'destinationAmount': 55.0, 'destinationCountry': 'US', 'status': 'SUCCESS', 'type': 'DEPOSIT', 'tierId': None, 'sourceAccountDetails': {}, 'sourceAccountId': 'abcdef*********0abcdef*********0', 'destinationAccountDetails': {'accountName': 'JOHN DOE', 'accountNumber': '*********', 'accountPhone': '*********0', 'bankCode': '123456', 'bankName': 'Bank of America', 'country': 'US', 'currency': 'USD'}, 'destinationAccountId': 'BANK_ACCOUNT', 'channel': 'BANK_ACCOUNT', 'processorResponseMeta': {'sessionId': 'abcdef*********0abcdef*********0', 'transactionStatusDescription': 'Approved or completed successfully'}, 'rates': {'EUR': {'USD': '1.10', 'EUR': '1'}}, 'fee': 0.0, 'initiatedBy': 'john_doe_initiator_id', 'ledgerTransactionIds': 'abcdef*********0abcdef*********0', 'voidTransactionIds': None, 'ip': '*************', 'otcRate': {}, 'depositTransactionReference': 'john_doe_deposit_reference', 'deviceInfo': {}, 'narration': 'Transaction by John Doe', 'meta': {'balanceAfter': [{'balances': {'USD': 100.0, 'EUR': 0}, 'userId': 'abcdef*********0abcdef*********0'}]}}, 'categorical_field_description': [{'name': 'processor', 'unique_values': ['PROVIDUS', 'DLOCAL', 'TERRAPAY', 'STRIPE', 'VFD', 'FAIRMONEY', 'HUB2', 'ZILMONEY', 'BEYONIC', 'FINCRA', 'ZENITH', 'CELLULANT', 'MONO', 'CHAPA', 'MONIEPOINT', 'ALPAY', 'PAYSTACK', 'ZEEPAY', 'JUICYWAY']}, {'name': 'sourceAsset', 'unique_values': ['NGN', 'EUR', 'GHS', 'ETB', 'CAD', 'ZAR', 'EGP', 'USD', 'PKR', 'GBP', 'UGX', 'KES', 'XOF', 'XAF']}, {'name': 'destinationAsset', 'unique_values': ['NGN', 'EUR', 'GHS', 'ETB', 'CAD', 'ZAR', 'EGP', 'USD', 'PKR', 'GBP', 'UGX', 'KES', 'XOF', 'XAF']}, {'name': 'sourceCountry', 'unique_values': ['UG', 'US', 'KE', 'other countries may be included as needed']}, {'name': 'destinationCountry', 'unique_values': ['UG', 'US', 'KE', 'other countries may be included as needed']}, {'name': 'status', 'unique_values': ['RETRY', 'CANCELLED', 'UNKNOWN', 'PROCESSING', 'SUCCESS', 'IN_REVIEW', 'FAILED', 'REFUNDED', 'PENDING', 'DISPUTE_RESOLVED']}, {'name': 'type', 'unique_values': ['SWAP', 'TRANSFER', 'REFERRAL', 'WITHDRAW', 'REVERSAL', 'DEPOSIT']}, {'name': 'channel', 'unique_values': ['CARD', 'INTERNAL', 'BANK_ACCOUNT', 'ACH_BANK_ACCOUNT', 'VIRTUAL_BANK_ACCOUNT', 'MOBILE_MONEY', 'WIDGET', 'ADMIN', 'INTERAC']}]}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='share a breakdown of how many virtual account users we have by country, sorted from highest to lowest.', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='To ensure I provide the most accurate information, could you please clarify the following:  - Are you interested in all users with virtual accounts or only those who have been active recently? - Should the breakdown include only specific countries or all countries where we have users? - Do you want to include any additional filters, such as account status or creation date?  These details will help me create the most useful response for your needs.\\n\\n```sql\\nTo ensure I provide the most accurate information, could you please clarify the following:\\n\\n- Are you interested in all users with virtual accounts or only those who have been active recently?\\n- Should the breakdown include only specific countries or all countries where we have users?\\n- Do you want to include any additional filters, such as account status or creation date?\\n\\nThese details will help me create the most useful response for your needs.\\n```', additional_kwargs={}, response_metadata={}),\n", "  HumanMessage(content='all users, return ans slack message and limit to jus 20 of them ,name and email , consider all countries, no other filter', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='Here is a list of 20 users with their names and emails:  1. chesh142 - <mailto:<EMAIL>|<EMAIL>> 2. amarh990 - <mailto:<EMAIL>|<EMAIL>> 3. rosem753 - <mailto:<EMAIL>|<EMAIL>> 4. lawal596 - <mailto:<EMAIL>|<EMAIL>> 5. k482157 - <mailto:<EMAIL>|<EMAIL>> 6. soloc24 - <mailto:<EMAIL>|<EMAIL>> 7. mokwu82 - <mailto:<EMAIL>|<EMAIL>> 8. abdel984 - <mailto:<EMAIL>|<EMAIL>> 9. kibab971 - <mailto:kibabajo<PERSON>@gmail.com|kibaba<PERSON><EMAIL>> 10. markj678 - <mailto:<EMAIL>|<EMAIL>> 11. julieokpetu4807 - <mailto:<EMAIL>|<EMAIL>> 12. bunmi595 - <mailto:<EMAIL>|<EMAIL>> 13. updat749 - <mailto:<EMAIL>|<EMAIL>> 14. jamal618 - <mailto:<EMAIL>|<EMAIL>> 15. henry323 - <mailto:<EMAIL>|<EMAIL>> 16. rasaq560 - <mailto:<EMAIL>|<EMAIL>> 17. sunda500 - <mailto:<EMAIL>|<EMAIL>> 18. marya954 - <mailto:<EMAIL>|<EMAIL>> 19. neilo416 - <mailto:<EMAIL>|<EMAIL>> 20. ayoom662 - <mailto:<EMAIL>|<EMAIL>>  Let me know if you need any more information!\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT \"userName\" AS \"Name\", \"email\" AS \"Email\"\\nFROM \"ACCOUNTS\"\\nLIMIT 20;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", "  HumanMessage(content='i asked for how many virtual account, forgottent?', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='It seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:  - A count of virtual account users per country. - The results sorted by the number of users in descending order. - The top 20 countries in terms of user count. - Names and emails of users from these countries.  Could you please confirm if this is what you need or if there are any changes?\\n\\n```sql\\nIt seems there might have been a misunderstanding. The previous agent is seeking confirmation on your request to ensure they provide the correct information. They are asking if you want:\\n\\n- A count of virtual account users per country.\\n- The results sorted by the number of users in descending order.\\n- The top 20 countries in terms of user count.\\n- Names and emails of users from these countries.\\n\\nCould you please confirm if this is what you need or if there are any changes?\\n```', additional_kwargs={}, response_metadata={}),\n", "  HumanMessage(content='how many users singed up today', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='3,484 users signed up today.\\n\\n```sql\\n*SQL Query:*\\n```sql\\nSELECT COUNT(*) AS \"Number of Signups Today\" FROM \"ACCOUNTS\" WHERE DATE(\"createdAt\") = CURRENT_DATE;\\n```\\n```', additional_kwargs={}, response_metadata={}),\n", "  HumanMessage(content='tthnaks', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='Processing your request...', additional_kwargs={}, response_metadata={}),\n", "  HumanMessage(content='Hi', additional_kwargs={}, response_metadata={})],\n", " 'engager_done': <PERSON><PERSON><PERSON>,\n", " 'interpreted_question': 'I want to know how many users signed up today.',\n", " 'status': 'allowed',\n", " 'engager_question': 'How can I assist you further?'}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["output"]}, {"cell_type": "code", "execution_count": 261, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WITH cte AS (\n", "    SELECT\n", "        t.\"date\" AS \"Date\",\n", "        UPPER(u.\"full_name\") AS \"Full Name\",\n", "        t.\"transaction_type\" AS \"Transaction Type\",\n", "        t.\"state\" AS \"Transaction status\",\n", "        t.\"channel\" AS \"Channel\",\n", "        t.\"amount\" AS \"Amount\",\n", "        t.\"currency\" AS \"Currency\",\n", "        UPPER(t.\"card_full_details\" :bankName::STRING) AS \"Sender Bank\",\n", "        UPPER(t.\"card_full_details\" :accountName::STRING) AS \"Sender Account Name\",\n", "        t.\"card_full_details\" :accountNumber::STRING AS \"Sender Account Number\",\n", "        t.\"currency\" || '-->' || t.\"fulfillment_asset\" AS \"Corridor Pair\",\n", "        t.\"fulfillment_asset\" AS \"Fulfillment Asset\",\n", "        t.\"fulfillment_asset_value\" AS \"Fulfillment Asset Value\",\n", "        t.\"bank_account_info\" :accountNumber::STRING AS \"Recipent account Number\",\n", "        UPPER(t.\"bank_account_info\" :accountName::STRING) AS \"Recipent account Name\",\n", "        t.\"bank_account_info\" :accountPhone::STRING AS \"Recipent Phone\",\n", "        UPPER(t.\"bank_account_info\" :bankName::STRING) AS \"Recipent Bank Name\",\n", "        t.\"bank_account_info\" :bankCode::STRING AS \"Recipent Bank Code\",\n", "        t.\"transfer_ref\" AS \"Transfer Reference\",\n", "        u.\"country\" AS \"Country\",\n", "        u.\"email\" AS \"Email\",\n", "        CASE WHEN t.\"currency\" = 'USD' THEN t.\"amount\" ELSE t.\"amount_usd\" END AS amount_usd_case,\n", "        t.\"amount_usd\" AS amount_usd,\n", "        t.\"created_at\"\n", "    FROM\n", "        transformed.transactions t\n", "        JOIN transformed.users u ON t.\"user_id\" = u.\"id\"\n", "    WHERE\n", "        LOWER(u.\"email\") = '<EMAIL>'\n", "        AND t.\"date\" >= '2024-01-01'\n", "        AND t.\"date\" <= '2024-12-31'\n", "),\n", "\n", "agg AS (\n", "    SELECT \n", "        \"Transaction Type\", ROUND(SUM(amount_usd_case),2) AS sum_usd_case, ROUND(SUM(amount_usd),2) AS sum_usd\n", "    FROM cte\n", "    WHERE \"Transaction status\" = 'SUCCESS'\n", "    GROUP BY 1\n", "    ORDER BY 1\n", "),\n", "\n", "rec AS (\n", "    SELECT \n", "        \"Date\",\n", "        \"Full Name\",\n", "        \"Transaction Type\",\n", "        \"Transaction status\",\n", "        \"Channel\",\n", "        \"Amount\",\n", "        \"Currency\",\n", "        \"Corridor Pair\",\n", "        \"Fulfillment Asset\",\n", "        \"Fulfillment Asset Value\",\n", "        \"Recipent account Number\",\n", "        \"Recipent account Name\",\n", "        \"Recipent Phone\",\n", "        \"Recipent Bank Name\",\n", "        \"Transfer Reference\"\n", "    FROM cte\n", "    ORDER BY \"created_at\"\n", ")\n", "\n", "SELECT * FROM rec\n"]}], "source": ["print(output[\"generated_query\"])"]}, {"cell_type": "code", "execution_count": 272, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(app.get_graph().draw_mermaid_png()))\n", "except Exception as e:\n", "    print(e)\n", "    # This requires some extra dependencies and is optional\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, END\n", "from typing import TypedDict, Annotated, Literal\n", "\n", "# Define your state\n", "class AgentState(TypedDict):\n", "    question: str\n", "    is_clear: bool\n", "    query: str\n", "    query_approved: bool\n", "    execution_success: bool\n", "    needs_followup: bool\n", "    response: str\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import List, Dict, Optional, TypedDict, Sequence, Annotated\n", "from dataclasses import dataclass\n", "from pathlib import Path\n", "from datetime import datetime\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import HumanMessage, AIMessage, BaseMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "from src.prompts import chat_prompt\n", "from langchain_openai import ChatOpenAI\n", "from src.models import Phase2Generation,Phase1Generation\n", "from config  import TEMPERATURE\n", "import os\n", "import requests\n", "import json\n", "from dotenv import load_dotenv\n", "from typing import List, Dict, Optional\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "\n", "\n", "@dataclass\n", "class Message:\n", "    role: str  # 'human' or 'ai'\n", "    content: str\n", "    timestamp: str=None\n", "\n", "QUESTIONS_PATH = \"./data/config_files/questions.json\"\n", "with open(QUESTIONS_PATH, \"r\") as f:\n", "    questions = json.load(f)\n", "\n", "\n", "from pydantic import BaseModel, Field\n", "from typing import List\n", "\n", "class Phase2Generation(BaseModel):\n", "    theme_title: str\n", "    question: str\n", "    Situation: List[str]\n", "    Task: List[str]\n", "    Action: List[str]\n", "    Results_and_Transitions: List[str] = Field(..., alias=\"Results and Transitions\")\n", "    Personal_Lessons: List[str] = <PERSON>(..., alias=\"Personal Lessons\")\n", "    Observations_of_Others: List[str] = Field(..., alias=\"Observations of Others\")\n", "    Professional_Connection: List[str] = Field(..., alias=\"Professional Connection\")\n", "\n", "    class Config:\n", "        allow_population_by_field_name = True\n", "\n", "\n", "class Phase1Generation(BaseModel):\n", "    end:str\n", "    message:str\n", "    pop_theme_generation:str\n", "\n", "prompt_template = None\n", "MODEL = \"gpt-4o-mini\"\n", "def initialize_workflow(model) -> StateGraph:\n", "    \"\"\"Initialize LangGraph workflow\"\"\"\n", "    workflow = StateGraph(state_schema=MessagesState)\n", "    memory = MemorySaver()\n", "\n", "    def call_model(state: MessagesState):\n", "        prompt = prompt_template.invoke({\"messages\": state[\"messages\"], \"language\": state[\"language\"]})\n", "        response = model.invoke(prompt)\n", "        return {\"messages\": [response]}\n", "\n", "    workflow.add_edge(START, \"model\")\n", "    workflow.add_node(\"model\", call_model)\n", "    return workflow.compile(checkpointer=memory)\n", "\n", "\n", "def setup_prompt_template(theme: int, resume: str,full_history=None,form_response=None,generate_theme=\"NO\") -> ChatPromptTemplate:\n", "    \"\"\"Set up the prompt template\"\"\"\n", "    return ChatPromptTemplate.from_messages([\n", "        (\"system\", chat_prompt(theme, resume,full_history,form_response,generate_theme)),\n", "        MessagesPlaceholder(variable_name=\"messages\")\n", "    ])\n", "def fetch_conversation_history(conversation_id: str) -> List[Message]:\n", "    \"\"\"\n", "    Fetch conversation history from the API using the conversation ID.\n", "    \"\"\"\n", "    x_api_key = os.getenv(\"BACKEND_XAPI_KEY\")\n", "    base_url = os.getenv(\"BACKEND_BASE_URL\")\n", "    url = f\"{base_url}/v3/api/custom/jordan/ai-chat/get-messages/{conversation_id}?x-project={x_api_key}\"\n", "    \n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()  # Raise an error for bad responses\n", "        data = response.json()[\"data\"]  # First JSON parse\n", "        print(data)\n", "        #\n", "        data = json.loads(data)\n", "        print(f\"Data {data}\")\n", "        print(f\"Type: {type(data)}\")\n", "        # Parse the API response into Message objects\n", "        messages = []\n", "        for item in data:\n", "            role = item.get(\"role\", \"unknown\")\n", "            content = item.get(\"content\", \"\")\n", "            timestamp = datetime.now().isoformat()  # Use current timestamp if not provided\n", "            messages.append(Message(role=role, content=content))\n", "        return messages\n", "    except requests.RequestException as e:\n", "        print(f\"Error fetching conversation history: {e}\")\n", "        return []\n", "\n", "\n", "def convert_to_langchain_messages(messages: List[Message]) -> List[HumanMessage | AIMessage]:\n", "    \"\"\"Convert our Message objects to LangChain message objects\"\"\"\n", "    converted_messages = []\n", "    for msg in messages:\n", "        if msg.role == \"human\":\n", "            converted_messages.append(HumanMessage(content=msg.content))\n", "        else:\n", "            converted_messages.append(AIMessage(content=msg.content))\n", "    return converted_messages\n", "\n", "def ai_chat(query: str, conversation_id: str, theme_id: int, resume: str, full_history=None, form_response=None, generate_theme=\"NO\") -> str:\n", "    \"\"\"Main chat function that processes queries and manages conversation\"\"\"\n", "    class State(TypedDict):\n", "        messages: List[HumanMessage | AIMessage]\n", "        language: str\n", "\n", "    # Initialize model and workflow\n", "    model = ChatOpenAI(model=MODEL, temperature=TEMPERATURE)\n", "    model = model.with_structured_output(Phase2Generation)\n", "    prompt = prompt_template.invoke({\n", "            \"messages\": state[\"messages\"], \n", "            \"language\": state[\"language\"]\n", "        })\n", "        response = model.invoke(prompt)\n", "        return {\"messages\": [response]}\n", "    if generate_theme == \"YES\":\n", "        model = model.with_structured_output(Phase2Generation)\n", "    else:\n", "        model = model.with_structured_output(Phase1Generation)\n", "\n", "    workflow = StateGraph(state_schema=State)\n", "    \n", "    def call_model(state: State):\n", "        prompt_template = setup_prompt_template(theme_id, resume, full_history, form_response, generate_theme)\n", "        prompt = prompt_template.invoke({\n", "            \"messages\": state[\"messages\"], \n", "            \"language\": state[\"language\"]\n", "        })\n", "        response = model.invoke(prompt)\n", "        return {\"messages\": [response]}\n", "    \n", "    workflow.add_edge(START, \"model\")\n", "    workflow.add_node(\"model\", call_model)\n", "    \n", "    memory = MemorySaver()\n", "    app = workflow.compile(checkpointer=memory)\n", "    \n", "    # Fetch conversation history from the API\n", "    history = fetch_conversation_history(conversation_id)\n", "    \n", "    config = {\"configurable\": {\"thread_id\": conversation_id}}\n", "    language = \"English\"\n", "    \n", "    if not history:\n", "        # New conversation\n", "        input_messages = [HumanMessage(content=query)] if query else [HumanMessage(content=\"Let's get started\")]\n", "    else:\n", "        # Existing conversation\n", "        history = convert_to_langchain_messages(history)\n", "        input_messages = history + [HumanMessage(content=query)] if query else history\n", "    \n", "    output = app.invoke(\n", "        {\"messages\": input_messages, \"language\": language},\n", "        config\n", "    )\n", "    \n", "    if generate_theme == \"YES\":\n", "        structured_message = output[\"messages\"][0]\n", "        output = structured_message.json(by_alias=True)  # This returns a JSON string.\n", "    else:\n", "        structured_message = output[\"messages\"][0]\n", "        output = structured_message.json()  # This returns a JSON string.\n", "        output = json.loads(output)\n", "\n", "        message = output.get(\"message\")\n", "        print(output)\n", "    \n", "    return output\n", "\n", "\n", "\n", "# Example usage:\n", "if __name__ == \"__main__\":\n", "    #conversation_id = \"12345\"  # Replace with the actual conversation ID\n", "    query = \"Hello let us continue\"\n", "    theme_id = 1\n", "    resume = \"Emergency Response Specialist\"\n", "    conversation_id = 1\n", "    response = ai_chat(query, conversation_id, theme_id, resume)\n", "    print(response)"]}], "metadata": {"kernelspec": {"display_name": "asql", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}