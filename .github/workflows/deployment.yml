name: "Deploy SQL Copilot backend"

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to use for deployment'
        type: environment
        required: true

# Keep current run from being cancelled by a new run if it's still in progress
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

env:
  GIT_TAG: "${{ github.sha }}"

permissions:
  id-token: write # This is required for aws oidc connection
  contents: write # This is required for actions/checkout
  pull-requests: write # This is required for gh bot to comment PR

jobs:
  normalize_environment:
    name: Normalize environment
    runs-on: ubuntu-latest
    outputs:
      normalized-env: ${{ env.environment }}
    steps:
      - name: Set environment for push events
        if: github.event_name == 'push'
        run: |
          echo "environment=prod" >> $GITHUB_ENV

      - name: Normalize environment variable
        env:
          ENVIRONMENT: ${{ github.event.inputs.environment }}
        run: |
          if [[ -n "$ENVIRONMENT" ]]; then
            echo "environment=$ENVIRONMENT" >> $GITHUB_ENV
          else
            echo "Using environment from GITHUB_ENV"
          fi

  deploy:
    needs: normalize_environment
    name: Deploying to ${{needs.normalize_environment.outputs.normalized-env}}
    runs-on: ubuntu-latest
    environment: ${{ needs.normalize_environment.outputs.normalized-env }}
    env:
      environment: ${{ needs.normalize_environment.outputs.normalized-env }}
      env: ${{ needs.normalize_environment.outputs.normalized-env }}
      domain_name: ${{ vars.DOMAIN_NAME }}
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials from AWS account
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT }}:role/${{vars.AWS_ROLE}}
          aws-region: ${{ vars.AWS_REGION }}
          role-session-name: GitHub-OIDC

      - name: Get Project Info
        id: project
        env:
          REPO: ${{ github.repository }}
          ECR_REGISTRY: ${{ vars.AWS_ACCOUNT }}.dkr.ecr.${{ vars.AWS_REGION }}.amazonaws.com
        run: |
          PROJECT_ID=`echo $REPO | sed 's/.*\///' | tr '[:upper:]' '[:lower:]'`
          echo "project_id=$PROJECT_ID" >> $GITHUB_ENV
          echo "ecr_registry=$ECR_REGISTRY" >> $GITHUB_ENV

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: ${{ vars.AWS_ACCOUNT }}
          mask-password: "true"

      - name: Deploy "Create ECR Repo" CloudFormation stack
        id: deploy-ecr
        uses: aws-actions/aws-cloudformation-github-deploy@v1
        with:
          name: ${{ env.project_id }}-ecr-stack
          template: cloudformation/ecr-template.yml
          no-fail-on-empty-changeset: "1"
          parameter-overrides: >
            ProjectId=${{ env.project_id }}
          tags: |
            [
              { "Key": "Environment", "Value": "${{ env.env }}" },
              { "Key": "ProjectId", "Value": "${{ env.project_id }}" }
            ]

      - name: Build, tag, and push Image to ECR
        id: build_backend_image
        run: |
          IMG_TAG="${{env.ecr_registry}}/${{ env.project_id }}-backend:${{ env.GIT_TAG }}"

          docker build -t $IMG_TAG .
          docker push $IMG_TAG
          
          echo "backend_image=$IMG_TAG" >> $GITHUB_ENV 

      - name: Deploy main CloudFormation stack
        id: deploy-services
        uses: aws-actions/aws-cloudformation-github-deploy@v1
        with:
          name: ${{ env.project_id }}-${{ env.env }}-services-stack
          template: cloudformation/services-template.yml
          capabilities: "CAPABILITY_AUTO_EXPAND, CAPABILITY_IAM"
          no-fail-on-empty-changeset: "1"
          no-execute-changeset: "0"
          timeout-in-minutes: 20
          parameter-overrides: >-
            ProjectId=${{ env.project_id }},
            BackendImage=${{ env.backend_image }},
            Env=${{ env.env }},
            ACMCertificateArn="arn:aws:acm:${{ vars.AWS_REGION }}:${{ vars.AWS_ACCOUNT }}:certificate/${{ secrets.ACM_CERTIFICATE_ID }}"
          tags: |
            [
              { "Key": "Env", "Value": "${{ env.env }}" },
              { "Key": "ProjectId", "Value": "${{ env.project_id }}" },
              { "Key": "BackendImage", "Value": "${{ env.backend_image }}" }
            ]

      - name: Deploy route53 CloudFormation stack
        id: deploy-route53
        if: env.domain_name != '' && env.domain_name
        uses: aws-actions/aws-cloudformation-github-deploy@v1
        with:
          name: ${{ env.project_id }}-${{ env.env }}-cname-stack
          template: cloudformation/cname-template.yml
          capabilities: CAPABILITY_NAMED_IAM
          no-fail-on-empty-changeset: '1'
          parameter-overrides: >-
            ProjectId=${{ env.project_id }},
            DomainName="${{ env.domain_name }}.",
            TargetDomainName=${{ steps.deploy-services.outputs.ALBDomainName }},
            HostedZoneId=${{ secrets.HOSTED_ZONE_ID }}
          tags: '[ { "Key": "Environment", "Value": "${{ env.env }}" }, { "Key": "ProjectId", "Value": "${{ env.project_id }}" }]'
