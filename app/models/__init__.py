from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey, Text, DateTime, Enum, Boolean, UniqueConstraint
from sqlalchemy.orm import relationship, declarative_base
import enum
from datetime import datetime

Base = declarative_base()

class UserRole(enum.Enum):
    admin = "admin"
    member = "member"

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    full_name = Column(String(255), nullable=False)
    organization_name = Column(String(255), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    organization_description = Column(Text, nullable=True)
    role = Column(Enum(UserRole), default=UserRole.member, nullable=False)

    connections = relationship('Connection', back_populates='user')
    messaging_connections = relationship('MessagingConnection', back_populates='user')
    settings = relationship('Settings', back_populates='user')

class Connection(Base):
    __tablename__ = 'connections'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    name = Column(String(255), nullable=False)
    database_type = Column(String(50), nullable=False)  # e.g., postgres, mysql, etc.
    database_platform = Column(String(50), nullable=False)  # e.g., AWS, GCP, Azure, etc.
    connection_params = Column(Text, nullable=False)  # JSON or text for connection parameters
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    status = Column(String(20), default='disconnected')  # 'connected', 'disconnected', or 'failed_on_connect'

    user = relationship('User', back_populates='connections')
    connection_tables = relationship('ConnectionTable', back_populates='connection')

    db_overview = Column(Text, nullable=True)


# class DBConnectionSchema(Base):
#     __tablename__ = 'db_connection_schemas'
#     id = Column(Integer, primary_key=True)
#     connection_id = Column(Integer, ForeignKey('connections.id'), nullable=False)
#     created_at = Column(DateTime, default=datetime.utcnow)

#     connection = relationship('Connection', back_populates='schemas')
#     tables = relationship('Table', back_populates='schema')


class ConnectionTable(Base):
    __tablename__ = 'connection_tables'
    id = Column(Integer, primary_key=True)
    connection_id = Column(Integer, ForeignKey('connections.id'), nullable=False)
    schema_name = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), default='inactive')  # 'active' or 'not active'
    sample_row = Column(Text, nullable=True)
    connection = relationship('Connection', back_populates='connection_tables')
    fields = relationship('Field', back_populates='connection_table')


class Field(Base):
    __tablename__ = 'fields'
    id = Column(Integer, primary_key=True)
    connection_table_id = Column(Integer, ForeignKey('connection_tables.id'), nullable=False)
    name = Column(String(255), nullable=False)
    data_type = Column(String(100))
    description = Column(Text)
    is_categorical = Column(Boolean, default=False)
    is_datetime = Column(Boolean, default=False)
    categorical_values = Column(Text, nullable=True)
    status = Column(String(20), default='active')  # 'active' or 'not active'
    connection_table = relationship('ConnectionTable', back_populates='fields')



class MessagingConnection(Base):
    __tablename__ = 'messaging_connections'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    team_id = Column(String(50), nullable=False)
    type = Column(String(50), nullable=False)  # e.g., slack, teams, etc.
    config = Column(Text)  # JSON or text config
    db_connection_id = Column(Integer, ForeignKey('connections.id', ondelete='CASCADE'), nullable=True)
    user = relationship('User', back_populates='messaging_connections')
    status = Column(String(20), default='disconnected')  # 'active' or 'not active'

    __table_args__ = (
        # Ensure only one db_connection per (user_id, type)
        UniqueConstraint('user_id', 'type', name='uq_user_type_messaging_connection'),
    )


class Settings(Base):
    __tablename__ = 'settings'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    key = Column(String(100), nullable=False)
    value = Column(Text)

    user = relationship('User', back_populates='settings')

class OTP(Base):
    __tablename__ = 'otps'
    id = Column(Integer, primary_key=True)
    email = Column(String(255), nullable=False)
    otp_code = Column(String(10), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_used = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

class PendingRegistration(Base):
    __tablename__ = 'pending_registrations'
    id = Column(Integer, primary_key=True)
    email = Column(String(255), unique=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    organization_name = Column(String(255), nullable=False)
    organization_description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_verified = Column(Boolean, default=False, nullable=False)
