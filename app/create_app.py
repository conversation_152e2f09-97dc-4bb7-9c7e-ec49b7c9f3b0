from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON>PBasic, HTTPBasicCredentials
from fastapi.responses import HTMLResponse
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
from secrets import compare_digest
import os
from .routes.routes import include_routers
from .middleware.auth import AuthMiddleware
import logging
from .core.redis import init_redis, close_redis
from .databse import test_db_connection, init_db, engine
import asyncio
import sys

# Configure the logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate required environment variables
def validate_environment():
    """Validate that all required environment variables are set"""
    # Only validate if environment variables are explicitly set
    # If not set, the config.py defaults will be used
    logger.info("Environment variables validation completed - using defaults from config.py if not set")
    
    # Log the current configuration for debugging
    from config import Config
    # logger.info(f"Database Host: {Config.DB_HOST}")
    # logger.info(f"Database Port: {Config.DB_PORT}")
    # logger.info(f"Database Name: {Config.DB_NAME}")
    # logger.info(f"Database User: {Config.DB_USER}")
    # logger.info(f"Redis URL: {Config.REDIS_URL}")

# Validate environment on module import
validate_environment()

security = HTTPBasic()

doc_user_name  = os.getenv("DOC_USERNAME")
doc_password = os.getenv("DOC_PASSWORD")

def get_doc_creds(credentials: HTTPBasicCredentials = Depends(security)) -> str:
    correct_username = compare_digest(credentials.username, doc_user_name)
    correct_password = compare_digest(credentials.password, doc_password)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

def create_app() -> FastAPI:
    app = FastAPI(
        version="0.1.0",
        title="Artisan API",
        description="API for Artisan services.",
        summary="Lifemind API",
        contact={
            "name": "Owusu",
            "email": "<EMAIL>",
        },
        docs_url=None,
        redoc_url=None,
        openapi_url=None
    )

    origins = ["*"]  # Allow all origins for development; adjust for production
    
    @app.middleware("http")
    async def log_middleware(request: Request, call_next):
        start_time = datetime.now()
        response = await call_next(request)
        process_time = datetime.now() - start_time

        log_dict = {
            "method": request.method,
            "url": str(request.url),
            "response_code": response.status_code,
            "process_time": process_time.total_seconds(),
        }

        logger.info(log_dict)
        return response

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    #app.add_middleware(middleware_class=AuthMiddleware)

    include_routers(app)  # ✅ Call the function to register routers

    @app.get("/docs", tags=['documentation'], include_in_schema=False, response_class=HTMLResponse)
    async def get_docs(username: str = Depends(get_doc_creds)) -> HTMLResponse:
        return get_swagger_ui_html(openapi_url='/openapi.json', title='docs')

    @app.get("/openapi.json", tags=['documentation'], include_in_schema=False)
    async def openapi(username: str = Depends(get_doc_creds)):
        return get_openapi(title='FastAPI', version='0.1.0', routes=app.routes)

    @app.get("/redoc", tags=['documentation'], include_in_schema=False, response_class=HTMLResponse)
    async def get_redoc(username: str = Depends(get_doc_creds)) -> HTMLResponse:
        return get_redoc_html(openapi_url='/openapi.json', title='docs')

    @app.get("/health", tags=['health'], include_in_schema=False)
    async def health_check():
        """Health check endpoint to verify Redis and Database connections"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "redis": "unknown",
                "database": "unknown"
            }
        }
        
        try:
            # Test Redis connection
            from .core.redis import redis_client
            if redis_client:
                await redis_client.ping()
                health_status["services"]["redis"] = "connected"
            else:
                health_status["services"]["redis"] = "error: client not initialized"
                health_status["status"] = "unhealthy"
        except Exception as e:
            health_status["services"]["redis"] = f"error: {str(e)}"
            health_status["status"] = "unhealthy"
        
        try:
            # Test Database connection
            if test_db_connection():
                health_status["services"]["database"] = "connected"
            else:
                health_status["services"]["database"] = "error: connection failed"
                health_status["status"] = "unhealthy"
        except Exception as e:
            health_status["services"]["database"] = f"error: {str(e)}"
            health_status["status"] = "unhealthy"
        
        return health_status

    @app.on_event("startup")
    async def startup_event():
        try:
            # Initialize Redis
            logger.info("TRYING TO CONNECT TO REDIS...")
            success = await asyncio.wait_for(init_redis(), timeout=5)
            logger.info(f"REDIS CONNECTION SUCCESS: {success}")
            if not success:
                logger.error("Failed to initialize Redis. Raising exception.")
                raise RuntimeError("Redis initialization returned False")
            
            # Initialize Database
            logger.info("TESTING DATABASE CONNECTION...")
            try:
                # Test database connection with timeout
                db_connected = await asyncio.wait_for(
                    asyncio.to_thread(test_db_connection), 
                    timeout=10
                )
                if not db_connected:
                    logger.error("Failed to connect to database. Raising exception.")
                    raise RuntimeError("Database connection failed")
                logger.info("DATABASE CONNECTION SUCCESS")
                
                # Initialize database tables
                logger.info("INITIALIZING DATABASE TABLES...")
                await asyncio.wait_for(
                    asyncio.to_thread(init_db), 
                    timeout=30
                )
                logger.info("DATABASE TABLES INITIALIZED SUCCESSFULLY")
                
            except asyncio.TimeoutError:
                logger.error("Database operation timed out during startup")
                raise RuntimeError("Database operation timed out during startup")
            except Exception as e:
                logger.error(f"Database initialization failed: {e}")
                raise RuntimeError(f"Database initialization failed: {e}")
            
        except asyncio.TimeoutError:
            logger.error("Redis connection timed out during startup")
            raise RuntimeError("Redis connection timed out during startup")
        except Exception as e:
            logger.error(f"Error during startup initialization: {e}")
            raise RuntimeError(f"Startup initialization failed: {e}")


    @app.on_event("shutdown")
    async def shutdown_event():
        """Close Redis and Database connections on shutdown"""
        try:
            # Close Redis connection
            await close_redis()
            logger.info("Redis connection closed successfully")
            
            # Close database connections
            engine.dispose()
            logger.info("Database connections closed successfully")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    return app