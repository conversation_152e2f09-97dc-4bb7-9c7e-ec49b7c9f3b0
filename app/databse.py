from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.declarative import declarative_base

from sqlalchemy.orm import sessionmaker, Session
import logging
import sys
from pathlib import Path

# Add project root to path for config import
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from app.models import Base  # Import Base from models instead of creating new one

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration from settings
DATABASE_URL = Config.DATABASE_URL
DB_HOST = Config.DB_HOST
DB_PORT = Config.DB_PORT
DB_USER = Config.DB_USER
DB_PASSWORD = Config.DB_PASSWORD
DB_NAME = Config.DB_NAME

# Create engine with connection pooling and proper configuration
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=300,    # Recycle connections every 5 minutes
    pool_size=10,        # Number of connections to maintain
    max_overflow=20,     # Additional connections that can be created
    echo=False           # Set to True for SQL debugging
)


# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models is now imported from app.models

def get_db() -> Session:
    """
    Dependency function to get database session.
    Use this in FastAPI endpoints and Dramatiq tasks.
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_session() -> Session:
    """
    Get a database session for use in Dramatiq tasks.
    Remember to close the session when done.
    """
    return SessionLocal()

def init_db():
    """Initialize the database by creating all tables."""
    try:
        logger.info("Initializing database...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

def test_db_connection():
    """Test database connection."""
    try:
        with engine.connect() as connection:
           

            connection.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

def clear_database():
    """Clear all data from all tables in the database."""
    try:
        logger.info("Clearing database...")
        
        # Get all table names
        with engine.connect() as connection:
            # Disable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            # Get all table names
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            # Clear each table
            for table in tables:
                connection.execute(text(f"DELETE FROM {table}"))
                logger.info(f"Cleared table: {table}")
            
            # Re-enable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            
            connection.commit()
            
        logger.info("Database cleared successfully")
        return True
    except Exception as e:
        logger.error(f"Error clearing database: {e}")
        return False

def clear_table(table_name: str):
    """Clear data from a specific table."""
    try:
        logger.info(f"Clearing table: {table_name}")
        
        with engine.connect() as connection:
            # Disable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            # Clear the specific table
            connection.execute(text(f"DELETE FROM {table_name}"))
            
            # Re-enable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            
            connection.commit()
            
        logger.info(f"Table {table_name} cleared successfully")
        return True
    except Exception as e:
        logger.error(f"Error clearing table {table_name}: {e}")
        return False

def clear_users_table():
    """Clear only the users table."""
    return clear_table("users")

def drop_all_tables():
    """Drop all tables in the database."""
    try:
        logger.info("Dropping all tables...")
        with engine.connect() as connection:
            # Disable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            # Get all table names
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            # Drop each table
            for table in tables:
                try:
                    connection.execute(text(f"DROP TABLE IF EXISTS `{table}`"))
                    logger.info(f"Dropped table: {table}")
                except Exception as drop_err:
                    logger.error(f"Error dropping table {table}: {drop_err}")
            
            # Re-enable foreign key checks for MySQL
            if 'mysql' in str(engine.url):
                connection.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            connection.commit()
        logger.info("All tables dropped successfully")
        return True
    except Exception as e:
        logger.error(f"Error dropping tables: {e}")
        return False

# Event listeners for connection management
@event.listens_for(engine, "connect")
def set_postgresql_settings(dbapi_connection, connection_record):
    """Set database-specific configurations on connect."""
    if 'mysql' in str(engine.url):
        # PostgreSQL specific settings can be added here
        logger.debug("PostgreSQL connection established")
        pass