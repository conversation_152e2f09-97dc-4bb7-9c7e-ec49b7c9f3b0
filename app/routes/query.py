from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import os
import json
import logging
import traceback
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from sql_copilot.services.ai.query import QueryHandler
from data.context import CONTEXT
#from config import DATABASE,training_examples



router = APIRouter(prefix="/api/public/v1/query", tags=["File accessibility API"])

class QueryRequest(BaseModel):
    question: str

class FeedbackRequest(BaseModel):
    question: str
    sql_query: str
    is_correct: bool
    feedback_text: Optional[str] = None

class QueryResponse(BaseModel):
    response: str
    file_name: Optional[str] = None
    has_file: bool = False
    query_code: str = Field(default="")
    error_detail: Optional[str] = None
    analytics_mode: bool = False
    plot_code: str = Field(default="")
    code_data: list = []
    question_interpretation:str=None



from sql_copilot.services.ai.query import QueryHandler
from config import moongo_sample_db, snowflake_sample_db

from sql_copilot.services.config.db_support import DB_TYPE_CONFIG

class QueryRequest2(BaseModel):
    question: str
    connection_params: dict
    provider: str

@router.post("/ask-ai-test-mongo", response_model=QueryResponse)
async def handle_query_mongo(request: QueryRequest2):  # Updated to use QueryRequest2
    try:
        conn_params = request.connection_params  # Fixed variable name
        query = request.question
        db_type = DB_TYPE_CONFIG[request.provider]
        handler = QueryHandler(database_structure=moongo_sample_db, provider=request.provider, connection_params=conn_params, query=query, db_type=db_type)  # Use provider from request
        response = await handler.process_query()
        print(f"Response from AI :{response}")
        return await handler.process_query()
    except Exception as e:
        logging.error(f"Error processing query: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/ask-ai-test-snowflake", response_model=QueryResponse)
async def handle_query_snowflake(request: QueryRequest2):  # Updated to use QueryRequest2
    try:
        conn_params = request.connection_params  # Fixed variable name
        query = request.question
        db_type = DB_TYPE_CONFIG[request.provider]
        handler = QueryHandler(database_structure=snowflake_sample_db, provider=request.provider, connection_params=conn_params, query=query, db_type=db_type)  # Use snowflake database structure
        response = await handler.process_query()
        print(f"Response from AI :{response}")
        return response
    except Exception as e:
        logging.error(f"Error processing query: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")