from fastapi import APIRouter, Depends, HTTPException, Request, status, Response
import requests
from config import Config
from sqlalchemy.orm import Session
from app.databse import get_db
from ..models import User
from app.core.security import hash_password, verify_password
from app.core.session import login_user, logout_user, get_current_user
from pydantic import BaseModel
from typing import Optional
from sql_copilot.services.email import send_email
from datetime import datetime, timedelta
from app.models import OTP, PendingRegistration
import random
from app.core.crypto_utils import encrypt_data
from fastapi.responses import RedirectResponse


router = APIRouter(
    tags=["User authentication API"]
)

class UserRegister(BaseModel):
    full_name: str
    email: str
    password: str
    organization_name: str
    organization_description: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class EmailRequest(BaseModel):
    email: str

class OTPVerifyRequest(BaseModel):
    email: str
    otp_code: str

@router.get("/slack/oauth/callback")
async def oauth_callback(request: Request, db: Session = Depends(get_db)):
    try:
        import json
        from app.models import MessagingConnection, User
        from app.core.crypto_utils import encrypt_data
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        if not code or not state:
            return RedirectResponse(url="/connections?slack=error&reason=missing_code_or_state", status_code=303)

        user = db.query(User).filter(User.id == state).first()
        if not user:
            return RedirectResponse(url="/connections?slack=error&reason=user_not_found", status_code=303)

        response = requests.post(
            "https://slack.com/api/oauth.v2.access",
            data={
                "client_id": Config.SLACK_CLIENT_ID,
                "client_secret": Config.SLACK_SECRET_ID,
                "code": code,
                "redirect_uri": Config.REDIRECT_URI
            }
        )
        data = response.json()
        if not data.get("ok"):
            reason = data.get("error", "oauth_failed")
            return RedirectResponse(url=f"/connections?slack=error&reason={reason}", status_code=303)

        access_token = data["access_token"]
        team_id = data["team"]["id"]
        team_name = data["team"]["name"]
        installer_user_id = data["authed_user"]["id"]

        config = {
            "team_id": team_id,
            "team_name": team_name,
            "installer_user_id": installer_user_id,
            "access_token": access_token
        }
        encrypted_config = encrypt_data(json.dumps(config))

        messaging_conn = MessagingConnection(
            user_id=user.id,
            team_id=team_id,
            type="slack",
            config=encrypted_config,
            status="connected"
        )
        db.add(messaging_conn)
        db.commit()

        return RedirectResponse(url="/connections?slack=connected", status_code=303)
    except Exception as e:
        import traceback
        print("Slack OAuth callback error:", traceback.format_exc())
        return RedirectResponse(url=f"/connections?slack=error&reason={str(e)}", status_code=303)


@router.post("/signup")
def register(user: UserRegister, db: Session = Depends(get_db)):
    """
    Register a new user. If a pending verification exists, delete it and continue.
    """
    # Check if email is already registered
    if db.query(User).filter(User.email == user.email).first():
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )

    # Check for pending verification and delete if exists
    pending_verification = db.query(PendingRegistration).filter(
        PendingRegistration.email == user.email,
        PendingRegistration.is_verified == False
    ).first()
    if pending_verification:
        db.delete(pending_verification)
        db.commit()

    # Hash password and store in PendingRegistration
    hashed_pw = hash_password(user.password)
    pending = PendingRegistration(
        full_name=user.full_name,
        email=user.email,
        hashed_password=hashed_pw,
        organization_name=user.organization_name,
        organization_description=user.organization_description
    )
    db.add(pending)
    db.commit()

    # Invalidate any previous OTPs for this email
    db.query(OTP).filter(
        OTP.email == user.email,
        OTP.is_used == False
    ).update({"is_used": True})

    # Generate/send OTP
    otp_code = generate_otp_code()
    expires_at = datetime.utcnow() + timedelta(minutes=10)
    otp = OTP(
        email=user.email,
        otp_code=otp_code,
        expires_at=expires_at,
        is_used=False
    )
    db.add(otp)
    db.commit()
    send_email(
        user.email,
        "Your OTP Code",
        f"Your OTP code is: {otp_code}"
    )
    return {"msg": "Signup pending OTP verification"}

@router.post("/login")
def login(user: UserLogin, request: Request, db: Session = Depends(get_db)):
    db_user = db.query(User).filter(User.email == user.email).first()
    if not db_user or not verify_password(user.password, db_user.hashed_password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    access_token = login_user(request, db_user)
    return {"msg": "Login successful", "access_token": access_token}

@router.post("/logout")
def logout(response: Response):
    logout_user()
    return {"msg": "Logout successful"}

# Password reset endpoint (stub)
@router.post("/reset-password")
def reset_password(email: str, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    # TODO: Implement password reset logic (email, token, etc)
    return {"msg": "Password reset link sent (stub)"}

# Helper to generate a 6-digit OTP
def generate_otp_code():
    return str(random.randint(100000, 999999))

@router.post("/send-otp")
def send_otp(payload: EmailRequest, db: Session = Depends(get_db)):
    email = payload.email
    # Only allow if pending registration exists and not verified
    pending = db.query(PendingRegistration).filter(PendingRegistration.email == email, PendingRegistration.is_verified == False).first()
    if not pending:
        raise HTTPException(status_code=404, detail="No pending registration for this email")
    db.query(OTP).filter(OTP.email == email, OTP.is_used == False).update({"is_used": True})
    otp_code = generate_otp_code()
    expires_at = datetime.utcnow() + timedelta(minutes=10)
    otp = OTP(email=email, otp_code=otp_code, expires_at=expires_at, is_used=False)
    db.add(otp)
    db.commit()
    send_email(email, "Your OTP Code", f"Your OTP code is: {otp_code}")
    return {"msg": "OTP sent"}

@router.post("/verify-otp")
def verify_otp(payload: OTPVerifyRequest, db: Session = Depends(get_db)):
    email = payload.email
    otp_code = payload.otp_code
    otp = db.query(OTP).filter(OTP.email == email, OTP.otp_code == otp_code, OTP.is_used == False).first()
    if not otp:
        raise HTTPException(status_code=400, detail="Invalid OTP code")
    if otp.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="OTP code expired")
    pending = db.query(PendingRegistration).filter(PendingRegistration.email == email, PendingRegistration.is_verified == False).first()
    if not pending:
        raise HTTPException(status_code=404, detail="No pending registration for this email")
    # Mark OTP as used and pending as verified
    otp.is_used = True
    pending.is_verified = True
    # Create user
    new_user = User(
        full_name=pending.full_name,
        email=pending.email,
        hashed_password=pending.hashed_password,
        organization_name=pending.organization_name,
        organization_description=pending.organization_description
    )
    db.add(new_user)
    db.commit()
    return {"msg": "OTP verified, user created"}

@router.post("/resend-otp")
def resend_otp(payload: EmailRequest, db: Session = Depends(get_db)):
    email = payload.email
    pending = db.query(PendingRegistration).filter(PendingRegistration.email == email, PendingRegistration.is_verified == False).first()
    if not pending:
        raise HTTPException(status_code=404, detail="No pending registration for this email")
    db.query(OTP).filter(OTP.email == email, OTP.is_used == False).update({"is_used": True})
    otp_code = generate_otp_code()
    expires_at = datetime.utcnow() + timedelta(minutes=10)
    otp = OTP(email=email, otp_code=otp_code, expires_at=expires_at, is_used=False)
    db.add(otp)
    db.commit()
    send_email(email, "Your OTP Code", f"Your OTP code is: {otp_code}")
    return {"msg": "OTP resent"}



    