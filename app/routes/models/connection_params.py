from typing import Optional
from pydantic import BaseModel

class SnowflakeConnectionParams(BaseModel):
    user: str
    password: str
    account: str
    database: str
    warehouse: str
    role: str
    schema: Optional[str] = None



class MongoDBConnectionParams(BaseModel):
    username: str
    password: str
    cluster_url: str
    database: str
    options: str = "retryWrites=true&w=majority"


class PostgresConnectionParams(BaseModel):
    host: str
    port: int 
    database: str
    user: str
    password: str
    schema: Optional[str] = None
    
# Add more connection parameter models here as needed



class MySQLConnectionParams(BaseModel):
    host: str
    port: int = 3306
    database: Optional[str] = None
    user: str
    password: str
    ssl_ca: Optional[str] = None

