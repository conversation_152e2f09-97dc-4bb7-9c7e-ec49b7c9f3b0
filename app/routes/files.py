from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import os

router = APIRouter(
    prefix="/files",
    tags=["File accessibility API"])

@router.get("/download/{file_name}")
async def download_file(file_name: str, background_tasks: BackgroundTasks):
    temp_dir = "temp_files"
    file_path = os.path.join(temp_dir, file_name)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    # Schedule the cleanup task to run after the response is sent
    #background_tasks.add_task(cleanup_file, file_path)
    
    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type="application/octet-stream"
    )
