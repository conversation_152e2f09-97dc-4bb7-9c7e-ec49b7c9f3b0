from fastapi import APIRouter, Depends, HTTPException, Query, Body, Form
from typing import List, Optional
from sqlalchemy.orm import Session
from app.databse import get_db
from app.models import Connection, ConnectionTable, Field, MessagingConnection
from app.core.session import get_current_user
from pydantic import BaseModel
from starlette.requests import Request
from starlette.responses import RedirectResponse

router = APIRouter(prefix="/api/v1", tags=["user connections"])

class UserConnectionResponse(BaseModel):
    id: int
    name: str
    database_type: str
    database_platform: str
    status: str

    class Config:
        from_attributes = True

class TableResponse(BaseModel):
    id: int
    name: str
    status: str
    description: str = ""
    sample_row: str = None

    class Config:
        from_attributes = True

class PaginatedTablesResponse(BaseModel):
    total: int
    page: int
    page_size: int
    tables: list[TableResponse]

class FieldResponse(BaseModel):
    id: int
    name: str
    data_type: str = ""
    description: str = ""
    is_categorical: bool = False
    is_datetime: bool = False
    categorical_values: Optional[str] = None
    status: str

    class Config:
        from_attributes = True

class UpdateTableStatusRequest(BaseModel):
    status: str

class EditTableRequest(BaseModel):
    name: str = None
    description: str = None

class EditFieldRequest(BaseModel):
    name: str = None
    data_type: str = None
    description: str = None
    is_categorical: bool = None
    is_datetime: bool = None
    categorical_values: str = None
    status: str = None

@router.get("/user-connections", response_model=List[UserConnectionResponse])
async def get_user_connections(user=Depends(get_current_user), db: Session = Depends(get_db)):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    connections = db.query(Connection).filter(Connection.user_id == user.id, Connection.status == 'connected').all()
    print(f"User connections: {connections}")
    return connections 

@router.get("/connections/{connection_id}/tables", response_model=PaginatedTablesResponse)
async def list_tables_for_connection(
    connection_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    # Ensure user owns the connection
    connection = db.query(Connection).filter(Connection.id == connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=404, detail="Connection not found")
    query = db.query(ConnectionTable).filter(ConnectionTable.connection_id == connection_id)
    total = query.count()
    tables = query.offset((page-1)*page_size).limit(page_size).all()
    table_responses = [TableResponse.from_orm(table) for table in tables]
    return PaginatedTablesResponse(
        total=total,
        page=page,
        page_size=page_size,
        tables=table_responses
    ) 

@router.get("/tables/{table_id}/fields", response_model=List[FieldResponse])
async def list_fields_for_table(
    table_id: int,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    table = db.query(ConnectionTable).filter(ConnectionTable.id == table_id).first()
    if not table:
        raise HTTPException(status_code=404, detail="Table not found")
    # Ensure user owns the connection
    connection = db.query(Connection).filter(Connection.id == table.connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=403, detail="Not authorized to view this table")
    fields = db.query(Field).filter(Field.connection_table_id == table_id).all()
    return fields 

@router.patch("/tables/{table_id}/status", response_model=TableResponse)
async def update_table_status(
    table_id: int,
    req: UpdateTableStatusRequest,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    table = db.query(ConnectionTable).filter(ConnectionTable.id == table_id).first()
    if not table:
        raise HTTPException(status_code=404, detail="Table not found")
    connection = db.query(Connection).filter(Connection.id == table.connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=403, detail="Not authorized to update this table")
    table.status = req.status
    db.commit()
    db.refresh(table)
    return table

@router.patch("/tables/{table_id}", response_model=TableResponse)
async def edit_table(
    table_id: int,
    req: EditTableRequest,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    table = db.query(ConnectionTable).filter(ConnectionTable.id == table_id).first()
    if not table:
        raise HTTPException(status_code=404, detail="Table not found")
    connection = db.query(Connection).filter(Connection.id == table.connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=403, detail="Not authorized to update this table")
    if req.name is not None:
        table.name = req.name
    if req.description is not None:
        table.description = req.description
    db.commit()
    db.refresh(table)
    return table

@router.patch("/fields/{field_id}", response_model=FieldResponse)
async def edit_field(
    field_id: int,
    req: EditFieldRequest,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    field = db.query(Field).filter(Field.id == field_id).first()
    if not field:
        raise HTTPException(status_code=404, detail="Field not found")
    table = db.query(ConnectionTable).filter(ConnectionTable.id == field.connection_table_id).first()
    connection = db.query(Connection).filter(Connection.id == table.connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=403, detail="Not authorized to update this field")
    for attr, value in req.dict(exclude_unset=True).items():
        setattr(field, attr, value)
    db.commit()
    db.refresh(field)
    return field 

@router.get("/connections/{connection_id}/debug")
async def debug_connection_tables(
    connection_id: int,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Debug endpoint to check tables for a connection"""
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    # Check if connection exists and belongs to user
    connection = db.query(Connection).filter(Connection.id == connection_id, Connection.user_id == user.id).first()
    if not connection:
        return {"error": "Connection not found or not owned by user", "connection_id": connection_id, "user_id": user.id}
    
    # Get all tables for this connection
    tables = db.query(ConnectionTable).filter(ConnectionTable.connection_id == connection_id).all()
    
    # Get table details
    table_details = []
    for table in tables:
        fields_count = db.query(Field).filter(Field.connection_table_id == table.id).count()
        table_details.append({
            "id": table.id,
            "name": table.name,
            "schema_name": table.schema_name,
            "status": table.status,
            "description": table.description,
            "fields_count": fields_count
        })
    
    return {
        "connection_id": connection_id,
        "connection_name": connection.name,
        "connection_platform": connection.database_platform,
        "connection_status": connection.status,
        "user_id": user.id,
        "tables_count": len(tables),
        "tables": table_details
    }

@router.post("/connections/{connection_id}/disconnect")
async def disconnect_connection(
    connection_id: int,
    user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    connection = db.query(Connection).filter(Connection.id == connection_id, Connection.user_id == user.id).first()
    if not connection:
        raise HTTPException(status_code=404, detail="Connection not found")
    # Delete all fields, tables, and schema structure for this connection
    tables = db.query(ConnectionTable).filter(ConnectionTable.connection_id == connection_id).all()
    for table in tables:
        db.query(Field).filter(Field.connection_table_id == table.id).delete()
    db.query(ConnectionTable).filter(ConnectionTable.connection_id == connection_id).delete()
    db.delete(connection)
    db.commit()
    return {"success": True, "detail": "Connection and all related data deleted."}


@router.post("/link-slack-db")
async def link_slack_db(request: Request, db_connection_id: str = Form(None)):
    user = get_current_user(request)
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    db = next(get_db())
    # Find the user's Slack messaging connection
    messaging_conn = db.query(MessagingConnection).filter_by(user_id=user.id, type="slack", status="connected").first()
    if not messaging_conn:
        raise HTTPException(status_code=400, detail="No Slack connection found. Please connect Slack first.")
    
    # Handle unlinking (empty value) or linking (valid ID)
    if not db_connection_id or db_connection_id.strip() == "":
        messaging_conn.db_connection_id = None
        message = "Data source unlinked successfully"
    else:
        try:
            connection_id = int(db_connection_id)
            # Verify the connection exists and belongs to the user
            connection = db.query(Connection).filter_by(id=connection_id, user_id=user.id).first()
            if not connection:
                raise HTTPException(status_code=400, detail="Invalid database connection selected.")
            messaging_conn.db_connection_id = connection_id
            message = f"Data source linked to {connection.name} successfully"
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid connection ID provided.")
    
    db.commit()
    return {"success": True, "message": message}



