from fastapi import FastAPI
from app.routes.files import router as file_router
from app.routes.auth import router as auth_router
from app.routes.integrations.slack import router as slack_integration_router
from app.routes.query import router as query_rouer
#from .feedback import router as feed_back_router
from app.routes.schema_generation import router as schema_generation_router
from app.routes.database_connections import router as database_connections_router
def include_routers(app: FastAPI):
    """Function to register all API routers."""
    app.include_router(auth_router, prefix="/api/auth")
    app.include_router(file_router)  # Registering file router
    app.include_router(slack_integration_router)  # Registering Slack integration router
    app.include_router(query_rouer) 
    app.include_router(schema_generation_router)
    app.include_router(database_connections_router)
    #app.include_router(feed_back_router) 
   