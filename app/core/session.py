from fastapi import Request, HTTPException
from typing import Optional
import jwt
from datetime import datetime, timedelta
from config import Config
from app.models import User
from sqlalchemy.orm import Session
from app.databse import get_db

# Secret key for JWT tokens (should be in config)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, Config.SECRET_KEY, algorithm=Config.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, Config.SECRET_KEY, algorithms=[Config.ALGORITHM])
        return payload
    except jwt.PyJWTError:
        return None

def get_current_user(request: Request) -> Optional[User]:
    """Get current user from request cookies or headers"""
    # Check for token in cookies first
    token = request.cookies.get("access_token")
    
    # If not in cookies, check Authorization header
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
    
    if not token:
        return None
    
    # Verify token
    payload = verify_token(token)
    if not payload:
        return None
    
    user_id = payload.get("sub")
    if not user_id:
        return None
    
    # Get user from database
    try:
        db = next(get_db())
        user = db.query(User).filter(User.id == user_id).first()
        return user
    except Exception:
        return None

def is_authenticated(request: Request) -> bool:
    """Check if user is authenticated"""
    return get_current_user(request) is not None

def login_user(request: Request, user: User) -> str:
    """Login user and return access token"""
    access_token_expires = timedelta(hours=24)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    return access_token

def logout_user(request: Request):
    """Logout user by clearing session"""
    # In a more sophisticated system, you might want to blacklist the token
    # For now, we'll just rely on the client to clear the cookie
    pass 