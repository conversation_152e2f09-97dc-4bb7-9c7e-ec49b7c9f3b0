"""
Utility functions for fetching active tables and fields for a connection.
"""

from sqlalchemy.orm import Session
from app.models import Connection, ConnectionTable, Field

def get_active_tables_with_fields_and_overview(db: Session, connection_id: int):
    """
    Return a dict with db_overview and all active tables (and their active fields)
    for a given connection_id.
    Only tables and fields with status 'active' are included.
    If any error occurs, return an empty dict.
    """
    try:
        connection = (
            db.query(Connection)
            .filter(Connection.id == connection_id)
            .first()
        )
        if not connection:
            return {}

        tables = (
            db.query(ConnectionTable)
            .filter(
                ConnectionTable.connection_id == connection_id,
                ConnectionTable.status == 'active'
            )
            .all()
        )

        result_tables = []
        for table in tables:
            fields = (
                db.query(Field)
                .filter(
                    Field.connection_table_id == table.id,
                    Field.status == 'active'
                )
                .all()
            )
            result_tables.append({
                "id": table.id,
                "table_name": table.name,
                "schema_name": table.schema_name,
                "status": table.status,
                "description": table.description,
                "sample_row": table.sample_row,
                "fields": [
                    {
                        "id": field.id,
                        "name": field.name,
                        "data_type": field.data_type,
                        "description": field.description,
                        "is_categorical": field.is_categorical,
                        "is_datetime": field.is_datetime,
                        "categorical_values": field.categorical_values,
                        "status": field.status,
                    }
                    for field in fields
                ],
            })

        return {
            "connection_id": connection.id,
            "provider": connection.database_platform,
            "schemas": connection.db_overview,
            "tables": result_tables,
            "connection_params": connection.connection_params,
            "db_type": connection.database_type
        }
    except Exception:
        return {}