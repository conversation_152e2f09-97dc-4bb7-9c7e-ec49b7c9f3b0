import redis.asyncio as redis
from config import Config
import logging
import asyncio

logger = logging.getLogger(__name__)

# Global Redis client
redis_client = None

async def init_redis():
    """Initialize Redis connection"""
    global redis_client
    try:
        if redis_client is None:
            logger.info(f"REDIS URL: {Config.REDIS_URL}")
            redis_client = redis.from_url(Config.REDIS_URL, decode_responses=True)

        # Test connection with timeout
        try:
            await asyncio.wait_for(redis_client.ping(), timeout=3)
            logger.info("Successfully connected to Redis")
            return True
        except asyncio.TimeoutError:
            logger.error("Redis ping timed out")
            redis_client = None
            return False

    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None
        return False

async def close_redis():
    """Close Redis connection"""
    global redis_client
    if redis_client:
        try:
            await redis_client.close()
            redis_client = None
            logger.info("Successfully closed Redis connection")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

def get_redis():
    """Get Redis client instance"""
    if redis_client is None:
        logger.error("Redis client not initialized")
        return None
    return redis_client 