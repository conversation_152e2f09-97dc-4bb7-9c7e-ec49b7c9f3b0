import redis
import os
from config import Config


def publish_schema_progress(request_id: str, message: str):
    import datetime
    import json
    
    r = redis.Redis.from_url(Config.REDIS_URL)
    channel = f"schema_progress:{request_id}"
    
    # Add timestamp to help with debugging and ensure fresh messages
    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
    
    # Try to parse as JSON, if it fails, wrap as simple message
    try:
        parsed_msg = json.loads(message)
        # If it's already a JSON object, add timestamp
        if isinstance(parsed_msg, dict):
            parsed_msg['timestamp'] = timestamp
            message_with_time = json.dumps(parsed_msg)
        else:
            message_with_time = f"[{timestamp}] {message}"
    except (json.JSONDecodeError, TypeError):
        # If not JSON, just add timestamp prefix
        message_with_time = f"[{timestamp}] {message}"
    
    r.publish(channel, message_with_time)

def clear_schema_progress_channel(request_id: str):
    """Clear any cached messages for a schema progress channel"""
    r = redis.Redis.from_url(Config.REDIS_URL)
    channel = f"schema_progress:{request_id}"
    # Remove any cached messages by deleting the key pattern
    # This helps ensure fresh start for new schema generation tasks
    try:
        # Use a pattern to find and delete any related keys
        keys = r.keys(f"*{channel}*")
        if keys:
            r.delete(*keys)
    except Exception as e:
        # Log but don't fail if clearing fails
        print(f"Warning: Could not clear Redis channel {channel}: {e}")
