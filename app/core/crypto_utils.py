"""
Utility functions for encrypting and decrypting sensitive data using Fernet.
"""

import os
from cryptography.fernet import Fernet
from config import Config

def get_fernet_key():
    """
    Retrieve the Fernet key from environment variable or generate a new one.
    """
    key = Config.FERNET_SECRET_KEY
    if not key:
        raise ValueError("FERNET_SECRET_KEY environment variable not set.")
    return key.encode()


def encrypt_data(data: str) -> str:
    """
    Encrypt a string using Fernet symmetric encryption.
    Args:
        data: The string to encrypt.
    Returns:
        The encrypted string (base64 encoded).
    """
    fernet = Fernet(get_fernet_key())
    return fernet.encrypt(data.encode()).decode()


def decrypt_data(token: str) -> str:
    """
    Decrypt a Fernet-encrypted string.
    Args:
        token: The encrypted string (base64 encoded).
    Returns:
        The decrypted string.
    """
    fernet = Fernet(get_fernet_key())
    return fernet.decrypt(token.encode()).decode() 