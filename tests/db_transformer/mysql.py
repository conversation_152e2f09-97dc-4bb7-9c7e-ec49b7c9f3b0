
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
import json
# Example usage
if __name__ == "__main__":
    # Snowflake example
   
    from config import TEST_MYSQL_PATH
    with open(TEST_MYSQL_PATH, "r") as f:

        mysql_data = json.load(f)["tables"]

    
    print("=== SUPPORTED PROVIDERS ===")
    for provider in SchemaTransformerFactory.get_supported_providers():
        print(f"- {provider.value}")
    
    print("\n=== MYSQL TRANSFORMATION ===")
    snowflake_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.MYSQL)
    snowflake_result = snowflake_transformer.transform_to_json_db(mysql_data)
    
    with open("mysql_transformed_data.json", "w") as f:
        json.dump(snowflake_result, f, indent=2)
    