
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
import json
# Example usage
if __name__ == "__main__":


    
    # Snowflake example
    snowflake_data = [
        {
            "schema_name": "TRANSFORMED",
            "table_name": "TRANSFORMED.MEDICI_BALANCES", 
            "description": "This table tracks financial balances for users in different currencies.",
            "fields": [
                {
                    "name": "id",
                    "data_type": "TEXT",
                    "is_categorical": False,
                    "is_datetime": False,
                    "description": "Unique identifier for each balance record."
                },
                {
                    "name": "book", 
                    "data_type": "TEXT",
                    "is_categorical": True,
                    "is_datetime": False,
                    "description": "Financial book identifier.",
                    "found_categorical_values": ["AfriexBook", "MainBook", "TestBook"]
                },
                {
                    "name": "currency",
                    "data_type": "TEXT",
                    "is_categorical": True,
                    "is_datetime": False,
                    "description": "Currency code.",
                    "found_categorical_values": ["USD", "EUR", "NGN"]
                }
            ]
        }
    ]
    
    # MongoDB example (your format)
    mongodb_data = {
        "collections": [
            {
                "database_name": "dev",
                "collection_name": "contacts",
                "description": "This collection stores information about individual contacts, including their unique identifiers and associated data.",
                "fields": [
                    {
                        "name": "_id",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "A unique identifier for each contact, automatically generated by MongoDB."
                    },
                    {
                        "name": "hash",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "A hashed value representing the contact's information for quick lookup and verification."
                    },
                    {
                        "name": "contacts",
                        "data_type": "array",
                        "is_categorical": False,
                        "description": "An array of objects containing detailed information about each contact, such as name, phone number, and email address."
                    }
                ],
                "sample_documents": "{\n  \"_id\": \"a1b2c3d4e5f67890abcdef12\",\n  \"hash\": \"f1e2d3c4b5a6978f0e1d2c3b4a5f6e7d8c9b0a1e2f3d4c5b6a7e8f9g0h1i2j3k\",\n  \"contacts\": [\n    \"1234567890abcdef12345678\",\n    \"abcdef1234567890abcdef12\",\n    \"fedcba0987654321fedcba09\"\n  ]\n}",
                "status": "active"
            },
            {
                "database_name": "dev",
                "collection_name": "feature-flags",
                "description": "This collection stores feature flags that control the availability of features in an application.",
                "fields": [
                    {
                        "name": "_id",
                        "data_type": "string",
                        "is_categorical": False,
                        "description": "Unique identifier for the feature flag."
                    },
                    {
                        "name": "flag_name",
                        "data_type": "string",
                        "is_categorical": True,
                        "description": "Name of the feature flag.",
                        "categorical_values": ["dark_mode", "new_ui", "beta_features"]
                    }
                ],
                "sample_documents": "{\n  \"_id\": \"flag123\",\n  \"flag_name\": \"dark_mode\",\n  \"enabled\": true\n}",
                "status": "active"
            },
            {
                "database_name": "dev",
                "collection_name": "waitlists",
                "description": "",
                "fields": [],
                "sample_documents": [],
                "status": "deactivated"
            }
        ]
    }
    
    print("=== SUPPORTED PROVIDERS ===")
    for provider in SchemaTransformerFactory.get_supported_providers():
        print(f"- {provider.value}")
    
    print("\n=== SNOWFLAKE TRANSFORMATION ===")
    snowflake_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.SNOWFLAKE)
    snowflake_result = snowflake_transformer.transform_to_json_db(snowflake_data)
    print(json.dumps(snowflake_result, indent=2))
    
    print("\n=== MONGODB TRANSFORMATION ===")
    mongodb_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.MONGODB)
    mongodb_result = mongodb_transformer.transform_to_json_db(mongodb_data)
    print(json.dumps(mongodb_result, indent=2))
    
    print("\n=== MONGODB SCHEMA OVERVIEW (Node 1) ===")
    for schema in mongodb_result["schemas"]:
        print(f"Database: {schema['schema_name']}")
        print(f"Total Collections: {schema['total_collections']}")
        print(f"Active Collections: {schema['active_collections']}")
        print("Collections Overview:")
        for collection in schema['collections_overview']:
            print(f"  - {collection['collection_name']} ({collection['status']})")
            print(f"    Description: {collection['description']}")
            print(f"    Fields: {collection['field_count']}")
            if collection['categorical_examples']:
                print("    Categorical Fields:")
                for cat in collection['categorical_examples']:
                    print(f"      • {cat['field_name']}: {', '.join(cat['sample_values'])}")
        print()
    
    print("=== MONGODB ORIGINAL DATA (Node 2) ===")
    for table in mongodb_result["tables"]:
        if table["status"] == "active":  # Only show active collections
            print(f"Collection: {table['collection_name']}")
            print(f"Database: {table['database_name']}")
            print(f"Description: {table['description']}")
            print(f"Fields: {len(table['fields'])}")
            print(f"Has Sample Documents: {bool(table['sample_documents'])}")
            print("---")






from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider


import json
# Example usage
if __name__ == "__main__":
   
    print("=== SUPPORTED PROVIDERS ===")
    for provider in SchemaTransformerFactory.get_supported_providers():
        print(f"- {provider.value}")
    
    print("\n=== Postgress TRANSFORMATION ===")
    from config import TEST_POSTGRES_PATH

    with open(TEST_POSTGRES_PATH, "r") as f:
        data = json.load(f)["tables"]
    snowflake_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.POSTGRES)
    snowflake_result = snowflake_transformer.transform_to_json_db(data)
    print(json.dumps(snowflake_result, indent=2))

    with open("postgres_transformed_data.json", "w") as f:
        json.dump(snowflake_result, f, indent=2)
    
   