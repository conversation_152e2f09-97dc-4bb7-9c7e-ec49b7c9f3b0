from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from pymongo import MongoClient
from langchain_core.prompts import ChatPromptTemplate
from sql_copilot.services.schema_generator.row_randomizer import generate_sample
from sql_copilot.services.schema_generator.schema_analyzer import BaseSchemaAnalyzer,SchemaAnalyzerRegistry,SchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mongodb import MongoDBSchemaAnalyzer
from config import Config
from bson import ObjectId
from test_keys import connection_params

from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from pymongo import MongoClient
from langchain_core.prompts import ChatPromptTemplate
from sql_copilot.services.schema_generator.row_randomizer import generate_sample
from sql_copilot.services.schema_generator.schema_analyzer import BaseSchemaAnalyzer,SchemaAnalyzerRegistry,SchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mongodb import MongoDBSchemaAnalyzer
from config import Config
from bson import ObjectId
from test_keys import connection_params

if __name__ == "__main__":
    
   
    analyzer = SchemaAnalyzer(
        backend="mongodb",
        openai_api_key=Config.OPENAI_API_KEY,
        connection_params=connection_params
    )

    res, report, error= analyzer.analyze_schema()
    analyzer.export_to_json(result_list=res,report=report)

