from typing import Dict, List, Any, Tuple, Optional
import time
import json
from decimal import Decimal
import datetime
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from sql_copilot.services.schema_generator.schema_analyzer import SchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mysql import MySQLSchemaAnalyzer
from config import Config

if __name__ == "__main__":
    # PostgreSQL connection parameters
    connection_params = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'sammy509'
    }
    # Initialize schema analyzer
    analyzer = SchemaAnalyzer(
        backend="mysql",
        openai_api_key=Config.OPENAI_API_KEY,
        connection_params=connection_params
    )

    # Test connection
    is_connected, error = analyzer.test_connection()
    if not is_connected:
        print(f"Connection test failed: {error}")
        exit(1)
    print("Connection test successful")

    # Add debug logging
    print("\nDebug: Starting schema analysis...")
    
    # Analyze schema
    # You can specify schemas to analyze, or it will analyze all schemas
    optional_params = {
        "databases": ["ecommerce", "finance", "analytics"]  # Optional: specify schemas to analyze
    }
    
    res, report, error = analyzer.analyze_schema(**optional_params)
    if error:
        print(f"Schema analysis failed: {error}")
        exit(1)

    # Debug: Print categorical fields for each table
    if res and len(res) > 0:
        print("\nDebug: Categorical Fields Analysis:")
        for table in res:
            print(f"\nTable: {table.get('table_name', 'Unknown')}")
            categorical_fields = [field for field in table.get("fields", []) if field.get("is_categorical", False)]
            
            if categorical_fields:
                print("Found categorical fields:")
                for field in categorical_fields:
                    print(f"\n  Field: {field['name']}")
                    print(f"  Data Type: {field['data_type']}")
                    print(f"  Description: {field.get('description', 'No description')}")
                    if "found_categorical_values" in field:
                        print(f"  Categorical Values ({len(field['found_categorical_values'])}):")
                        print(f"    {', '.join(str(val) for val in field['found_categorical_values'])}")
            else:
                print("  No categorical fields found in this table")

    # Export results
    analyzer.export_to_json(
        result_list=res,
        report=report,
        output_file="nysql_schema.json"
    )
    print("\nSchema analysis completed and exported successfully") 