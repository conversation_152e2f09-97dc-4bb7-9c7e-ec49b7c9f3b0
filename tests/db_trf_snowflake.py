
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
import json
# Example usage
if __name__ == "__main__":
    # Snowflake example
   
    from config import TEST_SNOQFLAKE_PATH
    with open(TEST_SNOQFLAKE_PATH, "r") as f:

        snowflake_data = json.load(f)["tables"]


    schemas = ["INGEST","TRANSFORMED","GROWTH_ACCOUNTING","FRESHWORKS"]
                
    snowflake_data = [table for table in snowflake_data if table["schema_name"] in schemas]
    
    print("=== SUPPORTED PROVIDERS ===")
    for provider in SchemaTransformerFactory.get_supported_providers():
        print(f"- {provider.value}")
    
    print("\n=== SNOWFLAKE TRANSFORMATION ===")
    snowflake_transformer = SchemaTransformerFactory.create_transformer(DatabaseProvider.SNOWFLAKE)
    snowflake_result = snowflake_transformer.transform_to_json_db(snowflake_data)
    
    with open("snowflake_result.json", "w") as f:
        json.dump(snowflake_result, f, indent=2)
    