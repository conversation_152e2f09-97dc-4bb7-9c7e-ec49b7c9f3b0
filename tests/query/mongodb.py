from sql_copilot.services.ai.query import QueryHandler
from dotenv import load_dotenv
import os
import json
from test_keys import connection_params_snowflake,connection_params



if __name__ == "__main__":

    CONTEXT ={
        "INFO": "Supported Countries CODES and Their Currencies CODES according to OUR DATABASE",
        "data": {
        "Nigeria (NG)": "NGN (Nigerian Naira)",
        "United Kingdom (GB)": "GBP (British Pound)",
        "United States (US)": "USD (United States Dollar)",
        "Canada (CA)": "CAD (Canadian Dollar)",
        "Kenya (KE)": "KES (Kenyan Shilling)",
        "Ghana (GH)": "GHS (Ghanaian Cedi)",
        "Uganda (UG)": "UGX (Ugandan Shilling)",
        "Cameroon (CM)": "XAF (Central African CFA Franc)",
        "Ethiopia (ET)": "ETB (Ethiopian Birr)",
        "Haiti (HT)": "HTG (Haitian Gourde)",
        "Austria (AT)": "EUR (Euro)",
        "Belgium (BE)": "EUR (Euro)",
        "Croatia (HR)": "EUR (Euro)",
        "Cyprus (CY)": "EUR (Euro)",
        "Estonia (EE)": "EUR (Euro)",
        "Finland (FI)": "EUR (Euro)",
        "France (FR)": "EUR (Euro)",
        "Germany (DE)": "EUR (Euro)",
        "Greece (GR)": "EUR (Euro)",
        "Ireland (IE)": "EUR (Euro)",
        "Italy (IT)": "EUR (Euro)",
        "Latvia (LV)": "EUR (Euro)",
        "Lithuania (LT)": "EUR (Euro)",
        "Luxembourg (LU)": "EUR (Euro)",
        "Malta (MT)": "EUR (Euro)",
        "Netherlands (NL)": "EUR (Euro)",
        "Portugal (PT)": "EUR (Euro)",
        "Slovakia (SK)": "EUR (Euro)",
        "Slovenia (SI)": "EUR (Euro)",
        "Spain (ES)": "EUR (Euro)",
        "Romania (RO)": "EUR (Euro)",
        "Bulgaria (BG)": "EUR (Euro)",
        "Czech Republic (CZ)": "EUR (Euro)",
        "Denmark (DK)": "EUR (Euro)",
        "Hungary (HU)": "EUR (Euro)",
        "Poland (PL)": "EUR (Euro)",
        "Sweden (SE)": "EUR (Euro)",
        "Norway (NO)": "EUR (Euro)",
        "Ukraine (UA)": "EUR (Euro)",
        "Australia (AU)": "AUD (Australian Dollar)",
        "Russia (RU)": "RUB (Russian Ruble)",
        "Burkina Faso (BF)": "XOF (West African CFA Franc)",
        "Mali (ML)": "XOF (West African CFA Franc)",
        "Senegal (SN)": "XOF (West African CFA Franc)",
        "Togo (TG)": "XOF (West African CFA Franc)",
        "Guinea (GN)": "GNF (Guinean Franc)",
        "Benin (BJ)": "XOF (West African CFA Franc)",
        "Ivory Coast (CI)": "XOF (West African CFA Franc)",
        "Egypt (EG)": "EGP (Egyptian Pound)",
        "Mexico (MX)": "MXN (Mexican Peso)",
        "China (CN)": "CNY (Chinese Yuan)",
        "India (IN)": "INR (Indian Rupee)",
        "Philippines (PH)": "PHP (Philippine Peso)",
        "Brazil (BR)": "BRL (Brazilian Real)",
        "Uruguay (UY)": "UYU (Uruguayan Peso)",
        "Pakistan (PK)": "PKR (Pakistani Rupee)",
        "Rwanda (RW)": "RWF (Rwandan Franc)",
        "South Africa (ZA)": "ZAR (South African Rand)"
        }
    }


    queries = [
    "how many users haveompleted their kyc from Ngeria and Canada, breakdown by country please , not futrher ",
    "give me the list of users who did at least 10 transaction in the year 2024 but haven't done any transaction in the year 2025, np further filter that is all",
    "share a list of users in the uk and europe who signed up before april 1 2025 name country and emails and id, no further questioning",
    "provide a list of users who have referred someone name and emaol and number of people refered, no further questioning",
    "How many Canadian users have global bank accounts? no further questioning",
    "list of all users making transactions to the account with the following information as account numbder \"**********\". Include transaction amount and dates. and name and email, no further questioning",
    "I want a list of users who signed up in April 2025, have not made any transfers, and are from the UK, US, Canada, Nigeria, South Africa, Kenya, Ethiopia, and all countries in Europe. I need their first names, user IDs, email addresses, and countries, no further questioning",
    "I'll like the distinct values of destinationAsset for successfultransactions that went through the BANK_ACCOUNT channel and proccessor was DLOCAL for the year 2025, no further questioning",
    "check for number of payment methods that don't have country or currency values set,no further questioning",
    "Who has the highest number of referrals, no further questioning give me your answer starigint away",
    "How many people have refereed  at least 2 people so far,username and count of refereals,no further questioning"
]

    provider = "mongodb"
    query = queries[-1]
    company_name = "afriex"
    
    db_type = "no_sql"
    connection_id = "test"

    handler = QueryHandler(provider=provider,connection_params=connection_params,query=query,db_type=db_type,connection_id=connection_id)
    # Assuming process_query is an async function, we need to run it in an event loop
    import asyncio
    async def main():
        response = await handler.process_query()
        print(response)
        return response

    asyncio.run(main())
