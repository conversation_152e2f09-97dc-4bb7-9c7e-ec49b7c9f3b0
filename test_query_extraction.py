#!/usr/bin/env python3
"""
Test script to verify that the query generator properly extracts and returns the generated query.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import <PERSON>qlCopilot
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_query_extraction():
    """Test that the query generator returns the actual generated query"""
    
    # Sample database structure (simplified for testing)
    database_structure = {
        "tables": [
            {
                "table_name": "users",
                "fields": [
                    {"field_name": "id", "data_type": "INTEGER", "is_primary_key": True},
                    {"field_name": "name", "data_type": "VARCHAR(255)"},
                    {"field_name": "email", "data_type": "VARCHAR(255)"},
                    {"field_name": "created_at", "data_type": "TIMESTAMP"}
                ]
            },
            {
                "table_name": "orders",
                "fields": [
                    {"field_name": "id", "data_type": "INTEGER", "is_primary_key": True},
                    {"field_name": "user_id", "data_type": "INTEGER"},
                    {"field_name": "amount", "data_type": "DECIMAL(10,2)"},
                    {"field_name": "status", "data_type": "VARCHAR(50)"},
                    {"field_name": "created_at", "data_type": "TIMESTAMP"}
                ]
            }
        ]
    }
    
    # Sample connection parameters (for testing - these won't be used for actual DB connection)
    connection_params = {
        "host": "localhost",
        "database": "test_db",
        "user": "test_user",
        "password": "test_password"
    }
    
    # Initialize SqlCopilot
    copilot = SqlCopilot(
        provider="postgres",  # or your preferred provider
        connection_params=connection_params,
        database_structure=database_structure,
        query="How many users do we have in total?",
        connection_id="test_connection_123",
        company_name="Test Company"
    )
    
    print("Testing query extraction...")
    print(f"Input query: {copilot.query}")
    print("-" * 50)
    
    try:
        # Invoke the workflow
        result = copilot.invoke()
        
        print("Workflow completed successfully!")
        print("-" * 50)
        
        # Check if we got a generated query
        if result.get("generated_query"):
            print("✅ SUCCESS: Generated query extracted successfully!")
            print(f"Generated Query: {result['generated_query']}")
        else:
            print("❌ ISSUE: No generated query found in result")
            print("Available keys in result:", list(result.keys()))
        
        # Print execution results if available
        if result.get("execution_results"):
            print(f"Execution Results: {result['execution_results']}")
        
        # Print any errors
        if result.get("execution_error"):
            print(f"Execution Error: {result['execution_error']}")
            
        return result
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_query_extraction()
