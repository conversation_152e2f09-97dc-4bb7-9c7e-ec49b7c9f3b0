#!/usr/bin/env python3
"""
Database initialization script
Creates all tables defined in the models
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.databse import init_db, test_db_connection
from app.models import Base
from sqlalchemy import create_engine
from config import Config

def main():
    """Initialize the database"""
    print("Testing database connection...")
    
    if not test_db_connection():
        print("❌ Database connection failed!")
        print("Please check your database configuration in config.py")
        sys.exit(1)
    
    print("✅ Database connection successful!")
    
    print("Creating database tables...")
    try:
        init_db()
        print("✅ Database tables created successfully!")
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 