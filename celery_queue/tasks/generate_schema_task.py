from utils import logger
from sql_copilot.services.schema_generator.schema_analyzer import SchemaAnalyzer
from config import Config
from sql_copilot.services.schema_generator.backend.snowflake import SnowflakeSchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mongodb import MongoDBSchemaAnalyzer
from sql_copilot.services.schema_generator.backend.postgres import PostgreSQLSchemaAnalyzer
from sql_copilot.services.schema_generator.backend.mysql import MySQLSchemaAnalyzer
from sql_copilot.services.db_structure.transformer import SchemaTransformerFactory, DatabaseProvider
from sql_copilot.services.db_structure.transformer import (
    SchemaTransformerFactory, 
    DatabaseProvider,
    PostgresSchemaTransformer,
    MongoDBSchemaTransformer,
     MysqlSchemaTransformer
)
from app.core.redis_pubsub import publish_schema_progress
import json
from sqlalchemy import Text
import json as pyjson
from app.core.crypto_utils import encrypt_data, decrypt_data

import traceback
from ..celery_app import celery_app
from app.databse import get_db_session
from app.models import User, Connection, ConnectionTable, Field



# You might want to use Redis or a database to store task status
task_status = {}  # This is just for demonstration. Use a proper database in production.

@celery_app.task(bind=True, max_retries=3, name="celery_queue.tasks.generate_schema_task")
def generate_schema_task(self, request_id: str, user_id: int, backend: str, connection_params: dict, optional_params: dict = None):
    """
    Background task for processing schema generation
    
    Args:
        request_id: Unique identifier for the request
        user_id: ID of the user who owns the connection
        backend: Database backend name
        connection_params: Database connection parameters
        optional_params: Optional parameters for schema analysis (schemas, max_workers, etc.)
    """
    try:
        logger.info("Entering generate_schema_task...")
        logger.info(f"Task called with request_id: {request_id}, backend: {backend}")
        logger.info("Starting schema generation...")
        logger.info("Task execution confirmed.")
        
        # Clear any previous messages in the Redis channel for this request_id
        from app.core.redis_pubsub import clear_schema_progress_channel
        clear_schema_progress_channel(request_id)
        
        publish_schema_progress(request_id, "Task started. Initializing schema generation...")
        
        # Update status to IN_PROGRESS
        task_status[request_id] = {
            "status": "IN_PROGRESS",
            "message": "Schema generation in progress"
        }
        
        logger.info("Initializing SchemaAnalyzer...")
        publish_schema_progress(request_id, "Initializing SchemaAnalyzer...")
        # Initialize the analyzer
       
        analyzer = SchemaAnalyzer(
                backend=backend,
                connection_params=connection_params,
                openai_api_key=Config.OPENAI_API_KEY,
                progress_callback=lambda msg: publish_schema_progress(request_id, msg)
            )
        logger.info("SchemaAnalyzer initialized.")
        publish_schema_progress(request_id, "SchemaAnalyzer initialized. Starting analysis...")

        # Analyze the schema with optional parameters
        results, report, error = analyzer.analyze_schema(**(optional_params or {}))
        #save resulst as json
        import json
        with open(f"{request_id}_raw_schema.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        publish_schema_progress(request_id, "Schema analysis complete. Transforming results...")
        #analyzer.export_to_json(result_list=results,report=report)
        # Update status based on results

        if error:
            task_status[request_id] = {
                "status": "ERROR_OCCURRED",
                "message": f"Schema generation failed: {error}",
                "error_detail": error
            }
            publish_schema_progress(request_id, f"Error: {error}")

        data = {
            "report": report,
            "tables": results
        }
        #get provider from backend
        if backend == "snowflake":
            provider = DatabaseProvider.SNOWFLAKE
        elif backend == "mongodb":
            provider = DatabaseProvider.MONGODB
        elif backend == "postgres":
            provider = DatabaseProvider.POSTGRES
        elif backend == "mysql":
            provider = DatabaseProvider.MYSQL
        
        publish_schema_progress(request_id, "Transforming schema to JSON DB format...")
        
        # For MongoDB, wrap results in collections key format that transformer expects
        if backend == "mongodb":
            formatted_results = {"collections": results}
        else:
            formatted_results = results
            
        
        data_transformed = SchemaTransformerFactory.create_transformer(provider).transform_to_json_db(formatted_results)
        db_overview = data_transformed.get("schemas")
        
        # Save the transformed data to a JSON file
        with open(f"{request_id}_transformed_schema.json", "w", encoding="utf-8") as f:
            import json
            json.dump(data_transformed, f, ensure_ascii=False, indent=2)

        publish_schema_progress(request_id, "Transformation complete. Saving results...")
        
        # --- DB Update Logic ---
        db = get_db_session()
        try:
            with db.begin():
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    raise Exception(f"User with id {user_id} not found")
                organization_name = user.organization_name
                connection_name = f"{backend}_{organization_name}"
                # Determine database_type and platform
                if backend in ["postgres", "mysql", "snowflake"]:
                    database_type = "sql"
                elif backend == "mongodb":
                    database_type = "no_sql"
                else:
                    database_type = "unknown"
                database_platform = backend
                # Create Connection
                encrypted_params = encrypt_data(json.dumps(connection_params))
                # Check for existing connection
                existing_connection = db.query(Connection).filter_by(
                    user_id=user_id,
                    name=connection_name,
                    database_platform=database_platform
                ).first()

                if existing_connection:
                    connection = existing_connection
                else:
                    connection = Connection(
                        user_id=user_id,
                        name=connection_name,
                        database_type=database_type,
                        database_platform=database_platform,
                        connection_params=encrypted_params,
                        db_overview=json.dumps(db_overview),
                        status="connected"
                    )
                    db.add(connection)
                    db.flush()  # Assigns connection.id without committing
                # Add tables
                for table in results:
                    schema_name = table.get("schema_name") or table.get("schema") or table.get("database_name")
                    table_name = table.get("table_name") or table.get("name") or table.get("collection_name")
                    description = table.get("description", "")
                    # Handle different sample data keys for different backends
                    if backend == "mongodb":
                        sample_data = table.get("sample_documents")
                    else:
                        sample_data = table.get("sample_rows")
                    sample_row_json = pyjson.dumps(sample_data) if sample_data is not None else None
                    conn_table = ConnectionTable(
                        connection_id=connection.id,
                        schema_name=schema_name or "default",
                        name=table_name or "unknown",
                        description=description,
                        status="inactive",
                        sample_row=sample_row_json
                    )
                    db.add(conn_table)
                    db.flush()  # get conn_table.id
                    # Add fields for this table
                    for field in table.get("fields", []):
                        field_obj = Field(
                            connection_table_id=conn_table.id,
                            name=field.get("name", "unknown"),
                            data_type=field.get("data_type", ""),
                            description=field.get("description", ""),
                            is_categorical=field.get("is_categorical", False),
                            is_datetime=field.get("is_datetime", False),
                            categorical_values=pyjson.dumps(field.get("categorical_values", [])) if field.get("categorical_values") is not None else None,
                            status="active"
                        )
                        db.add(field_obj)
        except Exception as db_exc:
            logger.error(f"DB update failed: {db_exc}")
            publish_schema_progress(request_id, f"Error: DB update failed: {db_exc}")
        finally:
            db.close()
        # --- End DB Update Logic ---

        task_status[request_id] = {
            "status": "COMPLETED",
            "message": "Schema generation completed successfully",
            "data": {"report": report,
                    "tables": results},
            "data_transformed": data_transformed
        }

        #save data transform as json
        SchemaTransformerFactory.create_transformer(provider).save_to_file(data_transformed, filename=f"{request_id}_schema.json")
        
        publish_schema_progress(request_id, "Schema generation completed successfully.")
        # Send final report as JSON
        final_report_msg = json.dumps({
            "type": "final_report",
            "report": report
        })
        publish_schema_progress(request_id, final_report_msg)
        logger.info(f"Completed schema generation for request_id: {request_id}")
        return task_status[request_id]
        
    except Exception as exc:
        error_msg = f"Schema generation failed: {str(exc)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        publish_schema_progress(request_id, f"Error: {error_msg}")
        
        # Update status to ERROR
        task_status[request_id] = {
            "status": "ERROR_OCCURRED",
            "message": f"Schema generation failed: {str(exc)}",
            "error_detail": error_msg
        }