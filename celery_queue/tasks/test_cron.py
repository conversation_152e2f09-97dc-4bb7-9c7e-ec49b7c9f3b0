from celery import shared_task
import logging

logger = logging.getLogger(__name__)
from ..celery_app import celery_app
@celery_app.task(bind=True, max_retries=3)
def test_cron(self, query_params):
    """
    Test task for for cronjob
    """
    try:
        logger.info(f"Processing SQL query: {query_params}")
        # Simulate some processing
        import time
        time.sleep(2)  # Simulate processing time
        result = "This is a test result"
        return {"status": "completed", "result": result}
    except Exception as exc:
        logger.error(f"SQL Processing failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=2 ** self.request.retries)