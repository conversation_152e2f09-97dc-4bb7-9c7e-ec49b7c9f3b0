import os
from celery import Celery
from config import Config
from celery_queue.celery_beat import BEAT_SCHEDULE
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# RabbitMQ configuration
env_data = os.environ  # Assuming env_data is sourced from environment variables
RABBITMQ_URL = Config.RABBITMQ_URL
#BROKER_URL = 'amqp://' + RABBITMQ_URL
BROKER_URL = Config.REDIS_URL
BACKEND_URL = Config.REDIS_URL

logger.info(f"Initializing Celery with:")
logger.info(f"Broker URL: {BROKER_URL}")
logger.info(f"Backend URL: {BACKEND_URL}")

# Create Celery application
celery_app = Celery(
    'sql_copilot_tasks',
    broker=BROKER_URL,
    backend=BACKEND_URL,
)

# Configure Celery
celery_app.conf.update(
    task_default_queue='sql_copilot_queue',
    task_track_started=True,
    task_time_limit=600,  # 10 minutes max task time
    task_soft_time_limit=500,  # Soft timeout with 5 minutes warning
    worker_concurrency=4,
    broker_connection_retry_on_startup=True,
    result_expires=3600,  # Result expires in 1 hour
    beat_schedule=BEAT_SCHEDULE
)


# This is where you need to register your tasks
celery_app.autodiscover_tasks(['celery_queue.tasks'])
logger.info("Celery app initialized successfully")

# # Optional: Register tasks manually if autodiscover doesn't work
# from celery_queue.tasks import generate_schema_task

# Command to run this Celery app
# celery -A celery_queue.celery_app.celery_app worker --loglevel=info --pool=solo
